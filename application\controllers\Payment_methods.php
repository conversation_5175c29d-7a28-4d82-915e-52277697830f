<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Payment_methods extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Payment_methods_model');
        $this->load->library('form_validation');
    }

    public function index()
    {


        $data = array(
            'payment_methods_data' => $this->Payment_methods_model->get_all(),

        );
        $config['active_menu'] = "server_config";
        $config['current_link'] = "method";
        $this->load->view('header',$config);
        $this->load->view('payment_methods/payment_methods_list', $data);
        $this->load->view('footer');

    }

    public function read($id) 
    {
        $row = $this->Payment_methods_model->get_by_id($id);
        if ($row) {
            $data = array(
		'payment_method_id' => $row->payment_method_id,
		'name' => $row->name,
		'stamp' => $row->stamp,
	    );
            $this->load->view('payment_methods/payment_methods_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('payment_methods'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('payment_methods/create_action'),
	    'payment_method_id' => set_value('payment_method_id'),
	    'name' => set_value('name'),
	    'stamp' => set_value('stamp'),
	);
        $config['active_menu'] = "server_config";
        $config['current_link'] = "method";
        $this->load->view('header',$config);
        $this->load->view('payment_methods/payment_methods_form', $data);
        $this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'name' => $this->input->post('name',TRUE)

	    );

            $this->Payment_methods_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('payment_methods'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Payment_methods_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('payment_methods/update_action'),
		'payment_method_id' => set_value('payment_method_id', $row->payment_method_id),
		'name' => set_value('name', $row->name),
		'stamp' => set_value('stamp', $row->stamp),
	    );
            $this->load->view('payment_methods/payment_methods_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('payment_methods'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('payment_method_id', TRUE));
        } else {
            $data = array(
		'name' => $this->input->post('name',TRUE),

	    );

            $this->Payment_methods_model->update($this->input->post('payment_method_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('payment_methods'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Payment_methods_model->get_by_id($id);

        if ($row) {
            $this->Payment_methods_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('payment_methods'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('payment_methods'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('name', 'name', 'trim|required');


	$this->form_validation->set_rules('payment_method_id', 'payment_method_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}
