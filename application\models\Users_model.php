<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Users_model extends CI_Model
{

    public $table = 'users';
    public $id = 'user_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }
public function auth($email,$pass){
    	$this->db->select("*")
			->from($this->table)
			->where('email',$email)
			->where('password',$pass);
    	return $this->db->get()->row();

}
    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->select("*")
			->from($this->table)
			->join('company','company.company_id=users.company');
        return $this->db->get()->result();
    }
    function get_all_by_id($id) {
        $this->db->order_by($this->id, $this->order);
        $this->db->select("*")
			->from($this->table)
			->join('company','company.company_id=users.company');
        $this->db->where('company',$id);
        return $this->db->get()->result();
                            }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('user_id', $q);
	$this->db->or_like('full_name', $q);
	$this->db->or_like('email', $q);
	$this->db->or_like('password', $q);
	$this->db->or_like('company', $q);
	$this->db->or_like('profile_photo', $q);
	$this->db->or_like('user_role', $q);
	$this->db->or_like('date_added', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('user_id', $q);
	$this->db->or_like('full_name', $q);
	$this->db->or_like('email', $q);
	$this->db->or_like('password', $q);
	$this->db->or_like('company', $q);
	$this->db->or_like('profile_photo', $q);
	$this->db->or_like('user_role', $q);
	$this->db->or_like('date_added', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Users_model.php */
/* Location: ./application/models/Users_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-15 06:58:39 */
/* http://harviacode.com */
