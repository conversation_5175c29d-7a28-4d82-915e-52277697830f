"use strict";!function(t){t.fn.circliful=function(e,o){var r=t.extend({foregroundColor:"#3498DB",backgroundColor:"#ccc",pointColor:"none",fillColor:"none",foregroundBorderWidth:15,backgroundBorderWidth:15,pointSize:28.5,fontColor:"#aaa",percent:75,animation:1,animationStep:5,icon:"none",iconSize:"30",iconColor:"#ccc",iconPosition:"top",target:0,start:0,showPercent:1,percentageTextSize:22,textAdditionalCss:"",targetPercent:0,targetTextSize:17,targetColor:"#2980B9",text:null,textStyle:null,textColor:"#666",multiPercentage:0,percentages:null},e);return this.each(function(){var e,o,n,i=t(this),c=r.percent,l=83,a=100,s=110,d=100,x=r.backgroundBorderWidth;"bottom"==r.iconPosition?(l=124,s=95):"left"==r.iconPosition?(a=80,l=110,d=117):"middle"==r.iconPosition?1==r.multiPercentage?(console.log("object"==typeof r.percentages),"object"==typeof r.percentages?x=30:(l=110,o='<g stroke="'+("none"!=r.backgroundColor?r.backgroundColor:"#ccc")+'" ><line x1="133" y1="50" x2="140" y2="40" stroke-width="2"  /></g>',o+='<g stroke="'+("none"!=r.backgroundColor?r.backgroundColor:"#ccc")+'" ><line x1="140" y1="40" x2="200" y2="40" stroke-width="2"  /></g>',d=228,s=47)):(l=110,o='<g stroke="'+("none"!=r.backgroundColor?r.backgroundColor:"#ccc")+'" ><line x1="133" y1="50" x2="140" y2="40" stroke-width="2"  /></g>',o+='<g stroke="'+("none"!=r.backgroundColor?r.backgroundColor:"#ccc")+'" ><line x1="140" y1="40" x2="200" y2="40" stroke-width="2"  /></g>',d=175,s=35):"right"==r.iconPosition&&(a=120,l=110,d=80),r.targetPercent>0&&(s=95,o='<g stroke="'+("none"!=r.backgroundColor?r.backgroundColor:"#ccc")+'" ><line x1="75" y1="101" x2="125" y2="101" stroke-width="1"  /></g>',o+='<text text-anchor="middle" x="'+d+'" y="120" style="font-size: '+r.targetTextSize+'px;" fill="'+r.targetColor+'">'+r.targetPercent+"%</text>",o+='<circle cx="100" cy="100" r="69" fill="none" stroke="'+r.backgroundColor+'" stroke-width="3" stroke-dasharray="450" transform="rotate(-90,100,100)" />',o+='<circle cx="100" cy="100" r="69" fill="none" stroke="'+r.targetColor+'" stroke-width="3" stroke-dasharray="'+3.6*r.targetPercent+', 20000" transform="rotate(-90,100,100)" />'),null!=r.text&&0==r.multiPercentage?o+='<text text-anchor="middle" x="100" y="125" style="'+r.textStyle+'" fill="'+r.textColor+'">'+r.text+"</text>":null!=r.text&&1==r.multiPercentage&&(o+='<text text-anchor="middle" x="228" y="65" style="'+r.textStyle+'" fill="'+r.textColor+'">'+r.text+"</text>"),"none"!=r.icon&&(n='<text text-anchor="middle" x="'+a+'" y="'+l+'" class="icon" style="font-size: '+r.iconSize+'px" fill="'+r.iconColor+'">&#x'+r.icon+"</text>"),i.addClass("svg-container").append(t('<svg xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 194 186" class="circliful">'+o+'<circle cx="100" cy="100" r="57" class="border" fill="'+r.fillColor+'" stroke="'+r.backgroundColor+'" stroke-width="'+x+'" stroke-dasharray="360" transform="rotate(-90,100,100)" /><circle class="circle" cx="100" cy="100" r="57" class="border" fill="none" stroke="'+r.foregroundColor+'" stroke-width="'+r.foregroundBorderWidth+'" stroke-dasharray="0,20000" transform="rotate(-90,100,100)" /><circle cx="100" cy="100" r="'+r.pointSize+'" fill="'+r.pointColor+'" />'+n+'<text class="timer" text-anchor="middle" x="'+d+'" y="'+s+'" style="font-size: '+r.percentageTextSize+"px; "+e+";"+r.textAdditionalCss+'" fill="'+r.fontColor+'">0%</text>'));var g=i.find(".circle"),k=i.find(".timer"),f=30,u=0,y=r.animationStep,h=0,C=0,p=0;if(r.start>0&&r.target>0&&(c=r.start/(r.target/100),p=r.target/100),1==r.animation)var m=window.setInterval(function(){u>=3.6*c?(window.clearInterval(m),h=1):(u+=y,C+=p),u/3.6>=c&&1==h&&(u=3.6*c),C>r.target&&1==h&&(C=r.target),g.attr("stroke-dasharray",u+", 20000"),1==r.showPercent?k.text(parseInt(u/360*100)+"%"):k.text(C)}.bind(g),f);else g.attr("stroke-dasharray",3.6*c+", 20000"),1==r.showPercent?k.text(c+"%"):k.text(r.target)})}}(jQuery);
