<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - EmailHost-Plus Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --cpanel-orange: #ff6c2c;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .badge-pending { background: #f59e0b; }
        .badge-approved { background: #10b981; }
        .badge-spam { background: #ef4444; }
        .badge-trash { background: #6b7280; }
        
        .comment-content {
            max-width: 300px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .comment-meta {
            font-size: 0.875rem;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-blog me-2"></i>Blog Admin
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin'); ?>">
                            <i class="fas fa-list me-2"></i>All Posts
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/create'); ?>">
                            <i class="fas fa-plus me-2"></i>Add New Post
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/categories'); ?>">
                            <i class="fas fa-folder me-2"></i>Categories
                        </a>
                        <a class="nav-link active" href="<?php echo base_url('admin/blog_admin/comments'); ?>">
                            <i class="fas fa-comments me-2"></i>Comments
                        </a>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                        <a class="nav-link" href="<?php echo base_url('blog'); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Blog
                        </a>
                        <a class="nav-link" href="<?php echo base_url(); ?>">
                            <i class="fas fa-home me-2"></i>Back to Site
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><?php echo $page_title; ?></h2>
                        <a href="<?php echo base_url('admin/blog_admin'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Posts
                        </a>
                    </div>

                    <!-- Flash Messages -->
                    <?php if ($this->session->flashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('success'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Comments Stats -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-warning">
                                        <?php 
                                        $pending = array_filter($comments, function($c) { return $c->status === 'pending'; });
                                        echo count($pending);
                                        ?>
                                    </h3>
                                    <p class="mb-0">Pending</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-success">
                                        <?php 
                                        $approved = array_filter($comments, function($c) { return $c->status === 'approved'; });
                                        echo count($approved);
                                        ?>
                                    </h3>
                                    <p class="mb-0">Approved</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-danger">
                                        <?php 
                                        $spam = array_filter($comments, function($c) { return $c->status === 'spam'; });
                                        echo count($spam);
                                        ?>
                                    </h3>
                                    <p class="mb-0">Spam</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h3 class="text-primary"><?php echo count($comments); ?></h3>
                                    <p class="mb-0">Total</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Comments Table -->
                    <div class="card">
                        <div class="card-body">
                            <?php if (!empty($comments)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Author</th>
                                                <th>Comment</th>
                                                <th>Post</th>
                                                <th>Status</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($comments as $comment): ?>
                                                <tr class="<?php echo $comment->status === 'pending' ? 'table-warning' : ''; ?>">
                                                    <td>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($comment->author_name); ?></strong>
                                                            <div class="comment-meta">
                                                                <i class="fas fa-envelope me-1"></i>
                                                                <?php echo htmlspecialchars($comment->author_email); ?>
                                                            </div>
                                                            <?php if ($comment->author_website): ?>
                                                                <div class="comment-meta">
                                                                    <i class="fas fa-globe me-1"></i>
                                                                    <a href="<?php echo htmlspecialchars($comment->author_website); ?>" target="_blank">
                                                                        Website
                                                                    </a>
                                                                </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="comment-content" title="<?php echo htmlspecialchars($comment->content); ?>">
                                                            <?php echo htmlspecialchars($comment->content); ?>
                                                        </div>
                                                        <?php if ($comment->parent_id): ?>
                                                            <small class="text-muted">
                                                                <i class="fas fa-reply me-1"></i>Reply to comment
                                                            </small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <a href="<?php echo base_url('blog/post/' . $comment->post_slug); ?>" target="_blank">
                                                            <?php echo htmlspecialchars(word_limiter($comment->post_title, 5)); ?>
                                                        </a>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-<?php echo $comment->status; ?>">
                                                            <?php echo ucfirst($comment->status); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo date('M j, Y', strtotime($comment->created_at)); ?><br>
                                                            <span class="text-muted"><?php echo date('g:i A', strtotime($comment->created_at)); ?></span>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <?php if ($comment->status === 'pending'): ?>
                                                                <button type="button" class="btn btn-outline-success" 
                                                                        onclick="approveComment(<?php echo $comment->id; ?>)" title="Approve">
                                                                    <i class="fas fa-check"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            
                                                            <?php if ($comment->status !== 'spam'): ?>
                                                                <button type="button" class="btn btn-outline-warning" 
                                                                        onclick="markSpam(<?php echo $comment->id; ?>)" title="Mark as Spam">
                                                                    <i class="fas fa-exclamation-triangle"></i>
                                                                </button>
                                                            <?php endif; ?>
                                                            
                                                            <button type="button" class="btn btn-outline-primary" 
                                                                    onclick="viewComment(<?php echo $comment->id; ?>)" title="View Full Comment">
                                                                <i class="fas fa-eye"></i>
                                                            </button>
                                                            
                                                            <button type="button" class="btn btn-outline-danger" 
                                                                    onclick="deleteComment(<?php echo $comment->id; ?>)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-comments fa-3x text-muted mb-3"></i>
                                    <h5>No comments yet</h5>
                                    <p class="text-muted">Comments will appear here when visitors start engaging with your blog posts.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- View Comment Modal -->
    <div class="modal fade" id="viewCommentModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">View Comment</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="commentModalBody">
                    <!-- Comment details will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function approveComment(commentId) {
            if (confirm('Approve this comment?')) {
                window.location.href = '<?php echo base_url("admin/blog_admin/approve_comment/"); ?>' + commentId;
            }
        }

        function markSpam(commentId) {
            if (confirm('Mark this comment as spam?')) {
                window.location.href = '<?php echo base_url("admin/blog_admin/mark_spam/"); ?>' + commentId;
            }
        }

        function deleteComment(commentId) {
            if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
                window.location.href = '<?php echo base_url("admin/blog_admin/delete_comment/"); ?>' + commentId;
            }
        }

        function viewComment(commentId) {
            // Find the comment data from the table
            const comments = <?php echo json_encode($comments); ?>;
            const comment = comments.find(c => c.id == commentId);
            
            if (comment) {
                const modalBody = document.getElementById('commentModalBody');
                modalBody.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Author Information</h6>
                            <p><strong>Name:</strong> ${comment.author_name}</p>
                            <p><strong>Email:</strong> ${comment.author_email}</p>
                            ${comment.author_website ? `<p><strong>Website:</strong> <a href="${comment.author_website}" target="_blank">${comment.author_website}</a></p>` : ''}
                            <p><strong>IP Address:</strong> ${comment.ip_address || 'N/A'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Comment Details</h6>
                            <p><strong>Post:</strong> ${comment.post_title}</p>
                            <p><strong>Status:</strong> <span class="badge badge-${comment.status}">${comment.status.charAt(0).toUpperCase() + comment.status.slice(1)}</span></p>
                            <p><strong>Date:</strong> ${new Date(comment.created_at).toLocaleString()}</p>
                            ${comment.parent_id ? '<p><strong>Type:</strong> Reply</p>' : ''}
                        </div>
                    </div>
                    <hr>
                    <h6>Comment Content</h6>
                    <div class="bg-light p-3 rounded">
                        ${comment.content.replace(/\n/g, '<br>')}
                    </div>
                `;
                
                const modal = new bootstrap.Modal(document.getElementById('viewCommentModal'));
                modal.show();
            }
        }
    </script>
</body>
</html>
