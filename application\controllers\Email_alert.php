<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Email_alert extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Product_life_model');
        $this->load->model('Company_model');
        $this->load->library('form_validation');
    }
function index(){
    $config['active_menu'] = "server_config";
    $config['current_link'] = "alert";
    $this->load->view('header',$config);
    $this->load->view('email_options');
    $this->load->view('footer');
}
public function expired(){
    $data = array(
        'product_life_data' => $this->Product_life_model->get_all(),
    );

    $config['active_menu'] = "server_config";
    $config['current_link'] = "alert";
    $this->load->view('header',$config);
    $this->load->view('expired', $data);
    $this->load->view('footer');
}
public function broadcast(){
    $data = array(
        'company' => $this->Company_model->get_all(),
    );

    $config['active_menu'] = "server_config";
    $config['current_link'] = "alert";
    $this->load->view('header',$config);
    $this->load->view('broadcast', $data);
    $this->load->view('footer');
}

    public function notify_selected(){

        $settings = get_by_id('cpanel_settings','id','1');
        $config = array('protocol' => $settings->protocal,
            'smtp_host' => $settings->email_host,
            'smtp_port' => $settings->email_port,
            'smtp_user' => $settings->email_user,
            'smtp_pass' => $settings->email_pass,

        );

        $this->load->library('email',$config);


        $message = $this->input->post('message');
        $subject = $this->input->post('subject');
if(!empty($_POST["emails"])) {
    foreach ($_POST['emails'] as $key) {


        $re = get_by_id('company', 'company_email', $key);

        $fn = $re->company_email;

        $output = "";
        $output .= '
         <html>
         <head>
         <title>' . $settings->company_name . '</title>
         <style>
         body {
  background: whitesmoke;
  text-align: center;
}

.card {
 text-align: center;
   margin: auto;
  width: 50%;
  
  padding: 10px;
  
  background: #E7E9EB;
  border-radius: 15px;
 
  height: 500px;

}

.card-1 {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.card-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

.card-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}


</style>
         
         </head>
         <body >
         <center><img src="' . base_url('uploads/') . $settings->logo . '" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
         <br>
          <center><a href="' . base_url() . '">' . $settings->company_name . '</a></center>
            <div class="card card-5" style="justify-items: center; align-items: center;">
            <p style="font-weight: bolder; font-size: 30px;">Hello: ' . $re->company_name . '</p>
            
            <p style="font-weight: bolder; font-size: 15px;">' . $message . '</p>
           
            
         </div>
         
         <center>Powered by:<a href="' . base_url() . '">' . $settings->company_name . '</a></center>
         </body>
         </html>
         ';


        $this->email->set_newline("\r\n");
        $this->email->from($settings->email_user, $settings->company_name);
        $this->email->to($key);
        $this->email->subject($subject);
        $this->email->message($output);
        $this->email->set_mailtype('html');
        $this->email->send();



    }
    $this->toaster->success('Bulk email process done');
      redirect($_SERVER['HTTP_REFERER']);
}
    }
    public function notify_all(){

        $settings = get_by_id('cpanel_settings','id','1');
        $config = array('protocol' => $settings->protocal,
            'smtp_host' => $settings->email_host,
            'smtp_port' => $settings->email_port,
            'smtp_user' => $settings->email_user,
            'smtp_pass' => $settings->email_pass,

        );

        $this->load->library('email',$config);
      $c =  $this->Company_model->get_all();

        $message = $this->input->post('message');
        $subject = $this->input->post('subject');
if(!empty($c)) {
    foreach ($c as $key) {


        $re = get_by_id('company', 'company_email', $key->company_email);

        $fn = $re->company_email;

        $output = "";
        $output .= '
         <html>
         <head>
         <title>' . $settings->company_name . '</title>
         <style>
         body {
  background: whitesmoke;
  text-align: center;
}

.card {
 text-align: center;
   margin: auto;
  width: 50%;
  
  padding: 10px;
  
  background: #E7E9EB;
  border-radius: 15px;
 
  height: 500px;

}

.card-1 {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.card-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

.card-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}


</style>
         
         </head>
         <body >
         <center><img src="' . base_url('uploads/') . $settings->logo . '" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
         <br>
          <center><a href="' . base_url() . '">' . $settings->company_name . '</a></center>
            <div class="card card-5" style="justify-items: center; align-items: center;">
            <p style="font-weight: bolder; font-size: 30px;">Dear, ' . $re->company_name . '</p>
            
            <p style="font-weight: bolder; font-size: 15px;">' . $message . '</p>
           
            
         </div>
         
         <center>Powered by:<a href="' . base_url() . '">' . $settings->company_name . '</a></center>
         </body>
         </html>
         ';


        $this->email->set_newline("\r\n");
        $this->email->from($settings->email_user, $settings->company_name);
        $this->email->to($key->company_email);
        $this->email->subject($subject);
        $this->email->message($output);
        $this->email->set_mailtype('html');
        $this->email->send();



    }
    $this->toaster->success('Bulk email process done');
      redirect($_SERVER['HTTP_REFERER']);
}
    }
    public function bulk_expired(){

        $settings = get_by_id('cpanel_settings','id','1');
        $config = array('protocol' => $settings->protocal,
            'smtp_host' => $settings->email_host,
            'smtp_port' => $settings->email_port,
            'smtp_user' => $settings->email_user,
            'smtp_pass' => $settings->email_pass,

        );

        $this->load->library('email',$config);
      $c =  $this->Product_life_model->get_all();


        $subject = $this->input->post('Product Expiry');
if(!empty($c)) {
    foreach ($c as $key) {


        $re = get_by_id('company', 'company_id', $key->product_owner);
        $dateOne = new DateTime($key->expire_date);
        $dateTwo = new DateTime(date('Y-m-d'));

        $diff = $dateTwo->diff($dateOne)->format("%a");

        if ($key->expire_date < date('Y-m-d')) {
            $fn = $re->company_email;
            $message = "Your ".$key->product_type. "  (".$key->product_name. ") has expired on ".$key->expire_date." Please prepare to make payment if you dont make payment within 15 days, else your product will be suspended";

            $output = "";
            $output .= '
         <html>
         <head>
         <title>' . $settings->company_name . '</title>
         <style>
         body {
  background: whitesmoke;
  text-align: center;
}

.card {
 text-align: center;
   margin: auto;
  width: 50%;
  
  padding: 10px;
  
  background: #E7E9EB;
  border-radius: 15px;
 
  height: 500px;

}

.card-1 {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.card-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

.card-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}


</style>
         
         </head>
         <body >
         <center><img src="' . base_url('uploads/') . $settings->logo . '" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
         <br>
          <center><a href="' . base_url() . '">' . $settings->company_name . '</a></center>
            <div class="card card-5" style="justify-items: center; align-items: center;">
            <p style="font-weight: bolder; font-size: 30px;">Dear, ' . $re->company_name . '</p>
            
            <p style="font-weight: bolder; font-size: 15px;">' . $message . '</p>
           
            
         </div>
         
         <center>Powered by:<a href="' . base_url() . '">' . $settings->company_name . '</a></center>
         </body>
         </html>
         ';


            $this->email->set_newline("\r\n");
            $this->email->from($settings->email_user, $settings->company_name);
            $this->email->to($re->company_email);
            $this->email->subject('Product Expiry');
            $this->email->message($output);
            $this->email->set_mailtype('html');
            $this->email->send();


        }elseif($diff < 31 && $diff > 0){
            $message = "Your ".$key->product_type. "  (".$key->product_name. ") is about to expire on ".$key->expire_date." Please prepare to make payment if you dont make payment within 15 days your product will be suspended";
            $output = "";
            $output .= '
         <html>
         <head>
         <title>' . $settings->company_name . '</title>
         <style>
         body {
  background: whitesmoke;
  text-align: center;
}

.card {
 text-align: center;
   margin: auto;
  width: 50%;
  
  padding: 10px;
  
  background: #E7E9EB;
  border-radius: 15px;
 
  height: 500px;

}

.card-1 {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.card-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

.card-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}


</style>
         
         </head>
         <body >
         <center><img src="' . base_url('uploads/') . $settings->logo . '" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
         <br>
          <center><a href="' . base_url() . '">' . $settings->company_name . '</a></center>
            <div class="card card-5" style="justify-items: center; align-items: center;">
            <p style="font-weight: bolder; font-size: 30px;">Dear, ' . $re->company_name . '</p>
            
            <p style="font-weight: bolder; font-size: 15px;">' . $message . '</p>
           
            
         </div>
         
         <center>Powered by:<a href="' . base_url() . '">' . $settings->company_name . '</a></center>
         </body>
         </html>
         ';




            $this->email->set_newline("\r\n");
            $this->email->from($settings->email_user, $settings->company_name);
            $this->email->to($re->company_email);
            $this->email->subject('Product Expiry');
            $this->email->message($output);
            $this->email->set_mailtype('html');
            $this->email->send();

        }
    }
    $this->toaster->success('Bulk email process done');
      redirect($_SERVER['HTTP_REFERER']);
}
    }

}


?>

