<!-- Blog Sidebar -->
<div class="blog-sidebar">
    
    <!-- Search Widget -->
    <div class="sidebar-widget">
        <h5 class="widget-title">Search</h5>
        <form action="<?php echo base_url('blog/search'); ?>" method="get">
            <div class="input-group">
                <input type="text" name="q" class="form-control" placeholder="Search posts..." value="<?php echo isset($search_query) ? $search_query : ''; ?>">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i>
                </button>
            </div>
        </form>
    </div>

    <!-- Categories Widget -->
    <?php if (!empty($categories)): ?>
    <div class="sidebar-widget">
        <h5 class="widget-title">Categories</h5>
        <ul class="category-list">
            <?php foreach ($categories as $category): ?>
                <li>
                    <a href="<?php echo base_url('blog/category/' . $category->slug); ?>">
                        <i class="fas fa-folder me-2"></i>
                        <?php echo htmlspecialchars($category->name); ?>
                    </a>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <!-- Recent Posts Widget -->
    <?php if (!empty($recent_posts)): ?>
    <div class="sidebar-widget">
        <h5 class="widget-title">Recent Posts</h5>
        <div class="recent-posts">
            <?php foreach ($recent_posts as $recent_post): ?>
                <div class="recent-post-item">
                    <?php if ($recent_post->featured_image): ?>
                        <div class="recent-post-image">
                            <img src="<?php echo base_url('uploads/blog/thumbs/' . $recent_post->featured_image); ?>" alt="<?php echo htmlspecialchars($recent_post->title); ?>">
                        </div>
                    <?php endif; ?>
                    <div class="recent-post-content">
                        <h6>
                            <a href="<?php echo base_url('blog/post/' . $recent_post->slug); ?>">
                                <?php echo htmlspecialchars($recent_post->title); ?>
                            </a>
                        </h6>
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            <?php echo date('M j, Y', strtotime($recent_post->published_at)); ?>
                        </small>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Popular Tags Widget -->
    <?php if (!empty($popular_tags)): ?>
    <div class="sidebar-widget">
        <h5 class="widget-title">Popular Tags</h5>
        <div class="tag-cloud">
            <?php foreach ($popular_tags as $tag): ?>
                <a href="<?php echo base_url('blog/tag/' . $tag->slug); ?>" class="tag-item">
                    <?php echo htmlspecialchars($tag->name); ?>
                    <?php if (isset($tag->post_count)): ?>
                        <span class="tag-count"><?php echo $tag->post_count; ?></span>
                    <?php endif; ?>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Newsletter Widget -->
    <div class="sidebar-widget newsletter-widget">
        <h5 class="widget-title">Newsletter</h5>
        <p>Subscribe to get our latest blog posts in your inbox.</p>
        <form class="newsletter-form">
            <div class="mb-3">
                <input type="email" class="form-control" placeholder="Your email address" required>
            </div>
            <button type="submit" class="btn btn-primary w-100">
                <i class="fas fa-paper-plane me-2"></i>Subscribe
            </button>
        </form>
    </div>

    <!-- Contact Widget -->
    <div class="sidebar-widget contact-widget">
        <h5 class="widget-title">Need Help?</h5>
        <p>Have questions about email hosting? Our team is here to help!</p>
        <div class="contact-info">
            <div class="contact-item">
                <i class="fas fa-envelope text-primary me-2"></i>
                <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
            <div class="contact-item">
                <i class="fas fa-phone text-primary me-2"></i>
                <a href="tel:+265992535274">+265 992 535 274</a>
            </div>
        </div>
        <a href="<?php echo base_url('contact'); ?>" class="btn btn-outline-primary btn-sm mt-3">
            <i class="fas fa-comments me-1"></i>Contact Us
        </a>
    </div>

</div>

<style>
/* Sidebar Styles */
.blog-sidebar {
    position: sticky;
    top: 2rem;
}

.sidebar-widget {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    border: 1px solid var(--border-light);
}

.widget-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    position: relative;
    padding-bottom: 0.5rem;
}

.widget-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 3px;
    background: var(--primary-color);
    border-radius: 2px;
}

/* Category List */
.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-list li {
    margin-bottom: 0.75rem;
}

.category-list a {
    color: var(--text-light);
    text-decoration: none;
    transition: color 0.3s ease;
    display: flex;
    align-items: center;
}

.category-list a:hover {
    color: var(--primary-color);
}

/* Recent Posts */
.recent-post-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-light);
}

.recent-post-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.recent-post-image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 0.5rem;
    overflow: hidden;
}

.recent-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.recent-post-content h6 {
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.recent-post-content a {
    color: var(--text-dark);
    text-decoration: none;
}

.recent-post-content a:hover {
    color: var(--primary-color);
}

/* Tag Cloud */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-item {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    background: var(--bg-tertiary);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: var(--primary-color);
    color: white;
}

.tag-count {
    background: rgba(255,255,255,0.2);
    padding: 0.125rem 0.5rem;
    border-radius: 1rem;
    font-size: 0.75rem;
}

/* Newsletter Widget */
.newsletter-widget {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
}

.newsletter-widget .widget-title {
    color: white;
}

.newsletter-widget .widget-title::after {
    background: rgba(255,255,255,0.3);
}

.newsletter-widget p {
    color: rgba(255,255,255,0.9);
}

/* Contact Widget */
.contact-widget {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-secondary) 100%);
}

.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.contact-item a {
    color: var(--text-dark);
    text-decoration: none;
}

.contact-item a:hover {
    color: var(--primary-color);
}

/* Responsive */
@media (max-width: 991px) {
    .blog-sidebar {
        position: static;
        margin-top: 3rem;
    }
}
</style>
