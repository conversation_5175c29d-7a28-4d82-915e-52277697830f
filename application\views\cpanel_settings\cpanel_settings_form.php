<!-- Content wrapper start -->
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card">

                <div class="card-body">
        <form action="<?php echo $action; ?>" method="post" >
            <h4>Server Settings</h4>
            <hr>
            <div class="row">
	    <div class="form-group col-lg-6">
            <label for="varchar">Cpanel User <?php echo form_error('cpanel_user') ?></label>
            <input type="text" class="form-control" name="cpanel_user" id="cpanel_user" placeholder="Cpanel User" value="<?php echo $cpanel_user; ?>" />
        </div>
	    <div class="form-group col-lg-6">
            <label for="varchar">Cpanel Root Path <?php echo form_error('cpanel_root_path') ?></label>
            <input type="text" class="form-control" name="cpanel_root_path" id="cpanel_root_path" placeholder="Cpanel Root Path" value="<?php echo $cpanel_root_path; ?>" />
        </div>
            </div>
            <h4>Company Branding</h4>
            <hr>
            <div class="row">

              <div class="form-group col-6">
                  <br>
                  <label for="id_front" id="pp" class="custom-file-upload"> Upload logo (1640 x 856) </label>
                  <input type="file"  onchange="uploadfile('id_front')"   id="id_front"  />
                  <input type="text" id="id_front1"  name="logo" value="<?php echo $logo; ?>" hidden required>
                  <div id="preview_data">
                      <img src="<?php echo base_url('uploads/').$logo?>" alt="" height="100" width="100">
                  </div>
              </div>

            <div class="form-group col-lg-6">
            <label for="varchar">Company name <?php echo form_error('company_name') ?></label>
            <input type="text" class="form-control" name="company_name" id="company_name" placeholder="Company Name" value="<?php echo $company_name; ?>" />
          </div>
            </div>
            <div class="row">

                <div class="form-group col-lg-4">
                    <label for="varchar">Company Phone <?php echo form_error('company_phone') ?></label>
                    <input type="text" class="form-control" name="company_phone" id="company_phone"  required placeholder="Company Phone Number" value="<?php echo $company_phone; ?>" />
                </div>
                <div class="form-group col-lg-4">
                    <label for="varchar">Company Email  Address<?php echo form_error('company_email') ?></label>
                    <input type="text" class="form-control" name="company_email" id="company_email"  required placeholder="Company Email Address" value="<?php echo $company_email; ?>" />
                </div>

            <div class="form-group col-lg-4">
            <label for="varchar">Currency <?php echo form_error('currency') ?></label>
            <input type="text" class="form-control" name="currency" id="currency" placeholder="Currency" value="<?php echo $currency; ?>" />
          </div>
            </div>
            <div class="row">
          <div class="form-group col-lg-6">
            <label for="varchar">Login page heading <?php echo form_error('login_page_heading') ?></label>
            <input type="text" class="form-control" name="login_page_heading" id="login_page_heading" placeholder="Login page heading" value="<?php echo $login_page_heading; ?>" />
          </div>
            <div class="form-group col-lg-6">
            <label for="varchar">Login page sub text <?php echo form_error('login_page_subtextt') ?></label>
            <input type="text" class="form-control" name="login_page_subtextt" id="login_page_subtextt" placeholder="Login page sub text" value="<?php echo $login_page_subtextt; ?>" />
          </div>
            </div>
            <hr>
            <h4>Cpanel Authentication details</h4>
            <div class="row">
          <div class="form-group col-lg-4">
            <label for="varchar">Server ip </label>
            <input type="text" class="form-control" name="hosting_ip" id="hosting_ip" placeholder="Hosting server IP" value="<?php echo $hosting_ip; ?>" />
          </div>
            <div class="form-group col-lg-4">
            <label for="varchar">Hosting username </label>
            <input type="text" class="form-control" name="username" id="username" placeholder="Hosting username" value="<?php echo $username; ?>" />
          </div>
                <div class="form-group col-lg-4">
            <label for="varchar">Hosting password </label>
            <input type="password" class="form-control" name="password" id="password" placeholder="Hosting password" value="<?php echo $password; ?>" />
          </div>
            </div>
            <hr>
            <h4>Email Settings</h4>
            <div class="row">
                <div class="form-group col-lg-4">
                    <label for="varchar">Email sending protocol </label>
                    <input type="text" class="form-control" name="protocal" id="password" placeholder="Protocoal" value="<?php echo $protocal; ?>" />
                </div>
                <div class="form-group col-lg-4">
                    <label for="varchar">Email host </label>
                    <input type="text" class="form-control" name="email_host" id="email_host" placeholder="Email host" value="<?php echo $email_host; ?>" />
                </div>
                <div class="form-group col-lg-4">
                    <label for="varchar">Email host port </label>
                    <input type="text" class="form-control" name="email_port" id="email_port" placeholder="Email host port" value="<?php echo $email_port; ?>" />
                </div>
            </div>
            <div class="row">
                <div class="form-group col-lg-6">
                    <label for="varchar">Email user </label>
                    <input type="text" class="form-control" name="email_user" id="email_user" placeholder="Email user" value="<?php echo $email_user; ?>" />
                </div>
                <div class="form-group col-lg-6">
                    <label for="varchar">Email user password </label>
                    <input type="password" class="form-control" name="email_pass" id="email_pass" placeholder="Email user pass" value="<?php echo $email_pass; ?>" />
                </div>

            </div>
            <br>
	    <input type="hidden" name="id" value="<?php echo $id; ?>" /> 
	    <button type="submit" class="btn btn-primary"><?php echo $button ?></button> 

	</form>
				</div>
			</div>
		</div>
	</div>
</div>
