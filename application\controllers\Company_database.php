<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');
require APPPATH . '/libraries/CpanelApi.php';
class Company_database extends CI_Controller
{
    public $servername = 'malawim2';
    public $id = 'database_id';
    public $order = 'DESC';
    function __construct()
    {
        parent::__construct();
        $this->confi = config_cpanel();
        $this->cpanel = new cpanelAPI($this->confi['servername'],$this->confi['password'],$this->confi['ip']);
        $this->load->model('Company_database_model');
		$this->load->model('Db_user_privileges_model');
		$this->load->model('Database_user_model');
        $this->load->library('form_validation');
    }
public function my_database(){
    	$data['data'] = $this->Company_database_model->my_databases($this->session->userdata('company_id'));
	$config['active_menu'] = "database";
	$config['current_link'] = "database";
	$this->load->view('header',$config);
	$this->load->view('my_database',$data);
	$this->load->view('footer');
}
public function add(){

	$config['active_menu'] = "database";
	$config['current_link'] = "create_database";
	$this->load->view('header',$config);
	$this->load->view('database_wizard');
	$this->load->view('footer');
}
public function add_action(){
    	$company = check_exist_in_table('cpanel_settings','id','1');
	$response = $this->cpanel->uapi->Mysql->create_database( array (
		'name' => $company->cpanel_user."_".$this->input->post('fullpath').'_'.$this->input->post('dbname'),

	));
	if ($response->status == 0){

		$this->session->set_flashdata('error',$response->errors);
		redirect($_SERVER["HTTP_REFERER"]);
	}else{
	$this->Company_database_model->insert(array('name'=>$this->input->post('fullpath').'_'.$this->input->post('dbname'),'company_id'=>$this->session->userdata('company_id')));
		$response2 = $this->cpanel->uapi->Mysql->create_user( array (
			'name' => $company->cpanel_user."_".$this->input->post('fullpath').'_'.$this->input->post('dbuser'),
			'password' => $this->input->post('dbpass'),

		));
		if ($response2->status == 0){

			$this->session->set_flashdata('error',$response2->errors);
			redirect($_SERVER["HTTP_REFERER"]);
		}else{
			$this->Database_user_model->insert(array('username'=>$this->input->post('fullpath').'_'.$this->input->post('dbuser'),'company_id'=>$this->session->userdata('company_id')));
			$p_list = implode(",", $this->input->post('p_id'));



			$response3 = $this->cpanel->uapi->Mysql->set_privileges_on_database( array (
				'user' => $company->cpanel_user."_".$this->input->post('fullpath').'_'.$this->input->post('dbuser'),
				'database' => $company->cpanel_user."_".$this->input->post('fullpath').'_'.$this->input->post('dbname'),
				'privileges'=> $p_list

			));
			if ($response3->status == 0){

				$this->session->set_flashdata('error',$response3->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else {
 				$this->Db_user_privileges_model->give_access($this->input->post('p_id'),$this->input->post('fullpath').'_'.$this->input->post('dbuser'));
				$this->toaster->success('Success !, database name,database user and database privileges  were saved successfully');
				redirect(site_url('database/list'));
			}
		}
	}

}
	public function update_it(){
		$company = check_exist_in_table('cpanel_settings','id','1');
		$bd_user = check_exist_in_table('database_user','database_user',$this->input->post('dbuser'));

			$p_list = implode(",", $this->input->post('p_id'));



				$response3 = $this->cpanel->uapi->Mysql->set_privileges_on_database( array (
					'user' => $company->cpanel_user."_".$bd_user->username,
					'database' => $company->cpanel_user."_".$this->input->post('dbname'),
					'privileges'=> $p_list

				));
				if ($response3->status == 0){

					$this->session->set_flashdata('error',$response3->errors);
					redirect($_SERVER["HTTP_REFERER"]);
				}else {
					$this->Db_user_privileges_model->give_access2($this->input->post('p_id'),$bd_user->username);
					$this->toaster->success('Success !,database privileges  were saved successfully');
					redirect(site_url('database/list'));
				}



	}
    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'company_database/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'company_database/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'company_database/index.html';
            $config['first_url'] = base_url() . 'company_database/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Company_database_model->total_rows($q);
        $company_database = $this->Company_database_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'company_database_data' => $company_database,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('company_database/company_database_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Company_database_model->get_by_id($id);
        if ($row) {
            $data = array(
		'database_id' => $row->database_id,
		'company_id' => $row->company_id,
		'name' => $row->name,
		'date_added' => $row->date_added,
	    );
            $this->load->view('company_database/company_database_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company_database'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('company_database/create_action'),
	    'database_id' => set_value('database_id'),
	    'company_id' => set_value('company_id'),
	    'name' => set_value('name'),
	    'date_added' => set_value('date_added'),
	);
        $this->load->view('company_database/company_database_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'company_id' => $this->input->post('company_id',TRUE),
		'name' => $this->input->post('name',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Company_database_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('company_database'));
        }
    }
    
    public function user($id)
    {
        $row = $this->Company_database_model->get_by_id2($id);

        if ($row) {
            $data = array(

		'database_id' => $row->database_id,
		'company_id' => $row->company_id,
		'name' => $row->name,

	    );
			$config['active_menu'] = "database";
			$config['current_link'] = "database";
			$this->load->view('header',$config);
            $this->load->view('user_wizard', $data);
            $this->load->view('footer');
        } else {
			$this->toaster->error('Error !, database name,name not found');
			redirect(site_url('database/list'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('database_id', TRUE));
        } else {
            $data = array(
		'company_id' => $this->input->post('company_id',TRUE),
		'name' => $this->input->post('name',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Company_database_model->update($this->input->post('database_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('company_database'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Company_database_model->get_by_id2($id);

        if ($row) {
			$company = check_exist_in_table('cpanel_settings','id','1');
			$response3 = $this->cpanel->uapi->Mysql->delete_database( array (
				'name' => $company->cpanel_user."_".$row->name,

			));
			if ($response3->status == 0){

				$this->session->set_flashdata('error',$response3->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else {
				$this->Company_database_model->delete($id);
				$this->toaster->success('Success !, database was deleted successfully');
				redirect(site_url('database/list'));
			}


        } else {
			$this->toaster->error('Sorry,no record found or you dont have permission');
			redirect(site_url('database/list'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('company_id', 'company id', 'trim|required');
	$this->form_validation->set_rules('name', 'name', 'trim|required');
	$this->form_validation->set_rules('date_added', 'date added', 'trim|required');

	$this->form_validation->set_rules('database_id', 'database_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Company_database.php */
/* Location: ./application/controllers/Company_database.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-21 06:30:28 */
/* http://harviacode.com */
