<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-lg-12">

            <div class="card">
                <div class="card-body" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">

                    <?php
                    if($this->session->flashdata('error')){
                        ?>
                        <div class="alert alert-danger" role="alert">
                            <ul>

                                <?php
                                foreach ($this->session->flashdata('error') as $err=>$value){
                                    echo "<li>-";
                                    echo $value;
                                    echo "</li>";
                                }
                                ?>
                            </ul>
                        </div>
                        <?php

                    }
                    $pm = get_all('payment_methods');
                    $pl = get_all('product_life');
                    ?>
        <h2 style="margin-top:0px">Product payments <?php echo $button ?></h2>
        <form action="<?php echo $action; ?>" method="post">
	   <div class="row">
           <div class="col-lg-6">
               <label for="int">Product to pay  <?php echo form_error('product_life_id') ?></label>
               <select class="form-control" name="product_life_id"  id="product_life_id" required>
                   <option value="">--Select Product--</option>
                   <?php
                   foreach ($pl as $pp){
                       ?>
                       <option value="<?php echo $pp->product_life_id  ?>"><?php echo $pp->product_name.' ('.$pp->product_type.')' ?></option>
                       <?php
                   }
                   ?>

               </select>
           </div>
           <div class="col-lg-6">

               <label for="int">Payment Method <?php echo form_error('payment_method') ?></label>
               <select class="form-control" name="payment_method" required>
                   <option value="">--Select Product--</option>
                   <?php
                   foreach ($pm as $p){
                       ?>
                       <option value="<?php echo $p->payment_method_id  ?>"><?php echo $p->name ?></option>
                       <?php
                   }
                   ?>

               </select>
           </div>
       </div>
	  <div class="row">
 <div class="col-lg-6">


              <label for="varchar">Reference <?php echo form_error('reference') ?></label>
              <input type="text" class="form-control" name="reference" id="reference" placeholder="Reference" value="<?php echo $reference; ?>" />
          </div>

           <div class="col-lg-6">

              <label for="decimal">Amount <?php echo form_error('amount') ?></label>
              <input type="text" class="form-control" name="amount" id="amount" placeholder="Amount" value="<?php echo $amount; ?>" />
          </div>
      </div>
	   <div class="row">
           <div class="col-lg-6">
               <label for="date">Payment Date <?php echo form_error('payment_date') ?></label>
               <input type="date" class="form-control" name="payment_date" id="payment_date" placeholder="Payment Date" value="<?php echo $payment_date; ?>" />
           </div>
           <div class="col-lg-6">
               <label for="varchar">Pop (Proof of Payment file)</label>
               <label for="id_front" id="pp" class="custom-file-upload"> Upload file </label>
               <input type="file"  onchange="uploadfile('id_front')"   id="id_front"  />
               <input type="text" id="id_front1"  name="pop" readonly>

           </div>
       </div>
           <div class="row">
               <div class="col-lg-4" style="border: thick solid red;">
                   <label for="date">Current  Product Expiry Date </label>
                   <input type="date" class="form-control" name="exp_d" id="exp_d" placeholder="Expire Date" disabled />
               </div>
               <div class="col-lg-4">
                   <p>Change Product Expire date?</p>
                   <input type="radio" name="exp" id="dno" value="no" checked />
                   <label for="pizza">NO</label>
                   <input type="radio" name="exp" id="dyes" value="yes" />
                   <label for="pasta">YES</label>
               </div>
           <div class="col-lg-4">
               <div  style="display: none;" id="disd">
                   <label for="date">Add Expiry Date </label>
                   <input type="date" class="form-control" name="expire_date" id="expire_date" placeholder="Expire Date"  />
               </div>
           </div>
           </div>

            <br>


	    <input type="hidden" name="product_payment_id" value="<?php echo $product_payment_id; ?>" /> 
	    <button type="submit" class="btn btn-primary"><?php echo $button ?></button> 

	</form>
                </div>
            </div>
        </div>
    </div>
</div>
