<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Blog extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model('Blog_model');
        $this->load->library('pagination');
        $this->load->helper('url');
        $this->load->helper('text');
    }

    /**
     * Blog listing page
     */
    public function index($page = 1) {
        $data = array();
        
        // Pagination configuration
        $config['base_url'] = base_url('blog/page/');
        $config['total_rows'] = $this->Blog_model->count_published_posts();
        $config['per_page'] = 6;
        $config['uri_segment'] = 3;
        $config['use_page_numbers'] = TRUE;
        $config['page_query_string'] = FALSE;
        
        // Pagination styling
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['first_link'] = 'First';
        $config['first_tag_open'] = '<li class="page-item">';
        $config['first_tag_close'] = '</li>';
        $config['last_link'] = 'Last';
        $config['last_tag_open'] = '<li class="page-item">';
        $config['last_tag_close'] = '</li>';
        $config['next_link'] = 'Next';
        $config['next_tag_open'] = '<li class="page-item">';
        $config['next_tag_close'] = '</li>';
        $config['prev_link'] = 'Previous';
        $config['prev_tag_open'] = '<li class="page-item">';
        $config['prev_tag_close'] = '</li>';
        $config['cur_tag_open'] = '<li class="page-item active"><span class="page-link">';
        $config['cur_tag_close'] = '</span></li>';
        $config['num_tag_open'] = '<li class="page-item">';
        $config['num_tag_close'] = '</li>';
        $config['attributes'] = array('class' => 'page-link');
        
        $this->pagination->initialize($config);
        
        $page = ($page > 0) ? $page : 1;
        $offset = ($page - 1) * $config['per_page'];
        
        // Get posts
        $data['posts'] = $this->Blog_model->get_published_posts($config['per_page'], $offset);
        $data['pagination'] = $this->pagination->create_links();
        $data['current_page'] = $page;
        $data['total_pages'] = ceil($config['total_rows'] / $config['per_page']);
        
        // Sidebar data
        $data['categories'] = $this->Blog_model->get_categories();
        $data['recent_posts'] = $this->Blog_model->get_recent_posts();
        $data['popular_tags'] = $this->Blog_model->get_popular_tags();
        
        $data['page_title'] = 'Blog - EmailHost-Plus';
        $data['meta_description'] = 'Read our latest blog posts about email hosting, web development, and business tips.';
        
        $this->load->view('landing/blog/index', $data);
    }

    /**
     * Single blog post
     */
    public function post($slug) {
        $post = $this->Blog_model->get_post_by_slug($slug);
        
        if (!$post) {
            show_404();
        }
        
        // Increment views
        $this->Blog_model->increment_views($post->id);
        
        $data = array();
        $data['post'] = $post;
        $data['post_tags'] = $this->Blog_model->get_post_tags($post->id);
        $data['related_posts'] = $this->Blog_model->get_related_posts($post->id, $post->category_id);
        $data['comments'] = $this->Blog_model->get_post_comments($post->id);
        $data['comment_count'] = $this->Blog_model->count_post_comments($post->id);
        
        // Sidebar data
        $data['categories'] = $this->Blog_model->get_categories();
        $data['recent_posts'] = $this->Blog_model->get_recent_posts();
        $data['popular_tags'] = $this->Blog_model->get_popular_tags();
        
        $data['page_title'] = $post->meta_title ?: $post->title;
        $data['meta_description'] = $post->meta_description ?: $post->excerpt;
        $data['meta_keywords'] = $post->meta_keywords;
        
        $this->load->view('landing/blog/single', $data);
    }

    /**
     * Category page
     */
    public function category($slug, $page = 1) {
        $category = $this->Blog_model->get_category_by_slug($slug);
        
        if (!$category) {
            show_404();
        }
        
        $data = array();
        
        // Pagination configuration
        $config['base_url'] = base_url('blog/category/' . $slug . '/page/');
        $config['total_rows'] = $this->Blog_model->count_published_posts($category->id);
        $config['per_page'] = 6;
        $config['uri_segment'] = 5;
        $config['use_page_numbers'] = TRUE;
        
        // Same pagination styling as index
        $config['full_tag_open'] = '<nav><ul class="pagination justify-content-center">';
        $config['full_tag_close'] = '</ul></nav>';
        $config['attributes'] = array('class' => 'page-link');
        
        $this->pagination->initialize($config);
        
        $page = ($page > 0) ? $page : 1;
        $offset = ($page - 1) * $config['per_page'];
        
        // Get posts
        $data['posts'] = $this->Blog_model->get_published_posts($config['per_page'], $offset, $category->id);
        $data['pagination'] = $this->pagination->create_links();
        $data['category'] = $category;
        
        // Sidebar data
        $data['categories'] = $this->Blog_model->get_categories();
        $data['recent_posts'] = $this->Blog_model->get_recent_posts();
        $data['popular_tags'] = $this->Blog_model->get_popular_tags();
        
        $data['page_title'] = $category->name . ' - Blog - EmailHost-Plus';
        $data['meta_description'] = $category->description;
        
        $this->load->view('landing/blog/category', $data);
    }

    /**
     * Tag page
     */
    public function tag($slug, $page = 1) {
        $data = array();
        
        // Get posts by tag
        $config['per_page'] = 6;
        $page = ($page > 0) ? $page : 1;
        $offset = ($page - 1) * $config['per_page'];
        
        $data['posts'] = $this->Blog_model->get_posts_by_tag($slug, $config['per_page'], $offset);
        $data['tag_slug'] = $slug;
        $data['tag_name'] = ucfirst(str_replace('-', ' ', $slug));
        
        // Sidebar data
        $data['categories'] = $this->Blog_model->get_categories();
        $data['recent_posts'] = $this->Blog_model->get_recent_posts();
        $data['popular_tags'] = $this->Blog_model->get_popular_tags();
        
        $data['page_title'] = $data['tag_name'] . ' - Blog - EmailHost-Plus';
        
        $this->load->view('landing/blog/tag', $data);
    }

    /**
     * Search posts
     */
    public function search() {
        $query = $this->input->get('q');
        
        if (empty($query)) {
            redirect('blog');
        }
        
        $data = array();
        $data['posts'] = $this->Blog_model->search_posts($query);
        $data['search_query'] = $query;
        
        // Sidebar data
        $data['categories'] = $this->Blog_model->get_categories();
        $data['recent_posts'] = $this->Blog_model->get_recent_posts();
        $data['popular_tags'] = $this->Blog_model->get_popular_tags();
        
        $data['page_title'] = 'Search Results for "' . $query . '" - Blog - EmailHost-Plus';
        
        $this->load->view('landing/blog/search', $data);
    }

    /**
     * Add comment (AJAX)
     */
    public function add_comment() {
        if ($this->input->method() !== 'post') {
            show_404();
        }
        
        $this->load->library('form_validation');
        
        $this->form_validation->set_rules('post_id', 'Post ID', 'required|integer');
        $this->form_validation->set_rules('author_name', 'Name', 'required|max_length[100]');
        $this->form_validation->set_rules('author_email', 'Email', 'required|valid_email|max_length[255]');
        $this->form_validation->set_rules('author_website', 'Website', 'valid_url|max_length[255]');
        $this->form_validation->set_rules('content', 'Comment', 'required|max_length[1000]');
        $this->form_validation->set_rules('parent_id', 'Parent ID', 'integer');
        
        if ($this->form_validation->run() === FALSE) {
            $response = array(
                'success' => false,
                'message' => validation_errors()
            );
        } else {
            $comment_data = array(
                'post_id' => $this->input->post('post_id'),
                'parent_id' => $this->input->post('parent_id') ?: null,
                'author_name' => $this->input->post('author_name'),
                'author_email' => $this->input->post('author_email'),
                'author_website' => $this->input->post('author_website'),
                'content' => $this->input->post('content'),
                'status' => 'pending', // Comments need approval
                'ip_address' => $this->input->ip_address(),
                'user_agent' => $this->input->user_agent()
            );
            
            if ($this->Blog_model->add_comment($comment_data)) {
                $response = array(
                    'success' => true,
                    'message' => 'Thank you for your comment! It will be published after review.'
                );
            } else {
                $response = array(
                    'success' => false,
                    'message' => 'Failed to add comment. Please try again.'
                );
            }
        }
        
        header('Content-Type: application/json');
        echo json_encode($response);
    }

    /**
     * Page routing for pagination
     */
    public function page($page_num = 1) {
        $this->index($page_num);
    }
}
