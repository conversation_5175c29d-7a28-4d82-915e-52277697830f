<?php
$domains = $this->Company_domains_model->get_my_domains($this->session->userdata('company_id'));
$company = $this->Company_model->company_details($this->session->userdata('company_id'));

?>
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-lg-12">

			<div class="card">
				<div class="card-body" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">
        <h2 style="margin-top:0px">Email account creation</h2>
					<?php
					if($this->session->flashdata('error')){
						?>
						<div class="alert alert-danger" role="alert">
							<ul>

							<?php
							foreach ($this->session->flashdata('error') as $err=>$value){
								echo "<li>-";
								echo $value;
								echo "</li>";
							}
							?>
							</ul>
						</div>
					<?php
					}
					?>
        <form action="<?php echo $action; ?>" method="post">
<div class="row">
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">

				<div class="field-wrapper">
					<select class="select-single js-states" name="domain"  id="dm" title="Select Domain" required>
                        <option value="">--select domain--</option>
						<?php
						foreach ($domains as $d){
							?>
							<option value="<?php echo $d->domain_name ?>"><?php echo $d->domain_name ?></option>
						<?php
						}
						?>
					</select>
					<div class="field-placeholder">Domain <?php echo form_error('domain') ?></div>
				</div>
			</div>
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
				<div class="field-wrapper">
					<div class="input-group">
						<input class="form-control" type="text" name="email"  onkeypress="return /[0-9a-zA-Z]/i.test(event.key)">
						<span class="input-group-text">
							<i class="icon-email"></i> <i id="cdomain">example.com</i>
						</span>
					</div>
					<div class="field-placeholder">email name <?php echo form_error('email') ?> <span class="text-danger">*</span></div>
				</div>

			</div>
	<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">

	</div>

</div>
			<div class="row">
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">

				<div class="field-wrapper">
					<input class="form-control" name="pass" id="pass" type="password" placeholder="Enter password">
					<div class="field-placeholder">Password <span class="text-danger">*</span></div>
				</div>
			</div>
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
				<div class="field-wrapper">
					<input class="form-control" id="pass2" name="pass2" type="password" placeholder="Repeat password">
					<div class="field-placeholder">Confirm password <span class="text-danger">*</span></div>
				</div>
			</div>

</div>
			<div id="passdiv"></div>

	    <button type="submit" id="sbtn" class="btn btn-primary"><?php echo $button ?></button>

	</form>
				</div>
			</div>
		</div>
	</div>
</div>
