// Landing Page JavaScript
document.addEventListener('DOMContentLoaded', function() {

    // Mobile menu toggle
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    if (navbarToggler) {
        navbarToggler.addEventListener('click', function() {
            navbarCollapse.classList.toggle('show');
        });
    }

    // Domain search functionality
    const domainForm = document.getElementById('domainSearchForm');
    const domainInput = document.getElementById('domainInput');
    const searchBtn = document.getElementById('searchBtn');
    const searchResult = document.getElementById('searchResult');

    if (domainForm) {
        domainForm.addEventListener('submit', function(e) {
            e.preventDefault();
            searchDomain();
        });
    }

    function searchDomain() {
        const domain = domainInput.value.trim();

        if (!domain) {
            showSearchResult('Please enter a domain name', 'error');
            return;
        }

        // Show loading state
        const originalText = searchBtn.innerHTML;
        searchBtn.innerHTML = '<span class="loading"></span> Searching...';
        searchBtn.disabled = true;

        // Make AJAX request
        fetch(base_url + 'search-domain', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: 'domain=' + encodeURIComponent(domain)
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const resultClass = data.available ? 'available' : 'taken';
                const icon = data.available ? '✓' : '✗';
                showSearchResult(icon + ' ' + data.message, resultClass);

                if (data.available) {
                    // Add "Get Started" button for available domains
                    setTimeout(() => {
                        const getStartedBtn = document.createElement('a');
                        getStartedBtn.href = base_url + 'pricing';
                        getStartedBtn.className = 'btn btn-success mt-2';
                        getStartedBtn.textContent = 'Get Started with ' + data.domain;
                        searchResult.appendChild(getStartedBtn);
                    }, 500);
                }
            } else {
                showSearchResult(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showSearchResult('An error occurred while searching. Please try again.', 'error');
        })
        .finally(() => {
            // Restore button state
            searchBtn.innerHTML = originalText;
            searchBtn.disabled = false;
        });
    }

    function showSearchResult(message, type) {
        searchResult.innerHTML = message;
        searchResult.className = 'search-result ' + type;
        searchResult.style.display = 'block';

        // Smooth scroll to result
        searchResult.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Contact form handling
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            // Basic client-side validation
            const name = document.getElementById('name').value.trim();
            const email = document.getElementById('email').value.trim();
            const subject = document.getElementById('subject').value.trim();
            const message = document.getElementById('message').value.trim();

            if (!name || !email || !subject || !message) {
                e.preventDefault();
                showAlert('Please fill in all required fields.', 'danger');
                return;
            }

            if (!isValidEmail(email)) {
                e.preventDefault();
                showAlert('Please enter a valid email address.', 'danger');
                return;
            }

            // Show loading state
            const submitBtn = contactForm.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="loading"></span> Sending...';
            submitBtn.disabled = true;

            // Form will submit normally, restore button after a delay
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 3000);
        });
    }

    // Email validation helper
    function isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }

    // Alert helper function
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at the top of the form
        const form = document.getElementById('contactForm');
        if (form) {
            form.insertBefore(alertDiv, form.firstChild);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
    }

    // Pricing plan selection
    document.querySelectorAll('.plan-select-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            const planName = this.dataset.plan;

            // Store selected plan in localStorage
            localStorage.setItem('selectedPlan', planName);

            // Redirect to contact or signup page
            window.location.href = base_url + 'contact?plan=' + encodeURIComponent(planName);
        });
    });

    // Animate elements on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.feature-card, .section-title').forEach(el => {
        observer.observe(el);
    });

    // Add animation styles
    const style = document.createElement('style');
    style.textContent = `
        .feature-card, .section-title {
            opacity: 0;
            transform: translateY(30px);
            transition: opacity 0.6s ease, transform 0.6s ease;
        }

        .feature-card.animate-in, .section-title.animate-in {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    document.head.appendChild(style);

    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const navbar = document.querySelector('.landing-header');
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // Add scrolled navbar styles
    const navbarStyle = document.createElement('style');
    navbarStyle.textContent = `
        .landing-header.scrolled {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }
    `;
    document.head.appendChild(navbarStyle);

    // Initialize tooltips if Bootstrap is available
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Copy to clipboard functionality for contact info
    document.querySelectorAll('.copy-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const text = this.dataset.copy;
            navigator.clipboard.writeText(text).then(() => {
                const originalText = this.textContent;
                this.textContent = 'Copied!';
                setTimeout(() => {
                    this.textContent = originalText;
                }, 2000);
            });
        });
    });
});

// Global variables that might be needed
const base_url = window.location.origin + '/';

// Utility functions
function formatPrice(price) {
    return new Intl.NumberFormat('en-MW', {
        style: 'currency',
        currency: 'MWK',
        minimumFractionDigits: 0
    }).format(price);
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
