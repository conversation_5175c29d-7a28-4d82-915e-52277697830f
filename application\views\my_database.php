<!-- Content wrapper start -->
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">
				<div class="card-body">
					<h5>MySQL databases</h5>
					<hr>
					<a  href="<?php echo base_url('my_database/create')?>" class="btn btn-primary"><i class="icon-add_circle"></i>Create database</a>
					<br><br>
					<div class="table-responsive">
						<?php
						if($this->session->flashdata('error')){
							?>
							<div class="alert alert-danger" role="alert">
								<ul>

									<?php
									foreach ($this->session->flashdata('error') as $err=>$value){
										echo "<li>-";
										echo $value;
										echo "</li>";
									}
									?>
								</ul>
							</div>
							<?php
						}
						?>
						<table id="copy-print-csv" class="table v-middle">
							<thead>
							<tr>
								<th>Mysql database name</th>

								<th>Date created</th>
								<th>Actions</th>
							</tr>
							</thead>
							<tbody>
							<?php
							foreach ($data as $d){
								?>

								<tr>

									<td>
										<?php  echo $d->name;?>
									</td>

									<td>
										<?php  echo $d->date_added;?>
									</td>
									<td><a href="<?php  echo base_url('Company_database/user/').$d->database_id ?>" class="btn btn-sm btn-info"><i class="icon-eye"></i>Mysql db user</a><a href="<?php echo base_url('Company_database/delete/'.$d->database_id )?>" class="btn btn-sm btn-danger" onclick="return confirm('Are you sure you want to delete this?')"><i class="icon-trash-2" ></i>Delete</a></td>
								</tr>
							<?php
							}
							?>


							</tbody>
						</table>
					</div>

				</div>
			</div>

		</div>
	</div>
	<!-- Row end -->

</div>
<!-- Content wrapper end -->
