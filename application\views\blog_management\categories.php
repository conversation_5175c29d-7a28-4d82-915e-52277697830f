<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <!-- Add New Category -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <h4><i class="icon-plus me-2"></i>Add New Category</h4>
                    </div>
                </div>
                <div class="card-body">

                    <?php if (validation_errors()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo validation_errors(); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="field-wrapper">
                            <input type="text" name="name" value="<?php echo set_value('name'); ?>" required>
                            <div class="field-placeholder">Category Name *</div>
                        </div>

                        <div class="field-wrapper">
                            <textarea name="description" rows="3" placeholder="Optional category description..."><?php echo set_value('description'); ?></textarea>
                            <div class="field-placeholder">Description</div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="icon-check me-2"></i>Add Category
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Categories List -->
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="card-title d-flex justify-content-between align-items-center">
                        <h4><i class="icon-list me-2"></i>Existing Categories</h4>
                        <a href="<?php echo base_url('Blog_management'); ?>" class="btn btn-outline-secondary">
                            <i class="icon-arrow-left"></i> Back to Posts
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <?php if ($this->session->flashdata('message')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('message'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($categories)): ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Slug</th>
                                        <th>Description</th>
                                        <th>Posts</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($categories as $category): ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo htmlspecialchars($category->name); ?></strong>
                                            </td>
                                            <td>
                                                <code><?php echo htmlspecialchars($category->slug); ?></code>
                                            </td>
                                            <td>
                                                <?php if ($category->description): ?>
                                                    <?php echo htmlspecialchars(word_limiter($category->description, 10)); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">No description</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                // Count posts in this category
                                                $this->db->where('category_id', $category->id);
                                                $post_count = $this->db->count_all_results('blog_posts');
                                                ?>
                                                <span class="badge bg-secondary"><?php echo $post_count; ?></span>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <a href="<?php echo base_url('blog/category/' . $category->slug); ?>" 
                                                       class="btn btn-outline-primary btn-sm" target="_blank" title="View">
                                                        <i class="icon-eye"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-secondary btn-sm" 
                                                            onclick="editCategory(<?php echo $category->id; ?>, '<?php echo addslashes($category->name); ?>', '<?php echo addslashes($category->description); ?>')" 
                                                            title="Edit">
                                                        <i class="icon-edit"></i>
                                                    </button>
                                                    <?php if ($post_count == 0): ?>
                                                        <button type="button" class="btn btn-outline-danger btn-sm" 
                                                                onclick="deleteCategory(<?php echo $category->id; ?>)" title="Delete">
                                                            <i class="icon-trash"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="icon-folder display-1 text-muted mb-3"></i>
                            <h5>No categories yet</h5>
                            <p class="text-muted">Create your first category to organize your blog posts.</p>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
        </div>
    </div>
    <!-- Row end -->

</div>
<!-- Content wrapper end -->

<!-- Edit Category Modal -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Category</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCategoryForm" method="post">
                <div class="modal-body">
                    <input type="hidden" id="edit_category_id" name="category_id">
                    
                    <div class="field-wrapper">
                        <input type="text" id="edit_name" name="name" required>
                        <div class="field-placeholder">Category Name *</div>
                    </div>

                    <div class="field-wrapper">
                        <textarea id="edit_description" name="description" rows="3"></textarea>
                        <div class="field-placeholder">Description</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="icon-check me-2"></i>Update Category
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editCategory(id, name, description) {
    document.getElementById('edit_category_id').value = id;
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_description').value = description;
    
    const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
    modal.show();
}

function deleteCategory(categoryId) {
    if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
        // Create a form to submit the delete request
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo base_url("Blog_management/delete_category/"); ?>' + categoryId;
        
        // Add CSRF token if needed
        const csrfInput = document.createElement('input');
        csrfInput.type = 'hidden';
        csrfInput.name = 'delete_category';
        csrfInput.value = '1';
        form.appendChild(csrfInput);
        
        document.body.appendChild(form);
        form.submit();
    }
}

// Handle edit form submission
document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const categoryId = formData.get('category_id');
    
    // Create a form to submit the update
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '<?php echo base_url("Blog_management/edit_category/"); ?>' + categoryId;
    
    // Add form data
    for (let [key, value] of formData.entries()) {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    }
    
    document.body.appendChild(form);
    form.submit();
});
</script>

<style>
.actions {
    display: flex;
    gap: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.table td {
    vertical-align: middle;
}

.display-1 {
    font-size: 4rem;
}

.field-wrapper {
    margin-bottom: 1.5rem;
}

code {
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}
</style>
