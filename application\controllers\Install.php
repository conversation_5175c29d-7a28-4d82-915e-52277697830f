<?php


class Install extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Users_model');
    }
    function index(){
        $this->load->view('install');
    }
    function finish_setup(){
        if($this->input->post('password',TRUE)==$this->input->post('password2',TRUE)){
            $data = array(
                'full_name' => $this->input->post('fullname',TRUE),
                'email' => $this->input->post('email',TRUE),
                'password' => md5($this->input->post('password',TRUE)),
                'company' => 0,
                'user_role' => 1,

            );

            $this->Users_model->insert($data);
            redirect('Login');
        }else{
            $this->session->set_flashdata('error','Sorry two passwords did not match');
            redirect($_SERVER['HTTP_REFERER']);
        }

    }

}