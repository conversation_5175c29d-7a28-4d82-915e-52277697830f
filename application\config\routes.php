<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/*
| -------------------------------------------------------------------------
| URI ROUTING
| -------------------------------------------------------------------------
| This file lets you re-map URI requests to specific controller functions.
|
| Typically there is a one-to-one relationship between a URL string
| and its corresponding controller class/method. The segments in a
| URL normally follow this pattern:
|
|	example.com/class/method/id/
|
| In some instances, however, you may want to remap this relationship
| so that a different class/function is called than the one
| corresponding to the URL.
|
| Please see the user guide for complete details:
|
|	https://codeigniter.com/user_guide/general/routing.html
|
| -------------------------------------------------------------------------
| RESERVED ROUTES
| -------------------------------------------------------------------------
|
| There are three reserved routes:
|
|	$route['default_controller'] = 'welcome';
|
| This route indicates which controller class should be loaded if the
| URI contains no data. In the above example, the "welcome" class
| would be loaded.
|
|	$route['404_override'] = 'errors/page_missing';
|
| This route will tell the Router which controller/method to use if those
| provided in the URL cannot be matched to a valid route.
|
|	$route['translate_uri_dashes'] = FALSE;
|
| This is not exactly a route, but allows you to automatically route
| controller and method names that contain dashes. '-' isn't a valid
| class or method name character, so it requires translation.
| When you set this option to TRUE, it will replace ALL dashes in the
| controller and method URI segments.
|
| Examples:	my-controller/index	-> my_controller/index
|		my-controller/my-method	-> my_controller/my_method
*/
$route['default_controller'] = 'Home';
$route['login'] = 'Login/index';
$route['about'] = 'Home/about';
$route['contact'] = 'Home/contact';
$route['pricing'] = 'Home/pricing';
$route['privacy'] = 'Home/privacy';
$route['terms'] = 'Home/terms';
$route['refund'] = 'Home/refund';
$route['search-domain'] = 'Home/search_domain';
$route['my_domains'] = 'company_domains/my_domains';
$route['buy_domains'] = 'company_domains/buy_domain';
$route['my_subdomains'] = 'sub_domains/my_subdomains';
$route['my_subdomains/create'] = 'sub_domains/create';
$route['my_emails'] = 'company_emails/my_emails';
$route['my_emails/create'] = 'company_emails/create';
$route['files/file_manager'] = 'Fmgr/index';
$route['files/ftp'] = 'Ftp_accounts/my_accounts';
$route['files/webdisk'] = 'Module_pending/webdisk';
$route['files/git'] = 'Module_pending/webdisk';
$route['database/remote'] = 'Module_pending/webdisk';
$route['database/phpmyadmin'] = 'Module_pending/webdisk';
$route['database/list'] = 'Company_database/my_database';
$route['my_database/create'] = 'Company_database/add';
$route['my_email_forwarder'] = 'Email_forwarder';
$route['my_emails/manage/(:any)'] = 'company_emails/manage/$1';

// Blog routes
$route['blog'] = 'Blog/index';
$route['blog/page/(:num)'] = 'Blog/page/$1';
$route['blog/search'] = 'Blog/search';
$route['blog/category/(:any)'] = 'Blog/category/$1';
$route['blog/category/(:any)/page/(:num)'] = 'Blog/category/$1/$2';
$route['blog/tag/(:any)'] = 'Blog/tag/$1';
$route['blog/tag/(:any)/page/(:num)'] = 'Blog/tag/$1/$2';
$route['blog/post/(:any)'] = 'Blog/post/$1';
$route['blog/add_comment'] = 'Blog/add_comment';

$route['404_override'] = '';
$route['translate_uri_dashes'] = FALSE;
