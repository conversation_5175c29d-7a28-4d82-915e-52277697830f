<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - EmailHost-Plus Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- TinyMCE Editor -->
    <script src="https://cdn.tiny.cloud/1/no-api-key/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --cpanel-orange: #ff6c2c;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .form-label {
            font-weight: 600;
            color: #374151;
        }
        
        .image-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 0.5rem;
        }
        
        .current-image {
            border: 2px solid #e5e7eb;
            border-radius: 0.5rem;
            padding: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-blog me-2"></i>Blog Admin
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin'); ?>">
                            <i class="fas fa-list me-2"></i>All Posts
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/create'); ?>">
                            <i class="fas fa-plus me-2"></i>Add New Post
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/categories'); ?>">
                            <i class="fas fa-folder me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/comments'); ?>">
                            <i class="fas fa-comments me-2"></i>Comments
                        </a>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                        <a class="nav-link" href="<?php echo base_url('blog'); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Blog
                        </a>
                        <a class="nav-link" href="<?php echo base_url(); ?>">
                            <i class="fas fa-home me-2"></i>Back to Site
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><?php echo $page_title; ?></h2>
                        <div>
                            <?php if ($post->status === 'published'): ?>
                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="btn btn-outline-primary me-2" target="_blank">
                                    <i class="fas fa-eye me-2"></i>View Post
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo base_url('admin/blog_admin'); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Posts
                            </a>
                        </div>
                    </div>

                    <!-- Flash Messages -->
                    <?php if (validation_errors()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo validation_errors(); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Edit Post Form -->
                    <form method="post" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Main Content -->
                            <div class="col-lg-8">
                                <div class="card mb-4">
                                    <div class="card-body">
                                        <!-- Title -->
                                        <div class="mb-3">
                                            <label for="title" class="form-label">Post Title *</label>
                                            <input type="text" class="form-control" id="title" name="title" 
                                                   value="<?php echo set_value('title', $post->title); ?>" required>
                                        </div>

                                        <!-- Content -->
                                        <div class="mb-3">
                                            <label for="content" class="form-label">Content *</label>
                                            <textarea id="content" name="content" class="form-control" rows="15"><?php echo set_value('content', $post->content); ?></textarea>
                                        </div>

                                        <!-- Excerpt -->
                                        <div class="mb-3">
                                            <label for="excerpt" class="form-label">Excerpt</label>
                                            <textarea class="form-control" id="excerpt" name="excerpt" rows="3" 
                                                      placeholder="Optional short description..."><?php echo set_value('excerpt', $post->excerpt); ?></textarea>
                                            <div class="form-text">Leave empty to auto-generate from content.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- SEO Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-search me-2"></i>SEO Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="meta_title" class="form-label">Meta Title</label>
                                            <input type="text" class="form-control" id="meta_title" name="meta_title" 
                                                   value="<?php echo set_value('meta_title', $post->meta_title); ?>" maxlength="60">
                                            <div class="form-text">Leave empty to use post title. Recommended: 50-60 characters.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="meta_description" class="form-label">Meta Description</label>
                                            <textarea class="form-control" id="meta_description" name="meta_description" 
                                                      rows="2" maxlength="160"><?php echo set_value('meta_description', $post->meta_description); ?></textarea>
                                            <div class="form-text">Recommended: 150-160 characters.</div>
                                        </div>

                                        <div class="mb-3">
                                            <label for="meta_keywords" class="form-label">Meta Keywords</label>
                                            <input type="text" class="form-control" id="meta_keywords" name="meta_keywords" 
                                                   value="<?php echo set_value('meta_keywords', $post->meta_keywords); ?>">
                                            <div class="form-text">Comma-separated keywords (optional).</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="col-lg-4">
                                <!-- Publish Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-cog me-2"></i>Publish Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <label for="status" class="form-label">Status</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="draft" <?php echo set_select('status', 'draft', $post->status === 'draft'); ?>>Draft</option>
                                                <option value="published" <?php echo set_select('status', 'published', $post->status === 'published'); ?>>Published</option>
                                                <option value="archived" <?php echo set_select('status', 'archived', $post->status === 'archived'); ?>>Archived</option>
                                            </select>
                                        </div>

                                        <div class="d-grid gap-2">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save me-2"></i>Update Post
                                            </button>
                                            <a href="<?php echo base_url('admin/blog_admin'); ?>" class="btn btn-outline-secondary">
                                                Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Category</h5>
                                    </div>
                                    <div class="card-body">
                                        <select class="form-select" name="category_id">
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category->id; ?>" 
                                                        <?php echo set_select('category_id', $category->id, $post->category_id == $category->id); ?>>
                                                    <?php echo htmlspecialchars($category->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Tags -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Tags</h5>
                                    </div>
                                    <div class="card-body">
                                        <input type="text" class="form-control" name="tags" 
                                               value="<?php echo set_value('tags', $post->tags); ?>" 
                                               placeholder="Enter tags separated by commas">
                                        <div class="form-text">e.g., email hosting, tutorial, business</div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5 class="mb-0"><i class="fas fa-image me-2"></i>Featured Image</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php if ($post->featured_image): ?>
                                            <div class="mb-3">
                                                <label class="form-label">Current Image:</label>
                                                <div class="current-image">
                                                    <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" 
                                                         alt="Current featured image" class="image-preview img-fluid">
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <input type="file" class="form-control" name="featured_image" 
                                               accept="image/*" onchange="previewImage(this)">
                                        <div class="form-text">
                                            <?php if ($post->featured_image): ?>
                                                Upload a new image to replace the current one.
                                            <?php else: ?>
                                                Recommended: 1200x630px, max 2MB
                                            <?php endif; ?>
                                        </div>
                                        <div id="imagePreview" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Initialize TinyMCE
        tinymce.init({
            selector: '#content',
            height: 400,
            menubar: false,
            plugins: [
                'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                'insertdatetime', 'media', 'table', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | bold italic forecolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | removeformat | help',
            content_style: 'body { font-family: -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif; font-size: 14px; }'
        });

        // Image preview
        function previewImage(input) {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = '';
            
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    img.className = 'image-preview img-fluid';
                    img.style.border = '2px dashed #d1d5db';
                    img.style.borderRadius = '0.5rem';
                    img.style.padding = '0.5rem';
                    preview.appendChild(img);
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
</body>
</html>
