<?php


class Dashboard extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model("Company_domains_model");
		$this->load->model('Company_emails_model');
		$this->load->model('Product_life_model');
		$this->load->model('Sub_domains_model');
	}
	public function index(){
        $data = array(
            'product_life_data' => $this->Product_life_model->get_all(),
        );
		$data['count_domains'] = $this->Company_domains_model->count_my_domains($this->session->userdata('company_id'));
		$data['emails'] = $this->Company_emails_model->count_my_emails($this->session->userdata('company_id'));
		$data['subdomains'] = $this->Sub_domains_model->count_my_subdomains($this->session->userdata('company_id'));
		$config['active_menu'] = "dashboard";
		$config['current_link'] = "dashboard";
$this->load->view('header',$config);
$this->load->view('index',$data);
$this->load->view('footer');
	}
}
