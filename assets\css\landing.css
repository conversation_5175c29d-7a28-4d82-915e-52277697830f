/* Modern Landing Page Styles */
:root {
    --primary-color: #6366f1;
    --primary-dark: #4f46e5;
    --primary-light: #8b5cf6;
    --secondary-color: #0ea5e9;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;

    /* cPanel Orange Color */
    --cpanel-orange: #ff6c2c;
    --cpanel-orange-dark: #e55a1f;
    --cpanel-orange-light: #ff8a52;

    --text-dark: #0f172a;
    --text-medium: #334155;
    --text-light: #64748b;
    --text-lighter: #94a3b8;
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-dark: #0f172a;
    --border-color: #e2e8f0;
    --border-light: #f1f5f9;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
    --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, #fb923c 100%);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

*::before,
*::after {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.7;
    color: var(--text-dark);
    background-color: var(--bg-primary);
    font-weight: 400;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Improved Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.3;
    color: var(--text-dark);
    margin-bottom: 0.5em;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }
h5 { font-size: clamp(1.125rem, 2vw, 1.25rem); }
h6 { font-size: clamp(1rem, 1.5vw, 1.125rem); }

p {
    margin-bottom: 1rem;
    color: var(--text-medium);
}

.lead {
    font-size: 1.25rem;
    font-weight: 400;
    color: var(--text-light);
}

/* Modern Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    font-weight: 600;
    line-height: 1.5;
    border-radius: 0.75rem;
    border: 2px solid transparent;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-secondary {
    background: var(--gradient-secondary);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-light {
    background: rgba(255, 255, 255, 0.95);
    color: var(--text-dark);
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-md);
}

.btn-light:hover {
    background: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-dark);
}

.btn-lg {
    padding: 1rem 2rem;
    font-size: 1.125rem;
    border-radius: 1rem;
}

/* Modern Header Styles */
.landing-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.landing-header.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-lg);
    border-bottom-color: var(--border-color);
}

.navbar {
    padding: 1.25rem 0;
    transition: padding 0.3s ease;
}

.landing-header.scrolled .navbar {
    padding: 0.75rem 0;
}

.navbar-brand {
    font-size: 1.75rem;
    font-weight: 800;
    color: var(--cpanel-orange) !important;
    text-decoration: none;
    transition: all 0.3s ease;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand:hover {
    transform: scale(1.05);
    color: var(--cpanel-orange-light) !important;
}

.navbar-brand span {
    color: var(--cpanel-orange) !important;
    font-weight: 800;
}

.navbar-nav {
    align-items: center;
    gap: 0.5rem;
}

.navbar-nav .nav-link {
    color: var(--text-medium);
    font-weight: 500;
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.navbar-nav .nav-link::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: var(--primary-color);
    background: var(--bg-tertiary);
}

.navbar-nav .nav-link:hover::before,
.navbar-nav .nav-link.active::before {
    width: 80%;
}

.btn-login {
    background: var(--gradient-primary);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.75rem;
    text-decoration: none;
    font-weight: 600;
    box-shadow: var(--shadow-md);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: white;
}

/* Mobile Navigation */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    background: var(--bg-tertiary);
    transition: all 0.3s ease;
}

.navbar-toggler:focus {
    box-shadow: none;
}

.navbar-toggler-icon {
    background-image: none;
    width: 24px;
    height: 2px;
    background: var(--text-dark);
    border-radius: 2px;
    position: relative;
    transition: all 0.3s ease;
}

.navbar-toggler-icon::before,
.navbar-toggler-icon::after {
    content: '';
    position: absolute;
    width: 24px;
    height: 2px;
    background: var(--text-dark);
    border-radius: 2px;
    transition: all 0.3s ease;
}

.navbar-toggler-icon::before {
    top: -8px;
}

.navbar-toggler-icon::after {
    bottom: -8px;
}

/* Modern Hero Section */
.hero-section {
    background: var(--gradient-primary);
    color: white;
    padding: 10rem 0 6rem;
    margin-top: 0;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="a" cx="50%" cy="50%"><stop offset="0%" stop-color="rgba(255,255,255,0.1)"/><stop offset="100%" stop-color="rgba(255,255,255,0)"/></radialGradient></defs><circle cx="200" cy="200" r="100" fill="url(%23a)"/><circle cx="800" cy="300" r="150" fill="url(%23a)"/><circle cx="400" cy="700" r="120" fill="url(%23a)"/></svg>') no-repeat center center;
    background-size: cover;
    opacity: 0.3;
}

.hero-section .container {
    position: relative;
    z-index: 2;
}

.hero-content h1 {
    font-size: clamp(3rem, 6vw, 4.5rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.hero-content .lead {
    font-size: 1.375rem;
    margin-bottom: 2.5rem;
    color: rgba(255, 255, 255, 0.95);
    font-weight: 400;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

/* Ensure all hero section text is white */
.hero-section h1,
.hero-section h2,
.hero-section h3,
.hero-section h4,
.hero-section h5,
.hero-section h6 {
    color: #ffffff !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.hero-section p,
.hero-section .lead {
    color: rgba(255, 255, 255, 0.95) !important;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.price-highlight {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    padding: 2.5rem;
    border-radius: 2rem;
    margin: 3rem 0;
    backdrop-filter: blur(20px);
    border: 2px solid rgba(255, 255, 255, 0.5);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
}

.price-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--cpanel-orange) 0%, var(--cpanel-orange-light) 100%);
    opacity: 0.1;
    border-radius: 2rem;
    pointer-events: none;
}

.price-highlight::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--cpanel-orange) 0%, var(--cpanel-orange-light) 100%);
    border-radius: 2rem 2rem 0 0;
}

.price-highlight h3 {
    font-size: clamp(2.2rem, 4vw, 3.2rem);
    font-weight: 900;
    color: var(--cpanel-orange);
    margin-bottom: 0.5rem;
    text-shadow: 0 2px 4px rgba(255, 108, 44, 0.2);
    position: relative;
    z-index: 1;
    background: linear-gradient(135deg, var(--cpanel-orange) 0%, var(--cpanel-orange-dark) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.price-highlight p {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--primary-color) !important;
    position: relative;
    z-index: 1;
}

.price-highlight p:last-child {
    font-size: 1rem;
    color: var(--primary-color) !important;
    font-weight: 500;
}

/* Modern Domain Search */
.domain-search {
    background: rgba(255, 255, 255, 0.98);
    padding: 2.5rem;
    border-radius: 2rem;
    box-shadow: var(--shadow-xl);
    margin-top: 4rem;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    position: relative;
}

.domain-search::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0.1) 100%);
    border-radius: 2rem;
    pointer-events: none;
}

.domain-search h4 {
    color: var(--primary-color) !important;
    margin-bottom: 2rem;
    text-align: center;
    font-weight: 700;
    font-size: 1.5rem;
    position: relative;
    z-index: 1;
}

.domain-search h4 i {
    color: var(--primary-color) !important;
}

/* Statistics Numbers */
.stats-number {
    color: var(--primary-color) !important;
    font-weight: 800;
}

.search-form {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    position: relative;
}

.search-form input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid rgba(139, 92, 246, 0.3);
    border-radius: 1rem;
    font-size: 1.125rem;
    font-weight: 500;
    background: white;
    color: #1f2937;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 1;
}

.search-form input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 4px rgba(139, 92, 246, 0.1);
    transform: translateY(-2px);
}

.search-form input::placeholder {
    color: #6b7280;
    font-weight: 400;
}

.btn-search {
    background: var(--gradient-primary);
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 1rem;
    font-weight: 600;
    font-size: 1.125rem;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-md);
    white-space: nowrap;
}

.btn-search:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.search-result {
    padding: 1.25rem;
    border-radius: 1rem;
    margin-top: 1.5rem;
    text-align: center;
    font-weight: 600;
    font-size: 1.125rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.search-result.available {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border: 2px solid #10b981;
}

.search-result.taken {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border: 2px solid #ef4444;
}

.search-result.error {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border: 2px solid #f59e0b;
}

/* Modern Sections */
.features-section {
    padding: 6rem 0;
    background: var(--bg-secondary);
    position: relative;
}

.features-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, var(--border-color), transparent);
}

.section-title {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.section-title h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-title p {
    font-size: 1.25rem;
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
}

/* Modern Feature Cards */
.feature-card {
    background: white;
    padding: 2.5rem;
    border-radius: 1.5rem;
    box-shadow: var(--shadow-md);
    text-align: center;
    height: 100%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    font-size: 2rem;
    color: white;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1) rotate(5deg);
}

.feature-card h4 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.feature-card p {
    color: var(--text-light);
    line-height: 1.7;
    font-size: 1.125rem;
}

/* Modern CTA Section */
.cta-section {
    background: var(--bg-dark);
    color: white;
    padding: 6rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 50%, var(--bg-dark) 100%);
    opacity: 0.9;
}

.cta-section .container {
    position: relative;
    z-index: 2;
}

.cta-section h2 {
    font-size: clamp(2rem, 4vw, 3rem);
    font-weight: 800;
    margin-bottom: 1.5rem;
    color: #ffffff;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.cta-section p {
    font-size: 1.25rem;
    margin-bottom: 3rem;
    color: rgba(255, 255, 255, 0.95);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.btn-cta {
    background: var(--gradient-accent);
    color: var(--text-dark);
    padding: 1.25rem 2.5rem;
    border-radius: 1rem;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.25rem;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-xl);
    border: none;
    cursor: pointer;
}

.btn-cta:hover {
    transform: translateY(-4px) scale(1.05);
    box-shadow: 0 20px 40px rgba(245, 158, 11, 0.4);
    color: var(--text-dark);
    text-decoration: none;
}

/* Footer */
.landing-footer {
    background: var(--text-dark);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    margin-bottom: 2rem;
}

.footer-section h5 {
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
    padding: 0;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

/* Modern Form Styles */
.form-control {
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
    transform: translateY(-1px);
}

.form-control::placeholder {
    color: var(--text-lighter);
    font-weight: 400;
}

.form-label {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.form-select {
    padding: 1rem 1.5rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    font-size: 1rem;
    font-weight: 500;
    background: white;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
}

.form-select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 4px rgba(99, 102, 241, 0.1);
}

/* Alert Styles */
.alert {
    padding: 1.25rem 1.5rem;
    border-radius: 1rem;
    border: none;
    font-weight: 500;
    box-shadow: var(--shadow-md);
    margin-bottom: 1.5rem;
}

.alert-success {
    background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
    color: #065f46;
    border-left: 4px solid #10b981;
}

.alert-danger {
    background: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
    color: #991b1b;
    border-left: 4px solid #ef4444;
}

.alert-warning {
    background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
    color: #92400e;
    border-left: 4px solid #f59e0b;
}

.alert-info {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    color: #1e40af;
    border-left: 4px solid #3b82f6;
}

/* Contact Page Specific */
.contact-item {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 1rem;
}

.contact-item:hover {
    background: var(--bg-tertiary);
    transform: translateX(5px);
}

.copy-btn {
    transition: all 0.3s ease;
}

.copy-btn:hover {
    transform: scale(1.1);
}

/* Pricing Cards */
.pricing-card {
    background: white;
    border-radius: 2rem;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid var(--border-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.pricing-card.popular {
    border-color: var(--cpanel-orange);
    transform: scale(1.05);
    box-shadow: 0 20px 40px rgba(255, 108, 44, 0.15);
}

.pricing-card.popular::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--cpanel-orange) 0%, var(--cpanel-orange-light) 100%);
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.pricing-card.popular:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 30px 60px rgba(255, 108, 44, 0.2);
}

.pricing-card .text-primary {
    color: var(--cpanel-orange) !important;
}

.pricing-card .btn-primary {
    background: linear-gradient(135deg, var(--cpanel-orange) 0%, var(--cpanel-orange-dark) 100%);
    border-color: var(--cpanel-orange);
}

.pricing-card .btn-primary:hover {
    background: linear-gradient(135deg, var(--cpanel-orange-dark) 0%, var(--cpanel-orange) 100%);
    transform: translateY(-2px);
}

.pricing-card .btn-outline-primary {
    color: var(--cpanel-orange);
    border-color: var(--cpanel-orange);
}

.pricing-card .btn-outline-primary:hover {
    background: var(--cpanel-orange);
    border-color: var(--cpanel-orange);
    color: white;
}

/* About Page Statistics - Orange Theme */
.feature-card .text-primary {
    color: var(--cpanel-orange) !important;
}

.feature-card h3.text-primary {
    color: var(--cpanel-orange) !important;
    font-weight: 900;
    font-size: 2.5rem;
}

/* Contact Page Improvements */
.contact-item {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 1rem;
    margin-bottom: 1.5rem !important;
}

.contact-item:hover {
    background: rgba(255, 108, 44, 0.05);
    transform: translateX(5px);
}

.contact-item h6 {
    color: var(--text-dark) !important;
    font-weight: 700;
}

.contact-item .text-muted {
    color: #6b7280 !important;
}

/* Fix contact page button colors */
.contact-item .btn-outline-primary {
    color: var(--cpanel-orange);
    border-color: var(--cpanel-orange);
}

.contact-item .btn-outline-primary:hover {
    background: var(--cpanel-orange);
    border-color: var(--cpanel-orange);
    color: white;
}

.contact-item .btn-outline-success {
    color: #10b981;
    border-color: #10b981;
}

.contact-item .btn-outline-success:hover {
    background: #10b981;
    border-color: #10b981;
    color: white;
}

/* Improve contact card spacing */
.feature-card {
    margin-bottom: 2rem;
    padding: 2rem;
}

/* Fix contact page layout on smaller screens */
@media (max-width: 991px) {
    .contact-item {
        margin-bottom: 2rem !important;
    }

    .feature-card {
        margin-bottom: 2rem;
    }
}

/* Fix Quick Links card spacing and layout */
.feature-card .d-grid {
    gap: 1rem !important;
}

.feature-card .btn {
    padding: 1rem;
    text-align: left;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.feature-card .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Ensure proper spacing between contact cards */
.col-lg-4 .feature-card {
    margin-bottom: 2rem;
    height: fit-content;
}

/* Fix footer text colors */
.landing-footer {
    background: var(--text-dark);
    color: rgba(255, 255, 255, 0.9) !important;
}

.landing-footer h5 {
    color: #ffffff !important;
    font-weight: 600;
}

.landing-footer ul li a {
    color: rgba(255, 255, 255, 0.8) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.landing-footer ul li a:hover {
    color: var(--cpanel-orange) !important;
}

.footer-bottom {
    color: rgba(255, 255, 255, 0.7) !important;
}

/* Policy Pages Layout */
.policy-card {
    background: white;
    padding: 2.5rem;
    border-radius: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
    border: 1px solid var(--border-light);
    position: relative;
    z-index: 1;
}

.policy-content {
    margin-bottom: 4rem;
    padding-bottom: 4rem;
}

.policy-content h2 {
    color: var(--text-dark);
    border-bottom: 2px solid var(--cpanel-orange);
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.policy-content h3 {
    color: var(--text-dark);
    margin-top: 1.5rem;
    margin-bottom: 1rem;
}

/* Fix main content area for all pages */
main {
    min-height: calc(100vh - 300px);
    padding-bottom: 4rem;
    margin-top: 80px; /* Account for fixed header */
}

/* Ensure footer stays at bottom */
body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.landing-footer {
    margin-top: auto;
    position: relative;
    z-index: 10;
}

/* Additional Contact Page Fixes */
.contact-item .text-primary {
    color: var(--cpanel-orange) !important;
}

.contact-item .text-success {
    color: #10b981 !important;
}

.contact-item .text-danger {
    color: #ef4444 !important;
}

/* Ensure proper text colors in contact cards */
.feature-card h3,
.feature-card h4,
.feature-card h5,
.feature-card h6 {
    color: var(--text-dark) !important;
}

.feature-card .text-muted {
    color: #6b7280 !important;
}

/* Fix button spacing in contact cards */
.feature-card .btn {
    margin-bottom: 0.5rem;
}

.feature-card .d-grid .btn {
    margin-bottom: 0;
}

/* Improve contact form styling */
.form-control:focus {
    border-color: var(--cpanel-orange);
    box-shadow: 0 0 0 0.2rem rgba(255, 108, 44, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, var(--cpanel-orange) 0%, var(--cpanel-orange-dark) 100%);
    border-color: var(--cpanel-orange);
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--cpanel-orange-dark) 0%, var(--cpanel-orange) 100%);
    border-color: var(--cpanel-orange-dark);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-section {
        padding: 8rem 0 4rem;
    }

    .search-form {
        flex-direction: column;
    }

    .btn-search {
        width: 100%;
    }

    .navbar-collapse {
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        padding: 1.5rem;
        border-radius: 1rem;
        margin-top: 1rem;
        box-shadow: var(--shadow-xl);
        border: 1px solid var(--border-light);
    }

    .feature-card {
        margin-bottom: 2rem;
    }

    .price-highlight {
        margin: 2rem 0;
        padding: 1.5rem;
    }

    .domain-search {
        margin-top: 2rem;
        padding: 2rem;
    }
}

@media (max-width: 576px) {
    .hero-section {
        padding: 6rem 0 3rem;
    }

    .section-title {
        margin-bottom: 3rem;
    }

    .features-section,
    .cta-section {
        padding: 4rem 0;
    }

    .btn-cta {
        padding: 1rem 2rem;
        font-size: 1.125rem;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Additional Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Utility Classes */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

/* Modern Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}

/* Selection Color */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* Focus Styles */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Modern Badge */
.badge {
    font-weight: 600;
    letter-spacing: 0.025em;
}

/* Modern Card Hover Effects */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Glass Effect */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient Text */
.gradient-text {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Shadows */
.shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.08);
}

.shadow-colored {
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
}

/* Improved Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --text-dark: #000000;
        --text-light: #333333;
        --border-color: #000000;
    }
}
