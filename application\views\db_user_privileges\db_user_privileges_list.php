<!doctype html>
<html>
    <head>
        <title>harviacode.com - codeigniter crud generator</title>
        <link rel="stylesheet" href="<?php echo base_url('assets/bootstrap/css/bootstrap.min.css') ?>"/>
        <style>
            body{
                padding: 15px;
            }
        </style>
    </head>
    <body>
        <h2 style="margin-top:0px">Db_user_privileges List</h2>
        <div class="row" style="margin-bottom: 10px">
            <div class="col-md-4">
                <?php echo anchor(site_url('db_user_privileges/create'),'Create', 'class="btn btn-primary"'); ?>
            </div>
            <div class="col-md-4 text-center">
                <div style="margin-top: 8px" id="message">
                    <?php echo $this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''; ?>
                </div>
            </div>
            <div class="col-md-1 text-right">
            </div>
            <div class="col-md-3 text-right">
                <form action="<?php echo site_url('db_user_privileges/index'); ?>" class="form-inline" method="get">
                    <div class="input-group">
                        <input type="text" class="form-control" name="q" value="<?php echo $q; ?>">
                        <span class="input-group-btn">
                            <?php 
                                if ($q <> '')
                                {
                                    ?>
                                    <a href="<?php echo site_url('db_user_privileges'); ?>" class="btn btn-default">Reset</a>
                                    <?php
                                }
                            ?>
                          <button class="btn btn-primary" type="submit">Search</button>
                        </span>
                    </div>
                </form>
            </div>
        </div>
        <table class="table table-bordered" style="margin-bottom: 10px">
            <tr>
                <th>No</th>
		<th>P Id</th>
		<th>User</th>
		<th>Company Id</th>
		<th>Privilege</th>
		<th>Date Added</th>
		<th>Action</th>
            </tr><?php
            foreach ($db_user_privileges_data as $db_user_privileges)
            {
                ?>
                <tr>
			<td width="80px"><?php echo ++$start ?></td>
			<td><?php echo $db_user_privileges->p_id ?></td>
			<td><?php echo $db_user_privileges->user ?></td>
			<td><?php echo $db_user_privileges->company_id ?></td>
			<td><?php echo $db_user_privileges->privilege ?></td>
			<td><?php echo $db_user_privileges->date_added ?></td>
			<td style="text-align:center" width="200px">
				<?php 
				echo anchor(site_url('db_user_privileges/read/'.$db_user_privileges->),'Read'); 
				echo ' | '; 
				echo anchor(site_url('db_user_privileges/update/'.$db_user_privileges->),'Update'); 
				echo ' | '; 
				echo anchor(site_url('db_user_privileges/delete/'.$db_user_privileges->),'Delete','onclick="javasciprt: return confirm(\'Are You Sure ?\')"'); 
				?>
			</td>
		</tr>
                <?php
            }
            ?>
        </table>
        <div class="row">
            <div class="col-md-6">
                <a href="#" class="btn btn-primary">Total Record : <?php echo $total_rows ?></a>
	    </div>
            <div class="col-md-6 text-right">
                <?php echo $pagination ?>
            </div>
        </div>
    </body>
</html>