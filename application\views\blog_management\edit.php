<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <!-- Card start -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title d-flex justify-content-between align-items-center">
                        <h4>Edit Blog Post</h4>
                        <div>
                            <?php if ($post->status === 'published'): ?>
                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="btn btn-outline-primary me-2" target="_blank">
                                    <i class="icon-eye me-2"></i>View Post
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo base_url('Blog_management'); ?>" class="btn btn-outline-secondary">
                                <i class="icon-arrow-left"></i> Back to Posts
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">

                    <?php if (validation_errors()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo validation_errors(); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($this->session->flashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('error'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post" enctype="multipart/form-data">
                        <div class="row">
                            <!-- Main Content -->
                            <div class="col-lg-8">
                                <!-- Title -->
                                <div class="field-wrapper">
                                    <input type="text" name="title" value="<?php echo set_value('title', $post->title); ?>" required>
                                    <div class="field-placeholder">Post Title *</div>
                                </div>

                                <!-- Content -->
                                <div class="field-wrapper">
                                    <label class="form-label">Content *</label>
                                    <textarea id="content" name="content" rows="15" class="form-control"><?php echo set_value('content', $post->content); ?></textarea>
                                </div>

                                <!-- Excerpt -->
                                <div class="field-wrapper">
                                    <textarea name="excerpt" rows="3" placeholder="Optional short description..."><?php echo set_value('excerpt', $post->excerpt); ?></textarea>
                                    <div class="field-placeholder">Excerpt</div>
                                    <small class="form-text text-muted">Leave empty to auto-generate from content.</small>
                                </div>

                                <!-- SEO Settings -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5><i class="icon-search me-2"></i>SEO Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="field-wrapper">
                                            <input type="text" name="meta_title" value="<?php echo set_value('meta_title', $post->meta_title); ?>" maxlength="60">
                                            <div class="field-placeholder">Meta Title</div>
                                            <small class="form-text text-muted">Leave empty to use post title. Recommended: 50-60 characters.</small>
                                        </div>

                                        <div class="field-wrapper">
                                            <textarea name="meta_description" rows="2" maxlength="160"><?php echo set_value('meta_description', $post->meta_description); ?></textarea>
                                            <div class="field-placeholder">Meta Description</div>
                                            <small class="form-text text-muted">Recommended: 150-160 characters.</small>
                                        </div>

                                        <div class="field-wrapper">
                                            <input type="text" name="meta_keywords" value="<?php echo set_value('meta_keywords', $post->meta_keywords); ?>">
                                            <div class="field-placeholder">Meta Keywords</div>
                                            <small class="form-text text-muted">Comma-separated keywords (optional).</small>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sidebar -->
                            <div class="col-lg-4">
                                <!-- Publish Settings -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="icon-settings me-2"></i>Publish Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="field-wrapper">
                                            <select name="status" class="form-select">
                                                <option value="draft" <?php echo set_select('status', 'draft', $post->status === 'draft'); ?>>Draft</option>
                                                <option value="published" <?php echo set_select('status', 'published', $post->status === 'published'); ?>>Published</option>
                                                <option value="archived" <?php echo set_select('status', 'archived', $post->status === 'archived'); ?>>Archived</option>
                                            </select>
                                            <div class="field-placeholder">Status</div>
                                        </div>

                                        <div class="d-grid gap-2 mt-3">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="icon-check me-2"></i>Update Post
                                            </button>
                                            <a href="<?php echo base_url('Blog_management'); ?>" class="btn btn-outline-secondary">
                                                Cancel
                                            </a>
                                        </div>
                                    </div>
                                </div>

                                <!-- Category -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="icon-folder me-2"></i>Category</h5>
                                    </div>
                                    <div class="card-body">
                                        <select name="category_id" class="form-select">
                                            <option value="">Select Category</option>
                                            <?php foreach ($categories as $category): ?>
                                                <option value="<?php echo $category->id; ?>"
                                                        <?php echo set_select('category_id', $category->id, $post->category_id == $category->id); ?>>
                                                    <?php echo htmlspecialchars($category->name); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                </div>

                                <!-- Tags -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="icon-tag me-2"></i>Tags</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="field-wrapper">
                                            <input type="text" name="tags" value="<?php echo set_value('tags', $post->tags); ?>"
                                                   placeholder="Enter tags separated by commas">
                                            <div class="field-placeholder">Tags</div>
                                            <small class="form-text text-muted">e.g., email hosting, tutorial, business</small>
                                        </div>
                                    </div>
                                </div>

                                <!-- Featured Image -->
                                <div class="card mb-4">
                                    <div class="card-header">
                                        <h5><i class="icon-image me-2"></i>Featured Image</h5>
                                    </div>
                                    <div class="card-body">
                                        <?php if ($post->featured_image): ?>
                                            <div class="mb-3">
                                                <label class="form-label">Current Image:</label>
                                                <div class="current-image">
                                                    <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>"
                                                         alt="Current featured image" class="img-fluid rounded"
                                                         style="max-width: 200px; max-height: 150px; border: 2px solid #e5e7eb; padding: 0.5rem;">
                                                </div>
                                            </div>
                                        <?php endif; ?>

                                        <input type="file" name="featured_image" class="form-control"
                                               accept="image/*" onchange="previewImage(this)">
                                        <small class="form-text text-muted">
                                            <?php if ($post->featured_image): ?>
                                                Upload a new image to replace the current one.
                                            <?php else: ?>
                                                Recommended: 1200x630px, max 2MB
                                            <?php endif; ?>
                                        </small>
                                        <div id="imagePreview" class="mt-3"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                </div>
            </div>
            <!-- Card end -->

        </div>
    </div>
    <!-- Row end -->

</div>
<!-- Content wrapper end -->

<!-- CKEditor (Free WYSIWYG) -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>
<script>
// Initialize CKEditor
ClassicEditor
    .create(document.querySelector('#content'), {
        toolbar: {
            items: [
                'heading', '|',
                'bold', 'italic', 'link', '|',
                'bulletedList', 'numberedList', '|',
                'outdent', 'indent', '|',
                'imageUpload', 'blockQuote', 'insertTable', '|',
                'undo', 'redo'
            ]
        },
        language: 'en',
        image: {
            toolbar: [
                'imageTextAlternative',
                'imageStyle:full',
                'imageStyle:side'
            ]
        },
        table: {
            contentToolbar: [
                'tableColumn',
                'tableRow',
                'mergeTableCells'
            ]
        }
    })
    .catch(error => {
        console.error(error);
    });

// Image preview
function previewImage(input) {
    const preview = document.getElementById('imagePreview');
    preview.innerHTML = '';

    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const img = document.createElement('img');
            img.src = e.target.result;
            img.className = 'img-fluid rounded';
            img.style.maxWidth = '200px';
            img.style.maxHeight = '150px';
            img.style.border = '2px dashed #d1d5db';
            img.style.padding = '0.5rem';
            preview.appendChild(img);
        };
        reader.readAsDataURL(input.files[0]);
    }
}
</script>

<style>
.field-wrapper {
    margin-bottom: 1.5rem;
}

.card {
    margin-bottom: 1rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

#imagePreview img {
    border-radius: 0.5rem;
}

.current-image {
    border-radius: 0.5rem;
}
</style>
