<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

require APPPATH . '/libraries/CpanelApi.php';
class Sub_domains extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->confi = config_cpanel();
        $this->cpanel = new cpanelAPI($this->confi['servername'],$this->confi['password'],$this->confi['ip']);
        $this->load->model('Sub_domains_model');
		$this->load->model('Company_domains_model');
		$this->load->model('Company_model');
        $this->load->library('form_validation');
    }
	public function my_subdomains(){
		$data['data'] = $this->Sub_domains_model->get_my_subdomains($this->session->userdata('company_id'));
		$config['active_menu'] = "domains";
		$config['current_link'] = "sub_domains";
		$this->load->view('header',$config);
		$this->load->view('my_subdomains',$data);
		$this->load->view('footer');
	}
    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'sub_domains/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'sub_domains/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'sub_domains/index.html';
            $config['first_url'] = base_url() . 'sub_domains/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Sub_domains_model->total_rows($q);
        $sub_domains = $this->Sub_domains_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'sub_domains_data' => $sub_domains,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('sub_domains/sub_domains_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Sub_domains_model->get_by_id($id);
        if ($row) {
            $data = array(
		'sub_domain_id' => $row->sub_domain_id,
		'name' => $row->name,
		'domain' => $row->domain,
		'dir' => $row->dir,
		'company' => $row->company,
		'sub_domain_stamp' => $row->sub_domain_stamp,
	    );
            $this->load->view('sub_domains/sub_domains_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('sub_domains'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('sub_domains/create_action'),
	    'sub_domain_id' => set_value('sub_domain_id'),
	    'name' => set_value('name'),
	    'domain' => set_value('domain'),
	    'dir' => set_value('dir'),
	    'company' => set_value('company'),
	    'sub_domain_stamp' => set_value('sub_domain_stamp'),
	);
		$config['active_menu'] = "domains";
		$config['current_link'] = "sub_domains";
		$this->load->view('header',$config);
        $this->load->view('sub_domains/sub_domains_form', $data);
		$this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'name' => $this->input->post('name',TRUE),
		'domain' => $this->input->post('domain',TRUE),
		'dir' => $this->input->post('dir',TRUE),
		'company' => $this->session->userdata('company_id'),

	    );
			$response = $this->cpanel->uapi->SubDomain->addsubdomain( array (
				'domain' => $data['name'],
				'rootdomain' => $data['domain'],
				'dir' => '/home3/malawim2/public_html/'.$this->input->post('fullpath').'/'.$data['dir'],
			));
if ($response->status == 0){

$this->session->set_flashdata('error',$response->errors);
redirect($_SERVER["HTTP_REFERER"]);
}else{
	$this->Sub_domains_model->insert($data);
	$this->toaster->success('Success !, subdomain  was added successfully');
	redirect(site_url('my_subdomains'));
}

        }
    }
    


    
    public function delete($id) 
    {
        $row = $this->Sub_domains_model->get_by_id($id);

        if ($row) {
            $this->Sub_domains_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('sub_domains'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('sub_domains'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('name', 'name', 'trim|required');
	$this->form_validation->set_rules('domain', 'domain', 'trim|required');
	$this->form_validation->set_rules('dir', 'dir', 'trim|required');


	$this->form_validation->set_rules('sub_domain_id', 'sub_domain_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}


