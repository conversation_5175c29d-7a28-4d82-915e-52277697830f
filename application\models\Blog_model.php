<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Blog_model extends CI_Model {

    public function __construct() {
        parent::__construct();
        $this->load->database();
    }

    // ========== BLOG POSTS ==========

    /**
     * Get all published blog posts with pagination
     */
    public function get_published_posts($limit = 10, $offset = 0, $category_id = null) {
        $this->db->select('bp.*, bc.name as category_name, bc.slug as category_slug, u.full_name as author_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->join('users u', 'bp.author_id = u.user_id', 'left');
        $this->db->where('bp.status', 'published');

        if ($category_id) {
            $this->db->where('bp.category_id', $category_id);
        }

        $this->db->order_by('bp.published_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result();
    }

    /**
     * Get total count of published posts
     */
    public function count_published_posts($category_id = null) {
        $this->db->from('blog_posts');
        $this->db->where('status', 'published');

        if ($category_id) {
            $this->db->where('category_id', $category_id);
        }

        return $this->db->count_all_results();
    }

    /**
     * Get single blog post by slug
     */
    public function get_post_by_slug($slug) {
        $this->db->select('bp.*, bc.name as category_name, bc.slug as category_slug, u.full_name as author_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->join('users u', 'bp.author_id = u.user_id', 'left');
        $this->db->where('bp.slug', $slug);
        $this->db->where('bp.status', 'published');

        return $this->db->get()->row();
    }

    /**
     * Get post tags
     */
    public function get_post_tags($post_id) {
        $this->db->select('bt.*');
        $this->db->from('blog_tags bt');
        $this->db->join('blog_post_tags bpt', 'bt.id = bpt.tag_id');
        $this->db->where('bpt.post_id', $post_id);

        return $this->db->get()->result();
    }

    /**
     * Increment post views
     */
    public function increment_views($post_id) {
        $this->db->set('views', 'views + 1', FALSE);
        $this->db->where('id', $post_id);
        $this->db->update('blog_posts');
    }

    /**
     * Get related posts
     */
    public function get_related_posts($post_id, $category_id, $limit = 3) {
        $this->db->select('bp.*, bc.name as category_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->where('bp.status', 'published');
        $this->db->where('bp.id !=', $post_id);
        $this->db->where('bp.category_id', $category_id);
        $this->db->order_by('bp.published_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result();
    }

    /**
     * Search posts
     */
    public function search_posts($query, $limit = 10, $offset = 0) {
        $this->db->select('bp.*, bc.name as category_name, u.full_name as author_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->join('users u', 'bp.author_id = u.user_id', 'left');
        $this->db->where('bp.status', 'published');
        $this->db->group_start();
        $this->db->like('bp.title', $query);
        $this->db->or_like('bp.content', $query);
        $this->db->or_like('bp.excerpt', $query);
        $this->db->group_end();
        $this->db->order_by('bp.published_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result();
    }

    // ========== CATEGORIES ==========

    /**
     * Get all categories with post counts
     */
    public function get_categories() {
        $this->db->select('bc.*, COUNT(bp.id) as post_count');
        $this->db->from('blog_categories bc');
        $this->db->join('blog_posts bp', 'bc.id = bp.category_id AND bp.status = "published"', 'left');
        $this->db->group_by('bc.id');
        $this->db->order_by('bc.name', 'ASC');
        return $this->db->get()->result();
    }

    /**
     * Get category by slug
     */
    public function get_category_by_slug($slug) {
        $this->db->where('slug', $slug);
        return $this->db->get('blog_categories')->row();
    }

    // ========== TAGS ==========

    /**
     * Get popular tags
     */
    public function get_popular_tags($limit = 20) {
        $this->db->select('bt.*, COUNT(bpt.tag_id) as post_count');
        $this->db->from('blog_tags bt');
        $this->db->join('blog_post_tags bpt', 'bt.id = bpt.tag_id');
        $this->db->group_by('bt.id');
        $this->db->order_by('post_count', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result();
    }

    /**
     * Get posts by tag
     */
    public function get_posts_by_tag($tag_slug, $limit = 10, $offset = 0) {
        $this->db->select('bp.*, bc.name as category_name, u.full_name as author_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->join('users u', 'bp.author_id = u.user_id', 'left');
        $this->db->join('blog_post_tags bpt', 'bp.id = bpt.post_id');
        $this->db->join('blog_tags bt', 'bpt.tag_id = bt.id');
        $this->db->where('bp.status', 'published');
        $this->db->where('bt.slug', $tag_slug);
        $this->db->order_by('bp.published_at', 'DESC');
        $this->db->limit($limit, $offset);

        return $this->db->get()->result();
    }

    // ========== RECENT POSTS ==========

    /**
     * Get recent posts for sidebar
     */
    public function get_recent_posts($limit = 5) {
        $this->db->select('id, title, slug, published_at, featured_image');
        $this->db->from('blog_posts');
        $this->db->where('status', 'published');
        $this->db->order_by('published_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result();
    }

    /**
     * Get popular posts (by views)
     */
    public function get_popular_posts($limit = 5) {
        $this->db->select('id, title, slug, views, published_at, featured_image');
        $this->db->from('blog_posts');
        $this->db->where('status', 'published');
        $this->db->order_by('views', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result();
    }

    // ========== COMMENTS ==========

    /**
     * Get approved comments for a post
     */
    public function get_post_comments($post_id) {
        $this->db->where('post_id', $post_id);
        $this->db->where('status', 'approved');
        $this->db->where('parent_id IS NULL');
        $this->db->order_by('created_at', 'ASC');

        $comments = $this->db->get('blog_comments')->result();

        // Get replies for each comment
        foreach ($comments as $comment) {
            $comment->replies = $this->get_comment_replies($comment->id);
        }

        return $comments;
    }

    /**
     * Get comment replies
     */
    private function get_comment_replies($parent_id) {
        $this->db->where('parent_id', $parent_id);
        $this->db->where('status', 'approved');
        $this->db->order_by('created_at', 'ASC');

        return $this->db->get('blog_comments')->result();
    }

    /**
     * Add new comment
     */
    public function add_comment($data) {
        return $this->db->insert('blog_comments', $data);
    }

    /**
     * Count comments for a post
     */
    public function count_post_comments($post_id) {
        $this->db->where('post_id', $post_id);
        $this->db->where('status', 'approved');
        return $this->db->count_all_results('blog_comments');
    }

    // ========== ADDITIONAL METHODS ==========

    /**
     * Increment post views
     */
    public function increment_views($post_id) {
        $this->db->set('views', 'views + 1', FALSE);
        $this->db->where('id', $post_id);
        return $this->db->update('blog_posts');
    }

    /**
     * Get post tags
     */
    public function get_post_tags($post_id) {
        $this->db->select('bt.name, bt.slug');
        $this->db->from('blog_tags bt');
        $this->db->join('blog_post_tags bpt', 'bt.id = bpt.tag_id');
        $this->db->where('bpt.post_id', $post_id);
        return $this->db->get()->result();
    }

    /**
     * Get related posts
     */
    public function get_related_posts($post_id, $category_id, $limit = 3) {
        $this->db->select('id, title, slug, featured_image, published_at');
        $this->db->from('blog_posts');
        $this->db->where('status', 'published');
        $this->db->where('id !=', $post_id);

        if ($category_id) {
            $this->db->where('category_id', $category_id);
        }

        $this->db->order_by('published_at', 'DESC');
        $this->db->limit($limit);

        return $this->db->get()->result();
    }
}
