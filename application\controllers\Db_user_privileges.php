<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Db_user_privileges extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Db_user_privileges_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'db_user_privileges/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'db_user_privileges/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'db_user_privileges/index.html';
            $config['first_url'] = base_url() . 'db_user_privileges/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Db_user_privileges_model->total_rows($q);
        $db_user_privileges = $this->Db_user_privileges_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'db_user_privileges_data' => $db_user_privileges,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('db_user_privileges/db_user_privileges_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Db_user_privileges_model->get_by_id($id);
        if ($row) {
            $data = array(
		'p_id' => $row->p_id,
		'user' => $row->user,
		'company_id' => $row->company_id,
		'privilege' => $row->privilege,
		'date_added' => $row->date_added,
	    );
            $this->load->view('db_user_privileges/db_user_privileges_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('db_user_privileges'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('db_user_privileges/create_action'),
	    'p_id' => set_value('p_id'),
	    'user' => set_value('user'),
	    'company_id' => set_value('company_id'),
	    'privilege' => set_value('privilege'),
	    'date_added' => set_value('date_added'),
	);
        $this->load->view('db_user_privileges/db_user_privileges_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'p_id' => $this->input->post('p_id',TRUE),
		'user' => $this->input->post('user',TRUE),
		'company_id' => $this->input->post('company_id',TRUE),
		'privilege' => $this->input->post('privilege',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Db_user_privileges_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('db_user_privileges'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Db_user_privileges_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('db_user_privileges/update_action'),
		'p_id' => set_value('p_id', $row->p_id),
		'user' => set_value('user', $row->user),
		'company_id' => set_value('company_id', $row->company_id),
		'privilege' => set_value('privilege', $row->privilege),
		'date_added' => set_value('date_added', $row->date_added),
	    );
            $this->load->view('db_user_privileges/db_user_privileges_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('db_user_privileges'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('', TRUE));
        } else {
            $data = array(
		'p_id' => $this->input->post('p_id',TRUE),
		'user' => $this->input->post('user',TRUE),
		'company_id' => $this->input->post('company_id',TRUE),
		'privilege' => $this->input->post('privilege',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Db_user_privileges_model->update($this->input->post('', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('db_user_privileges'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Db_user_privileges_model->get_by_id($id);

        if ($row) {
            $this->Db_user_privileges_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('db_user_privileges'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('db_user_privileges'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('p_id', 'p id', 'trim|required');
	$this->form_validation->set_rules('user', 'user', 'trim|required');
	$this->form_validation->set_rules('company_id', 'company id', 'trim|required');
	$this->form_validation->set_rules('privilege', 'privilege', 'trim|required');
	$this->form_validation->set_rules('date_added', 'date added', 'trim|required');

	$this->form_validation->set_rules('', '', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Db_user_privileges.php */
/* Location: ./application/controllers/Db_user_privileges.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-23 05:44:18 */
/* http://harviacode.com */