<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <div class="card">
                <div class="card-body">
                    <a href="<?php echo base_url('email_forwarder/create')?>" class="btn btn-primary">Create email forwarder</a>
                    <br><br>
                    <div class="table-responsive">
                       <table id="copy-print-csv" class="table v-middle">
                        <thead>
            <tr>
                <th>No</th>

		<th>Forwarder</th>
		<th>Forwarded To</th>
		<th>Date Added</th>
		<th>Action</th>
            </tr>
                        </thead>
                           <tbody>
            <?php
            $start = 0;
            foreach ($email_forwarder_data as $email_forwarder)
            {
                ?>
                <tr>
			<td width="80px"><?php echo ++$start ?></td>

			<td><?php echo $email_forwarder->forwarder ?></td>
			<td><?php echo $email_forwarder->forwarded_to ?></td>
			<td><?php echo $email_forwarder->date_added ?></td>
			<td style="text-align:center" width="200px">
				<?php 

				echo anchor(site_url('email_forwarder/delete/'.$email_forwarder->email_forwarder_id),'Delete','onclick="javasciprt: return confirm(\'Are You Sure you want to delete ?\')"');
				?>
			</td>
		</tr>
                <?php
            }
            ?>
                           </tbody>
        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
