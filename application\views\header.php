<?php
$settings = get_by_id('cpanel_settings','id','1');
if(!$this->session->userdata('user_id')){
	redirect('Login');
}
?>
<!doctype html>
<html lang="en">
<head>
	<!-- Required meta tags -->
	<meta charset="utf-8">
	<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

	<!-- Meta -->
    <meta name="description" content="<?php echo $settings->company_name;?>">
    <meta name="author" content="<?php echo $settings->company_name;?>">
    <link rel="shortcut icon" href="<?php echo base_url('cpanel_assets/')?>img/fav.png" />

    <!-- Title -->
    <title><?php echo $settings->company_name;?></title>


	<!-- *************
		************ Common Css Files *************
	************ -->
	<!-- Bootstrap css -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>css/bootstrap.min.css">

	<!-- Icomoon Font Icons css -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>fonts/style.css">

	<!-- Main css -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>css/main.css">
	<link rel="stylesheet" href="<?php echo base_url('assets/')?>css/select2.css">
    <style>
        .home_menu{
            border-radius: 9px; border: thin solid red;  background-color: white; color: black; padding: 12px;
        }
        .home_menu .hove {

            --color: #333;
            --size: 16px;
            position: absolute;
            top: 0; right: 0; bottom: 0; left: 0;
            display: flex;

            align-items: center;
            text-align: center;
            padding: 1em;

        }
        .reddot {
            height: 20px;
            width: 20px;
            background-color: red;
            border-radius: 50%;
            display: inline-block;
             }
        .greendot {
            height: 20px;
            width: 20px;
            background-color: green;
            border-radius: 50%;
            display: inline-block;
                }
        .yellowdot {
            height: 20px;
            width: 20px;
            background-color: orangered;
            border-radius: 50%;
            display: inline-block;
        }
    </style>
    <style>

        .card {
            background-color: #fff;
            border-radius: 10px;
            border: none;
            position: relative;
            margin-bottom: 30px;
            box-shadow: 0 0.46875rem 2.1875rem rgba(90,97,105,0.1), 0 0.9375rem 1.40625rem rgba(90,97,105,0.1), 0 0.25rem 0.53125rem rgba(90,97,105,0.12), 0 0.125rem 0.1875rem rgba(90,97,105,0.1);
        }
        .l-bg-cherry {
            background: linear-gradient(to right, #493240, #f09) !important;
            color: #fff;
        }

        .l-bg-blue-dark {
            background: linear-gradient(to right, #373b44, #4286f4) !important;
            color: #fff;
        }

        .l-bg-green-dark {
            background: linear-gradient(to right, #0a504a, #38ef7d) !important;
            color: #fff;
        }

        .l-bg-orange-dark {
            background: linear-gradient(to right, #a86008, #ffba56) !important;
            color: #fff;
        }

        .card .card-statistic-3 .card-icon-large .fas, .card .card-statistic-3 .card-icon-large .far, .card .card-statistic-3 .card-icon-large .fab, .card .card-statistic-3 .card-icon-large .fal {
            font-size: 110px;
        }

        .card .card-statistic-3 .card-icon {
            text-align: center;
            line-height: 50px;
            margin-left: 15px;
            color: #000;
            position: absolute;
            right: -5px;
            top: 20px;
            opacity: 0.1;
        }

        .l-bg-cyan {
            background: linear-gradient(135deg, #289cf5, #84c0ec) !important;
            color: #fff;
        }

        .l-bg-green {
            background: linear-gradient(135deg, #23bdb8 0%, #43e794 100%) !important;
            color: #fff;
        }

        .l-bg-orange {
            background: linear-gradient(to right, #f9900e, #ffba56) !important;
            color: #fff;
        }

        .l-bg-cyan {
            background: linear-gradient(135deg, #289cf5, #84c0ec) !important;
            color: #fff;
        }
    </style>
	<style>
		#regForm {
			background-color: #ffffff;
			margin: 100px auto;
			font-family: Raleway;
			padding: 40px;
			width: 70%;
			min-width: 300px;
		}

		h1 {
			text-align: center;
		}

		input {
			padding: 10px;
			width: 100%;
			font-size: 17px;
			font-family: Raleway;
			border: 1px solid #aaaaaa;
		}

		/* Mark input boxes that gets an error on validation: */
		input.invalid {
			background-color: #ffdddd;
		}

		/* Hide all steps by default: */
		.tab {
			display: none;
		}

		button {
			background-color: #04AA6D;
			color: #ffffff;
			border: none;
			padding: 10px 20px;
			font-size: 17px;
			font-family: Raleway;
			cursor: pointer;
		}

		button:hover {
			opacity: 0.8;
		}

		#prevBtn {
			background-color: #bbbbbb;
		}

		/* Make circles that indicate the steps of the form: */
		.step {
			height: 50px;
			width: 50px;
			margin: 0 2px;
			background-color: #316ac1;
			border: thick solid green;
			border-radius: 50%;
			display: inline-block;
			opacity: 0.5;
			text-align: center;
			font-size: 30px;
			color: white;
		}

		.step.active {
			opacity: 1;
		}

		/* Mark the steps that are finished and valid: */
		.step.finish {
			background-color: #04AA6D;
		}
        input[type="file"] {
            display: none;
        }
        .custom-file-upload {
            border: 1px solid #ccc;
            display: inline-block;
            padding: 6px 12px;
            cursor: pointer;
        }
	</style>

	<!-- *************
		************ Vendor Css Files *************
	************ -->

	<!-- Mega Menu -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>vendor/megamenu/css/megamenu.css">

	<!-- Search Filter JS -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>vendor/search-filter/search-filter.css">
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>vendor/search-filter/custom-search-filter.css">
	<!-- Data Tables -->
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>vendor/datatables/dataTables.bs4.css" />
	<link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>vendor/datatables/dataTables.bs4-custom.css" />
	<link href="<?php echo base_url('cpanel_assets/')?>vendor/datatables/buttons.bs.css" rel="stylesheet" />
	<link href="<?php echo base_url('cpanel_assets/')?>toaster/toastr.min.css" rel="stylesheet" />

	<link rel="stylesheet" type="text/css" media="screen" href="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/themes/smoothness/jquery-ui.css">


	<!-- elFinder CSS (REQUIRED) -->
	<link rel="stylesheet" type="text/css" media="screen" href="<?php echo base_url() ?>/css/elfinder.full.css">
	<link rel="stylesheet" type="text/css" media="screen" href="<?php echo base_url() ?>/css/theme.css">

    <script src="https://www.gstatic.com/charts/loader.js"></script>


</head>
<body>

<!-- Loading wrapper start -->
<div id="loading-wrapper">
	<div class="spinner-border"></div>
	Loading...
</div>
<!-- Loading wrapper end -->

<!-- Page wrapper start -->
<div class="page-wrapper">

	<!-- Sidebar wrapper start -->
	<nav class="sidebar-wrapper">

		<!-- Sidebar content start -->
		<div class="sidebar-tabs">

			<!-- Tabs nav start -->
			<div class="nav" role="tablist" aria-orientation="vertical">
				<a href="#" class="logo">
					<img src="<?php echo base_url('uploads/').$settings->logo?>" alt="Cpanel">
				</a>
				<a  class="nav-link <?php if($active_menu=='dashboard'){echo 'active';}  ?>" id="dash-tab" data-bs-toggle="tab" href="#tab-d" role="tab" aria-controls="tab-d" aria-selected="true">
					<i class="icon-home2"></i>
					<span class="nav-link-text">Dashboard</span>
				</a>

				<?php
				if($this->session->userdata('role')=='1'){

					?>
					<a class="nav-link <?php if($active_menu=='server_config'){echo 'active';}  ?>" id="accounts-tab" data-bs-toggle="tab" href="#tab-accounts" role="tab" aria-controls="tab-accounts" aria-selected="false">
						<i class="icon-activity"></i>
						<span class="nav-link-text">Server config</span>
					</a>

					<?php
				}else{
				?>
				<a class="nav-link <?php if($active_menu=='domains'){echo 'active';}  ?>" id="home-tab" data-bs-toggle="tab" href="#tab-home" role="tab" aria-controls="tab-home" aria-selected="true">
					<i class="icon-globe1"></i>
					<span class="nav-link-text">Domains</span>
				</a>
				<a class="nav-link <?php if($active_menu=='emails'){echo 'active';}  ?>" id="product-tab" data-bs-toggle="tab" href="#tab-product" role="tab" aria-controls="tab-product" aria-selected="false">
					<i class="icon-email"></i>
					<span class="nav-link-text">Emails</span>
				</a>
				<a class="nav-link <?php if($active_menu=='files'){echo 'active';}  ?>" id="pages-tab" data-bs-toggle="tab" href="#tab-pages" role="tab" aria-controls="tab-pages" aria-selected="false">
					<i class="icon-folder"></i>
					<span class="nav-link-text">File Manager</span>
				</a>
				<a class="nav-link <?php if($active_menu=='database'){echo 'active';}  ?>" id="forms-tab" data-bs-toggle="tab" href="#tab-forms" role="tab" aria-controls="tab-forms" aria-selected="false">
					<i class="icon-database"></i>
					<span class="nav-link-text">Databases</span>
				</a>
				<a class="nav-link <?php if($active_menu=='scripts'){echo 'active';}  ?>" id="components-tab" data-bs-toggle="tab" href="#tab-components" role="tab" aria-controls="tab-components" aria-selected="false">
					<i class="icon-activity"></i>
					<span class="nav-link-text">Scripts</span>
				</a>
				<a class="nav-link <?php if($active_menu=='billing'){echo 'active';}  ?>" id="bill-tab" data-bs-toggle="tab" href="#tab-bill" role="tab" aria-controls="tab-bill" aria-selected="false">
					<i class="icon-briefcase"></i>
					<span class="nav-link-text">Billing and Payments</span>
				</a>
                    <a class="nav-link <?php if($active_menu=='company'){echo 'active';}  ?>" id="authentication-tab" data-bs-toggle="tab" href="#tab-authentication" role="tab" aria-controls="tab-authentication" aria-selected="false">
					<i class="icon-settings1"></i>
					<span class="nav-link-text">Company details</span>
				</a>
				<?php

				}
				?>
				<a class="nav-link settings" id="settings-tab" data-bs-toggle="tab" href="#tab-settings" role="tab" aria-controls="tab-authentication" aria-selected="false">
					<i class="icon-user1"></i>
					<span class="nav-link-text">Settings</span>
				</a>
			</div>
			<!-- Tabs nav end -->

			<!-- Tabs content start -->
			<div class="tab-content">

				<!-- Chat tab -->
				<div class="tab-pane fade  <?php if($active_menu=='dashboard'){echo 'show active';}  ?>" id="tab-d" role="tabpanel" aria-labelledby="dash-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Dashboard
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php echo  base_url('Dashboard')?>" class="<?php if($current_link=='dashboard'){echo 'current-page';}  ?>">Dashboard</a>
								</li>

							</ul>

						</div>
					</div>
					<!-- Sidebar menu ends -->



				</div>
				<div class="tab-pane fade  <?php if($active_menu=='domains'){echo 'show active';}  ?>" id="tab-home" role="tabpanel" aria-labelledby="home-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Domains
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php echo  base_url('my_domains')?>" class="<?php if($current_link=='domains'){echo 'current-page';}  ?>">Domains</a>
								</li>
								<li>
									<a href="<?php echo  base_url('my_subdomains')?>" class="<?php if($current_link=='sub_domains'){echo 'current-page';}  ?>">Sub domains</a>
								</li>
								<li>
									<a href="<?php echo  base_url('buy_domains')?>" class="<?php if($current_link=='buy_domains'){echo 'current-page';}  ?>">Buy Domain</a>
								</li>

							</ul>

						</div>
					</div>
					<!-- Sidebar menu ends -->



				</div>

				<!-- Pages tab -->
				<div class="tab-pane fade <?php if($active_menu=='emails'){echo 'show active';}  ?>" id="tab-product" role="tabpanel" aria-labelledby="product-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Emails
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php echo base_url('my_emails') ?>" class="<?php if($current_link=='emails'){echo 'current-page';}  ?>">Email accounts</a>
								</li>
								<li>
									<a href="<?php echo base_url('my_emails/create') ?>" class="<?php if($current_link=='email_create'){echo 'current-page';}  ?>">Create email account</a>
								</li>
								<li>
									<a href="<?php echo base_url('my_email_forwarder') ?>" class="<?php if($current_link=='email_forwarder'){echo 'current-page';}  ?>">Email forwarder</a>
								</li>
<!--								<li>-->
<!--									<a href="--><?php //echo base_url('my_email/auto_responder') ?><!--">Auto responders</a>-->
<!--								</li>-->
<!--								<li>-->
<!--									<a href="--><?php //echo base_url('my_email/mailing_list') ?><!--">Mailing lists</a>-->
<!--								</li>-->

							</ul>

						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile">
							<i class="icon-headphones"></i> 24/7 Support
						</div>
					</div>
					<!-- Sidebar actions ends -->

				</div>

				<!-- Pages tab -->
				<div class="tab-pane fade <?php if($active_menu=='files'){echo 'show active';}  ?>" id="tab-pages" role="tabpanel" aria-labelledby="pages-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Files
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php  echo  base_url('Fmgr/index')?>" class="<?php if($current_link=='files'){echo 'current-page';}  ?>">File manager</a>
								</li>
								<li>
									<a href="<?php  echo  base_url('files/ftp')?>"  class="<?php if($current_link=='ftp'){echo 'current-page';}  ?>">Ftp account</a>
								</li>
<!--								<li>-->
<!--									<a href="--><?php // echo  base_url('files/webdisk')?><!--">Web disk</a>-->
<!--								</li>-->
<!--								<li>-->
<!--									<a href="--><?php // echo  base_url('files/git')?><!--">Git version control</a>-->
<!--								</li>-->

							</ul>
						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile green">
							<i class="icon-pie-chart1"></i> Files and Space
						</div>
					</div>
					<!-- Sidebar actions ends -->

				</div>

				<!-- Forms tab -->
				<div class="tab-pane fade <?php if($active_menu=='database'){echo 'show active';}  ?>" id="tab-forms" role="tabpanel" aria-labelledby="forms-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
					Databases
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li class="list-heading">Database managements</li>
								<li>
									<a href="<?php echo base_url('phpmyadmin')?>"  target="_blank">PhpAdminer</a>
								</li>
								<li>
									<a href="<?php echo base_url('database/list')?>" class="<?php if($current_link=='database'){echo 'current-page';}  ?>">Databases</a>
								</li>
								<li>
									<a href="<?php echo base_url('my_database/create')?>" class="<?php if($current_link=='create_database'){echo 'current-page';}  ?>">Create Database</a>
								</li>
<!--								<li>-->
<!--									<a href="--><?php //echo base_url('database/remote')?><!--">Remote connection</a>-->
<!--								</li>-->

							</ul>

						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile red">
							<i class="icon-database"></i> All about databases
						</div>
					</div>
					<!-- Sidebar actions ends -->

				</div>

				<!-- Components tab -->
				<div class="tab-pane fade" id="tab-components" role="tabpanel" aria-labelledby="components-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Scripts
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php echo base_url('wordpress.php')?>" target="_blank">Wordpress installation</a>
								</li>
<!--								<li>-->
<!--									<a href="--><?php //echo base_url('scripts/cron')?><!--">Cron jobs</a>-->
<!--								</li>-->
<!--								<li>-->
<!--									<a href="--><?php //echo base_url('scripts/bandwidth')?><!--">Bandwidth usage</a>-->
<!--								</li>-->
								<li>
									<a href="<?php echo base_url('scripts/php')?>">PHP version</a>
								</li>

							</ul>
						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile yellow">
							<i class="icon-arrow-down-circle"></i><a href="#">All about scripts</a>						</div>
					</div>
					<!-- Sidebar actions ends -->

				</div>

                <!-- Authentication tab -->
                <div class="tab-pane fade <?php if($active_menu=='billing'){echo 'show active';}  ?>" id="tab-bill" role="tabpanel" aria-labelledby="bill-tab">

                    <!-- Tab content header start -->
                    <div class="tab-pane-header">
                       Payments & Billing
                    </div>
                    <!-- Tab content header end -->

                    <!-- Sidebar menu starts -->
                    <div class="sidebarMenuScroll">
                        <div class="sidebar-menu">
                            <ul>
                                <li class="list-heading">Payments & Billing</li>

                                <li>
                                    <a href="<?php echo base_url('Product_payments/company')?>" class="<?php if($current_link=='co_pay'){echo 'current-page';}  ?>">Payments</a>
                                </li>
                                <li>
                                    <a href="<?php echo base_url('company/billing')?>" class="<?php if($current_link=='billing'){echo 'current-page';}  ?>">Billing and invoices</a>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- Sidebar menu ends -->

                    <!-- Sidebar actions starts -->
                    <div class="sidebar-actions">
                        <div class="support-tile blue">
                            <a href="#" class="btn btn-light m-auto">All about payments and billing</a>
                        </div>
                    </div>
                    <!-- Sidebar actions ends -->

                </div>

				<!-- Authentication tab -->
				<div class="tab-pane fade <?php if($active_menu=='company'){echo 'show active';}  ?>" id="tab-authentication" role="tabpanel" aria-labelledby="authentication-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Company management
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li class="list-heading">Company details</li>
								<li>
									<a href="<?php echo base_url('company/details')?>" class="<?php if($current_link=='company_details'){echo 'current-page';}  ?>">Company information</a>
								</li>
								<li>
									<a href="<?php echo base_url('users/company_users')?>" class="<?php if($current_link=='users'){echo 'current-page';}  ?>">Users</a>
								</li>
								<li>
									<a href="<?php echo base_url('company/billing')?>" class="<?php if($current_link=='billing'){echo 'current-page';}  ?>">Billing and invoices</a>
								</li>
							</ul>
						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile blue">
							<a href="#" class="btn btn-light m-auto">Upgrade Account</a>
						</div>
					</div>
					<!-- Sidebar actions ends -->

				</div>
				<div class="tab-pane fade <?php if($active_menu=='server_config'){echo 'show active';}  ?>" id="tab-accounts" role="tabpanel" aria-labelledby="accounts-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Administration
					</div>
					<!-- Tab content header end -->

					<!-- Sidebar menu starts -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-menu">
							<ul>
								<li>
									<a href="<?php echo base_url('cpanel_settings/update/1')?>" class="<?php if($current_link=='server_config'){echo 'current-page';}  ?>">Hosting server setup</a>
								</li>
								<li>
									<a href="<?php echo base_url('Company')?>" class="<?php if($current_link=='company'){echo 'current-page';}  ?>">Manage customer</a>
								</li><li>
									<a href="<?php echo base_url('Company_domains')?>" class="<?php if($current_link=='c_domains'){echo 'current-page';}  ?>">Manage company domains</a>
								</li>
								<li>
									<a href="<?php echo base_url('Users')?>">Manage client users</a>
								</li>
                                <li>
									<a href="<?php echo base_url('Payment_methods')?>" class="<?php if($current_link=='method'){echo 'current-page';}  ?>">Payment Methods</a>
								</li>
                                <li>
									<a href="<?php echo base_url('Product_payments')?>" class="<?php if($current_link=='payments'){echo 'current-page';}  ?>">Product payments</a>
								</li>
                                <li>
									<a href="<?php echo base_url('Product_life')?>" class="<?php if($current_link=='product_life'){echo 'current-page';}  ?>">Products</a>
								</li>
                                <li>
									<a href="<?php echo base_url('Email_alert')?>" class="<?php if($current_link=='alert'){echo 'current-page';}  ?>">Bulk Email/ alerts</a>
								</li>


							</ul>
						</div>
					</div>
					<!-- Sidebar menu ends -->

					<!-- Sidebar actions starts -->
					<div class="sidebar-actions">
						<div class="support-tile yellow">
							<i class="icon-arrow-down-circle"></i><a href="#">Administration</a></div>
					</div>
					<!-- Sidebar actions ends -->

				</div>

				<!-- Settings tab -->
				<div class="tab-pane fade" id="tab-settings" role="tabpanel" aria-labelledby="settings-tab">

					<!-- Tab content header start -->
					<div class="tab-pane-header">
						Settings
					</div>
					<!-- Tab content header end -->

					<!-- Settings start -->
					<div class="sidebarMenuScroll">
						<div class="sidebar-settings">
							<div class="accordion" id="settingsAccordion">
								<div class="accordion-item">
									<h2 class="accordion-header" id="genInfo">
										<button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#genCollapse" aria-expanded="true" aria-controls="genCollapse">
											General Info
										</button>
									</h2>
									<div id="genCollapse" class="accordion-collapse collapse show" aria-labelledby="genInfo" data-bs-parent="#settingsAccordion">
										<div class="accordion-body">
											<div class="field-wrapper">
												<input type="text" disabled value="<?php echo $this->session->userdata('name')?>" />
												<div class="field-placeholder">Full Name</div>
											</div>

											<div class="field-wrapper">
												<input  disabledtype="email" value="<?php echo $this->session->userdata('email')?>" />
												<div class="field-placeholder">Email</div>
											</div>

                                            <a href="<?php echo base_url('Login/logout')?>"><i class="icon-log-out1"></i>Logout</a>
										</div>
									</div>
								</div>

							</div>
						</div>
					</div>
					<!-- Settings end -->

					<!-- Sidebar actions starts -->

					<!-- Sidebar actions ends -->
				</div>

			</div>
			<!-- Tabs content end -->

		</div>
		<!-- Sidebar content end -->

	</nav>
	<!-- Sidebar wrapper end -->

	<!-- *************
		************ Main container start *************
	************* -->
	<div class="main-container">

		<!-- Page header starts -->
		<div class="page-header">

			<!-- Row start -->
			<div class="row gutters">
				<div class="col-xl-8 col-lg-8 col-md-8 col-sm-6 col-9">

					<!-- Search container start -->
					<div class="search-container">

						<!-- Toggle sidebar start -->
						<div class="toggle-sidebar" id="toggle-sidebar">
                            <i class="icon-menu"></i>
						</div>
						<!-- Toggle sidebar end -->
                        <h2 style="font-family:'fantasy';color: blue; font-weight: bolder;background-color: #fff; padding: 0.5em;border-radius: 50px 0px 50px 0px;">WELCOME TO <font color="red"><?php echo $settings->company_name; ?></font> </h2>

                                                <!-- Mega Menu Start -->
<!--						<div class="cd-dropdown-wrapper">-->
<!--							<a class="cd-dropdown-trigger" href="#0"><i class="icon-menu menu-icon"></i><span class="menu-text">Megamenu</span></a>-->
<!--							<nav class="cd-dropdown">-->
<!---->
<!---->
<!---->
<!--							</nav>-->
<!--						</div>-->
						<!-- Mega Menu End -->

						<!-- Search input group start -->
<!--						<div class="ui fluid category search">-->
<!--							<div class="ui icon input">-->
<!--								<input class="prompt" type="text" placeholder="Search">-->
<!--								<i class="search icon icon-search1"></i>-->
<!--							</div>-->
<!--							<div class="results"></div>-->
<!--						</div>-->
						<!-- Search input group end -->

					</div>
					<!-- Search container end -->

				</div>
				<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-3">

					<!-- Header actions start -->
					<ul class="header-actions">

						<li class="dropdown">
							<a href="#" id="userSettings" class="user-settings" data-toggle="dropdown" aria-haspopup="true">
										<span class="avatar">
											<img src="<?php echo base_url('cpanel_assets/')?>img/user.svg" alt="User Avatar">
											<span class="status busy"></span>
										</span>
							</a>
							<div class="dropdown-menu dropdown-menu-end md" aria-labelledby="userSettings">
								<div class="header-profile-actions">
									<a href="<?php echo base_url('users/profile') ?>"><i class="icon-user1"></i>Profile</a>
<!--									<a href="account-settings.html"><i class="icon-settings1"></i>Settings</a>-->
									<a href="<?php echo base_url('Login/logout')?>"><i class="icon-log-out1"></i>Logout</a>
								</div>
							</div>
						</li>
					</ul>
					<!-- Header actions end -->

				</div>
			</div>
			<!-- Row end -->

		</div>
		<!-- Page header ends -->

		<!-- Content wrapper scroll start -->
		<div class="content-wrapper-scroll">
