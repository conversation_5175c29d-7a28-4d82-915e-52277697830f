<?php $this->load->view('landing/header'); ?>

<!-- Blog Search Results -->
<section class="blog-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo base_url('blog'); ?>">Blog</a></li>
                        <li class="breadcrumb-item active">Search Results</li>
                    </ol>
                </nav>
                <h1 class="hero-title">Search Results</h1>
                <p class="hero-subtitle">
                    <?php if (!empty($posts)): ?>
                        Found <?php echo count($posts); ?> result(s) for "<?php echo htmlspecialchars($search_query); ?>"
                    <?php else: ?>
                        No results found for "<?php echo htmlspecialchars($search_query); ?>"
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
</section>

<section class="blog-content">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (!empty($posts)): ?>
                    <div class="row">
                        <?php foreach ($posts as $post): ?>
                            <div class="col-md-6 mb-4">
                                <article class="blog-card">
                                    <?php if ($post->featured_image): ?>
                                        <div class="blog-card-image">
                                            <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" 
                                                 alt="<?php echo htmlspecialchars($post->title); ?>" 
                                                 class="img-fluid">
                                            <div class="blog-card-overlay">
                                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more-btn">
                                                    <i class="fas fa-arrow-right"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-content">
                                        <div class="blog-meta">
                                            <span class="blog-date">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('M j, Y', strtotime($post->published_at)); ?>
                                            </span>
                                            <?php if ($post->category_name): ?>
                                                <a href="<?php echo base_url('blog/category/' . $post->category_slug); ?>" class="blog-category">
                                                    <i class="fas fa-folder"></i>
                                                    <?php echo htmlspecialchars($post->category_name); ?>
                                                </a>
                                            <?php endif; ?>
                                            <span class="blog-author">
                                                <i class="fas fa-user"></i>
                                                <?php echo htmlspecialchars($post->author_name); ?>
                                            </span>
                                        </div>
                                        
                                        <h3 class="blog-card-title">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>">
                                                <?php echo htmlspecialchars($post->title); ?>
                                            </a>
                                        </h3>
                                        
                                        <p class="blog-card-excerpt">
                                            <?php 
                                            $excerpt = $post->excerpt ?: strip_tags($post->content);
                                            echo htmlspecialchars(word_limiter($excerpt, 20));
                                            ?>
                                        </p>
                                        
                                        <div class="blog-card-footer">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more">
                                                Read More <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                            <div class="blog-stats">
                                                <span><i class="fas fa-eye"></i> <?php echo number_format($post->views); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-results">
                        <div class="text-center py-5">
                            <i class="fas fa-search fa-3x text-muted mb-3"></i>
                            <h4>No Results Found</h4>
                            <p class="text-muted mb-4">
                                We couldn't find any posts matching "<?php echo htmlspecialchars($search_query); ?>". 
                                Try searching with different keywords.
                            </p>
                            <div class="search-suggestions">
                                <h6>Search Suggestions:</h6>
                                <ul class="list-unstyled">
                                    <li>• Check your spelling</li>
                                    <li>• Try more general keywords</li>
                                    <li>• Use fewer keywords</li>
                                    <li>• Browse our <a href="<?php echo base_url('blog'); ?>">latest posts</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="blog-sidebar">
                    <!-- Search Widget -->
                    <div class="sidebar-widget">
                        <h5 class="widget-title">Search Again</h5>
                        <form action="<?php echo base_url('blog/search'); ?>" method="get" class="search-form">
                            <div class="input-group">
                                <input type="text" name="q" class="form-control" 
                                       placeholder="Search posts..." 
                                       value="<?php echo htmlspecialchars($search_query); ?>" required>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Categories Widget -->
                    <?php if (!empty($categories)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="category-list">
                                <?php foreach ($categories as $category): ?>
                                    <li>
                                        <a href="<?php echo base_url('blog/category/' . $category->slug); ?>">
                                            <?php echo htmlspecialchars($category->name); ?>
                                            <span class="post-count">(<?php echo $category->post_count; ?>)</span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Recent Posts Widget -->
                    <?php if (!empty($recent_posts)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Recent Posts</h5>
                            <div class="recent-posts">
                                <?php foreach ($recent_posts as $recent_post): ?>
                                    <div class="recent-post-item">
                                        <?php if ($recent_post->featured_image): ?>
                                            <div class="recent-post-image">
                                                <img src="<?php echo base_url('uploads/blog/thumbs/' . $recent_post->featured_image); ?>" 
                                                     alt="<?php echo htmlspecialchars($recent_post->title); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="recent-post-content">
                                            <h6>
                                                <a href="<?php echo base_url('blog/post/' . $recent_post->slug); ?>">
                                                    <?php echo htmlspecialchars($recent_post->title); ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($recent_post->published_at)); ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Popular Tags Widget -->
                    <?php if (!empty($popular_tags)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Popular Tags</h5>
                            <div class="tag-cloud">
                                <?php foreach ($popular_tags as $tag): ?>
                                    <a href="<?php echo base_url('blog/tag/' . $tag->slug); ?>" class="tag-item">
                                        <?php echo htmlspecialchars($tag->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.no-results {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.search-suggestions {
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
    padding: 1.5rem;
    text-align: left;
    display: inline-block;
}

.search-suggestions h6 {
    color: var(--text-dark);
    margin-bottom: 1rem;
}

.search-suggestions ul li {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.search-suggestions a {
    color: var(--primary-color);
    text-decoration: none;
}

.search-suggestions a:hover {
    text-decoration: underline;
}
</style>

<?php $this->load->view('landing/footer'); ?>
