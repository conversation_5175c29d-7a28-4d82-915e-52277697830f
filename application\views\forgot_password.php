<?php
$settings = get_by_id('cpanel_settings','id','1');
?>
<!doctype html>
<html lang="en">
<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">

    <!-- Meta -->
    <meta name="description" content="<?php echo $settings->company_name;?>">
    <meta name="author" content="<?php echo $settings->company_name;?>">
    <link rel="shortcut icon" href="<?php echo base_url('cpanel_assets/')?>img/fav.png" />

    <!-- Title -->
    <title><?php echo $settings->company_name;?></title>


    <!-- *************
        ************ Common Css Files *************
    ************ -->
    <!-- Bootstrap css -->
    <link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>css/bootstrap.min.css">

    <!-- Main css -->
    <link rel="stylesheet" href="<?php echo base_url('cpanel_assets/')?>css/main.css">


    <!-- *************
        ************ Vendor Css Files *************
    ************ -->

</head>
<body class="authentication">

<!-- Loading wrapper start -->
<div id="loading-wrapper">
    <div class="spinner-border"></div>
    Loading...
</div>
<!-- Loading wrapper end -->

<!-- *************
	************ Login container start *************
************* -->
<div class="login-container">

    <div class="container-fluid h-100">

        <!-- Row start -->
        <div class="row g-0 h-100">
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <div class="login-about">
                    <div class="slogan">
                        <span><?php echo $settings->login_page_heading;?></span>

                    </div>
                    <div class="about-desc">
                        <?php echo $settings->login_page_subtextt;?>
                        <!--						With Our premium hosting you may get unlimited space, email and even bandwidth. Free wordpress installation and customized cpanel.-->
                    </div>

                </div>
            </div>
            <div class="col-xl-6 col-lg-6 col-md-12 col-sm-12 col-12">
                <div class="login-wrapper">
                    <form action="<?php echo base_url('Login/send')?>" method="post">
                        <div class="login-screen">
                            <div class="login-body">

                                <center>
                                    <img src="<?php echo base_url('uploads/').$settings->logo?>" alt="logo" width="200" height="100">

                                </center>
                                <h6>PASSWORD RESET,<br>Please enter email to recover password.</h6>
                                <?php
                                if($this->session->flashdata('error')){
                                    ?>
                                    <div class="alert alert-danger" role="alert">
                                        Sorry !, Email is not recognized in our system;
                                    </div>
                                    <?php
                                }
                                ?>
                                <br>
                                <div class="field-wrapper">
                                    <input type="email" name="email" required>
                                    <div class="field-placeholder">Email Address</div>
                                </div>

                                <div class="actions">
                                    <button type="submit" class="btn btn-primary">Login</button>
                                    <a href="<?php echo base_url('Login')?>">Back to login</a>

                                </div>
                            </div>

                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- Row end -->

    </div>
</div>
<!-- *************
	************ Login container end *************
************* -->

<!-- *************
	************ Required JavaScript Files *************
************* -->
<!-- Required jQuery first, then Bootstrap Bundle JS -->
<script src="<?php echo base_url('cpanel_assets/')?>js/jquery.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>js/modernizr.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>js/moment.js"></script>

<!-- *************
	************ Vendor Js Files *************
************* -->

<!-- Main Js Required -->
<script src="<?php echo base_url('cpanel_assets/')?>js/main.js"></script>

</body>
</html>
