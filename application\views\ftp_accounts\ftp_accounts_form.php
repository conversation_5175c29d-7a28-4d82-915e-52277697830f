<?php
$domains = $this->Company_domains_model->get_my_domains($this->session->userdata('company_id'));
$company = $this->Company_model->company_details($this->session->userdata('company_id'));

?>
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card">
				<div class="card-body">
        <h2 style="margin-top:0px">Ftp accounts creation</h2>
					<?php
					if($this->session->flashdata('error')){
						?>
						<div class="alert alert-danger" role="alert">
							<ul>

								<?php
								foreach ($this->session->flashdata('error') as $err=>$value){
									echo "<li>-";
									echo $value;
									echo "</li>";
								}
								?>
							</ul>
						</div>
						<?php
					}
					?>
        <form action="<?php echo $action; ?>" method="post">
			<div class="row">
				<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">

					<div class="field-wrapper">
						<select class="select-single js-states" name="domain"  id="dm" title="Select Domain" required>

							<?php
							foreach ($domains as $d){
								?>
								<option value="<?php echo $d->domain_name ?>"><?php echo $d->domain_name ?></option>
								<?php
							}
							?>
						</select>
						<div class="field-placeholder">Domain <?php echo form_error('domain') ?></div>
					</div>
				</div>
				<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
	    <div class="field-wrapper">
			<input type="text" class="form-control" name="username" id="username" placeholder="Username" value="<?php echo $username; ?>" />
			<div class="field-placeholder">Username <?php echo form_error('username') ?></div>
        </div>
				</div>
				<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
					<div class="field-wrapper">
						<input type="password" class="form-control" name="password" id="username" placeholder="Ftp password" value="" />
						<div class="field-placeholder">password </div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
					<div class="field-wrapper">
						<div class="input-group">
							<span class="input-group-text"><?php echo $company->company_dir; ?>/</span>
							<input type="text" name="fullpath" value="<?php echo $company->company_dir; ?>" hidden>
							<input type="text" class="form-control" name="homedir"  placeholder="Enter dir/folder name">
						</div>

						<div class="field-placeholder">Directory path  <?php echo form_error('homedir') ?></div>
					</div>
				</div>
				<div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
					<div class="field-wrapper">
						<input type="number" class="form-control" name="quota" id="quota" required placeholder="Enter Quota" value="0" />
						<div class="field-placeholder">Quota (0 for unlimited space) <?php echo form_error('quota') ?></div>
					</div>
				</div>
			</div>



			<button type="submit" class="btn btn-primary"><?php echo $button ?></button>

	</form>
				</div>
			</div>
		</div>
	</div>
</div>
