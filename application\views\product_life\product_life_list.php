<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <div class="card">
                <div class="card-body">
                    <h2>Product  tracker</h2>
                    <br>
                    <br>
                    <br>
                    <div class="table-responsive">
                        <table id="copy-print-csv" class="table v-middle">
                            <thead>
            <tr>
                <th>No</th>
		<th>Product Type</th>

		<th>Product Name</th>
		<th>Product Price</th>
		<th>Expire Date</th>
		<th>Product Owner</th>
		<th>State</th>
		<th>Updated Date</th>
		<th>Action</th>
            </tr>
                            </thead>
                            <tbody>
            <?php
            $start = 0;
            foreach ($product_life_data as $product_life)
            {
                ?>
                <tr>
			<td width="80px"><?php echo ++$start ?></td>
			<td><?php echo $product_life->product_type ?></td>
			<td><a href="<?php echo base_url('Product_life/view_product/').$product_life->product_id?>" style="text-underline: blue;"><?php echo $product_life->product_name ?></a></td>

			<td><?php echo $product_life->product_price ?></td>
			<td><?php echo $product_life->expire_date ?></td>
			<td><a href="<?php echo base_url('Company/read/').  $product_life->product_owner  ?>">View this client</a></td>
			<td><?php
                $dateOne = new DateTime($product_life->expire_date);
                $dateTwo = new DateTime(date('Y-m-d'));

                 $diff = $dateTwo->diff($dateOne)->format("%a");

                if($product_life->expire_date < date('Y-m-d')){
                    ?>

                  Expired <span class="reddot"></span>
                    <?php
                }elseif ($diff < 31 && $diff > 0){
                    ?>
                   Will Expire soon <span class="yellowdot"></span>
                <?php
                }else{
                    ?>
                    Still time
                 
                    <span class="greendot"></span>
                <?php
                }?></td>
			<td><?php echo $product_life->updated_date ?></td>
			<td style="text-align:center" width="300px">
                <a href="<?php echo base_url('product_life/update/'.$product_life->product_life_id)?>" class="btn btn-sm btn-success">Edit/Update</a>
                 <?php if($product_life->expire_date < date('Y-m-d')){
                ?>
                     <a href="<?php echo base_url('product_life/send_note/?id='.$product_life->product_life_id.'&status=Expired')?>" class="btn btn-sm btn-danger">Email notify customer</a>


                <?php
                }elseif ($diff < 31 && $diff > 0){
                ?>
                     <a href="<?php echo base_url('product_life/send_note/?id='.$product_life->product_life_id.'&status=Will Expire soon')?>" class="btn btn-sm btn-danger">Email notify customer</a>


                <?php
                }else{
                    ?>

                <?php
                }?>
			</td>
		</tr>
                <?php
            }
            ?>
                            </tbody>
        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
