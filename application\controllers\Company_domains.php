<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Company_domains extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Company_domains_model');
        $this->load->model('Company_model');
        $this->load->model('Product_payments_model');
        $this->load->model('Product_life_model');

        $this->load->library('form_validation');
    }
public function my_domains(){
    	$data['data'] = $this->Company_domains_model->get_my_domains($this->session->userdata('company_id'));
    	$config['active_menu'] = "domains";
		$config['current_link'] = "domains";
    	$this->load->view('header',$config);
    	$this->load->view('my_domains',$data);
    	$this->load->view('footer');
}
public function buy_domain(){

    	$config['active_menu'] = "domains";
		$config['current_link'] = "buy_domains";
    	$this->load->view('header',$config);
    	$this->load->view('buy_domain');
    	$this->load->view('footer');
}
public function search_domain(){

}
    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'company_domains/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'company_domains/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'company_domains/index.html';
            $config['first_url'] = base_url() . 'company_domains/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Company_domains_model->total_rows($q);
        $company_domains = $this->Company_domains_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'company_domains_data' => $this->Company_domains_model->get_all(),
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $configs['active_menu'] = "server_config";
        $configs['current_link'] = "c_domains";
        $this->load->view('header',$configs);
        $this->load->view('company_domains/company_domains_list', $data);
        $this->load->view('footer',$config);
    }

    public function read($id) 
    {
        $row = $this->Company_domains_model->get_by_id($id);
        if ($row) {
            $data = array(
		'domain_id' => $row->domain_id,
		'domain_name' => $row->domain_name,
		'company_id' => $row->company_id,
		'domain_file_path' => $row->domain_file_path,
		'domain_stamp' => $row->domain_stamp,
	    );
            $this->load->view('company_domains/company_domains_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company_domains'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('company_domains/create_action'),
	    'domain_id' => set_value('domain_id'),
	    'domain_name' => set_value('domain_name'),
	    'company_id' => set_value('company_id'),
	    'domain_file_path' => set_value('domain_file_path'),
	    'domain_stamp' => set_value('domain_stamp'),
	);
        $config['active_menu'] = "server_config";
        $config['current_link'] = "c_domains";
        $this->load->view('header',$config);
        $this->load->view('company_domains/company_domains_form', $data);
        $this->load->view('footer',$config);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'domain_name' => $this->input->post('domain_name',TRUE),
		'company_id' => $this->input->post('company_id',TRUE),
		'domain_file_path' => $this->input->post('domain_file_path',TRUE),
		
	    );



            $domains = $this->Company_domains_model->insert($data);

            $domain_data = array(
                'product_type' => 'Domain',
                'product_id' => $domains,
                'product_name' => $this->input->post('domain_name',TRUE),
                'product_price' => $this->input->post('domain_amount',TRUE),
                'expire_date' => $this->input->post('domain_expiry_date',TRUE),
                'product_owner' =>  $this->input->post('company_id',TRUE),
                'updated_date' => date('Y-m-d'),
            );

            $life1 = $this->Product_life_model->insert($domain_data);



            $domain_product_payments = array(
                'product_life_id' => $life1,
                'payment_method' => $this->input->post('payment_method',TRUE),
                'reference' => $this->input->post('reference',TRUE),
                'amount' => $this->input->post('domain_amount',TRUE),
                'payment_date' => date('Y-m-d'),


            );

            $this->Product_payments_model->insert($domain_product_payments);

            $this->toaster->success('Company domain created successfully');
            redirect(site_url('company_domains'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Company_domains_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('company_domains/update_action'),
		'domain_id' => set_value('domain_id', $row->domain_id),
		'domain_name' => set_value('domain_name', $row->domain_name),
		'company_id' => set_value('company_id', $row->company_id),
		'domain_file_path' => set_value('domain_file_path', $row->domain_file_path),
		'domain_stamp' => set_value('domain_stamp', $row->domain_stamp),
	    );
            $this->load->view('company_domains/company_domains_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company_domains'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('domain_id', TRUE));
        } else {
            $data = array(
		'domain_name' => $this->input->post('domain_name',TRUE),
		'company_id' => $this->input->post('company_id',TRUE),
		'domain_file_path' => $this->input->post('domain_file_path',TRUE),

	    );

            $this->Company_domains_model->update($this->input->post('domain_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('company_domains'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Company_domains_model->get_by_id($id);

        if ($row) {
            $this->Company_domains_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('company_domains'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company_domains'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('domain_name', 'domain name', 'trim|required');
	$this->form_validation->set_rules('company_id', 'company id', 'trim|required');
	$this->form_validation->set_rules('domain_file_path', 'domain file path', 'trim|required');


	$this->form_validation->set_rules('domain_id', 'domain_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

