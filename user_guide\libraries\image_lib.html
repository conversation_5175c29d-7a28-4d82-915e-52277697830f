

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Image Manipulation Class &mdash; CodeIgniter 3.1.11 documentation</title>
  

  
  
    <link rel="shortcut icon" href="../_static/ci-icon.ico"/>
  

  
  <link href='https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic|Roboto+Slab:400,700|Inconsolata:400,700&subset=latin,cyrillic' rel='stylesheet' type='text/css'>

  
  
    

  

  
  
    <link rel="stylesheet" href="../_static/css/citheme.css" type="text/css" />
  

  
        <link rel="index" title="Index"
              href="../genindex.html"/>
        <link rel="search" title="Search" href="../search.html"/>
    <link rel="top" title="CodeIgniter 3.1.11 documentation" href="../index.html"/>
        <link rel="up" title="Libraries" href="index.html"/>
        <link rel="next" title="Input Class" href="input.html"/>
        <link rel="prev" title="FTP Class" href="ftp.html"/> 

  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

  <div id="nav">
  <div id="nav_inner">
    
    
    
      <div id="pulldown-menu" class="ciNav">
        <ul>
<li class="toctree-l1"><a class="reference internal" href="../general/welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../general/index.html">General Topics</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../general/urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/styleguide.html">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Libraries</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="ftp.html">FTP Class</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

      </div>
    
      
  </div>
</div>
<div id="nav2">
  <a href="#" id="openToc">
    <img src="data:image/jpeg;base64,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" title="Toggle Table of Contents" alt="Toggle Table of Contents" />
  </a>
</div>

  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-nav-search">
        
          <a href="../index.html" class="fa fa-home"> CodeIgniter</a>
        
        
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="../search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        
          
          
              <ul>
<li class="toctree-l1"><a class="reference internal" href="../general/welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="../installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="../overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="../tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="../DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../general/index.html">General Topics</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../general/urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="../general/styleguide.html">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul class="current">
<li class="toctree-l1 current"><a class="reference internal" href="index.html">Libraries</a><ul class="current">
<li class="toctree-l2"><a class="reference internal" href="benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="ftp.html">FTP Class</a></li>
<li class="toctree-l2 current"><a class="current reference internal" href="#">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="../database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="../helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="../helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="../helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

          
        
      </div>
      &nbsp;
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
        <a href="../index.html">CodeIgniter</a>
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="../index.html">Docs</a> &raquo;</li>
      
        <li><a href="index.html">Libraries</a> &raquo;</li>
      
    <li>Image Manipulation Class</li>
    <li class="wy-breadcrumbs-aside">
      
    </li>
    <div style="float:right;margin-left:5px;" id="closeMe">
      <img title="Classic Layout" alt="classic layout" src="data:image/gif;base64,R0lGODlhFAAUAJEAAAAAADMzM////wAAACH5BAUUAAIALAAAAAAUABQAAAImlI+py+0PU5gRBRDM3DxbWoXis42X13USOLauUIqnlsaH/eY6UwAAOw==" />
    </div>
  </ul>
  <hr/>
</div>
          <div role="main" class="document">
            
  <div class="section" id="image-manipulation-class">
<h1>Image Manipulation Class<a class="headerlink" href="#image-manipulation-class" title="Permalink to this headline">¶</a></h1>
<p>CodeIgniter’s Image Manipulation class lets you perform the following
actions:</p>
<ul class="simple">
<li>Image Resizing</li>
<li>Thumbnail Creation</li>
<li>Image Cropping</li>
<li>Image Rotating</li>
<li>Image Watermarking</li>
</ul>
<p>All three major image libraries are supported: GD/GD2, NetPBM, and
ImageMagick</p>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">Watermarking is only available using the GD/GD2 library. In
addition, even though other libraries are supported, GD is required in
order for the script to calculate the image properties. The image
processing, however, will be performed with the library you specify.</p>
</div>
<div class="contents local topic" id="contents">
<ul class="simple">
<li><a class="reference internal" href="#initializing-the-class" id="id1">Initializing the Class</a><ul>
<li><a class="reference internal" href="#processing-an-image" id="id2">Processing an Image</a></li>
<li><a class="reference internal" href="#processing-methods" id="id3">Processing Methods</a></li>
<li><a class="reference internal" href="#preferences" id="id4">Preferences</a></li>
<li><a class="reference internal" href="#setting-preferences-in-a-config-file" id="id5">Setting preferences in a config file</a></li>
</ul>
</li>
<li><a class="reference internal" href="#image-watermarking" id="id6">Image Watermarking</a><ul>
<li><a class="reference internal" href="#two-types-of-watermarking" id="id7">Two Types of Watermarking</a></li>
<li><a class="reference internal" href="#watermarking-an-image" id="id8">Watermarking an Image</a></li>
<li><a class="reference internal" href="#watermarking-preferences" id="id9">Watermarking Preferences</a><ul>
<li><a class="reference internal" href="#text-preferences" id="id10">Text Preferences</a></li>
<li><a class="reference internal" href="#overlay-preferences" id="id11">Overlay Preferences</a></li>
</ul>
</li>
</ul>
</li>
<li><a class="reference internal" href="#class-reference" id="id12">Class Reference</a></li>
</ul>
</div>
<div class="custom-index container"></div><div class="section" id="initializing-the-class">
<h2><a class="toc-backref" href="#id1">Initializing the Class</a><a class="headerlink" href="#initializing-the-class" title="Permalink to this headline">¶</a></h2>
<p>Like most other classes in CodeIgniter, the image class is initialized
in your controller using the $this-&gt;load-&gt;library function:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$this</span><span class="o">-&gt;</span><span class="na">load</span><span class="o">-&gt;</span><span class="na">library</span><span class="p">(</span><span class="s1">&#39;image_lib&#39;</span><span class="p">);</span>
</pre></div>
</div>
<p>Once the library is loaded it will be ready for use. The image library
object you will use to call all functions is: <code class="docutils literal"><span class="pre">$this-&gt;image_lib</span></code></p>
<div class="section" id="processing-an-image">
<h3><a class="toc-backref" href="#id2">Processing an Image</a><a class="headerlink" href="#processing-an-image" title="Permalink to this headline">¶</a></h3>
<p>Regardless of the type of processing you would like to perform
(resizing, cropping, rotation, or watermarking), the general process is
identical. You will set some preferences corresponding to the action you
intend to perform, then call one of four available processing functions.
For example, to create an image thumbnail you’ll do this:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;image_library&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;gd2&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;source_image&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/path/to/image/mypic.jpg&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;create_thumb&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">TRUE</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;maintain_ratio&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">TRUE</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;width&#39;</span><span class="p">]</span>         <span class="o">=</span> <span class="mi">75</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;height&#39;</span><span class="p">]</span>       <span class="o">=</span> <span class="mi">50</span><span class="p">;</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">load</span><span class="o">-&gt;</span><span class="na">library</span><span class="p">(</span><span class="s1">&#39;image_lib&#39;</span><span class="p">,</span> <span class="nv">$config</span><span class="p">);</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">resize</span><span class="p">();</span>
</pre></div>
</div>
<p>The above code tells the image_resize function to look for an image
called <em>mypic.jpg</em> located in the source_image folder, then create a
thumbnail that is 75 X 50 pixels using the GD2 image_library. Since the
maintain_ratio option is enabled, the thumb will be as close to the
target width and height as possible while preserving the original aspect
ratio. The thumbnail will be called <em>mypic_thumb.jpg</em> and located at
the same level as <em>source_image</em>.</p>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">In order for the image class to be allowed to do any
processing, the folder containing the image files must have write
permissions.</p>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">Image processing can require a considerable amount of server
memory for some operations. If you are experiencing out of memory errors
while processing images you may need to limit their maximum size, and/or
adjust PHP memory limits.</p>
</div>
</div>
<div class="section" id="processing-methods">
<h3><a class="toc-backref" href="#id3">Processing Methods</a><a class="headerlink" href="#processing-methods" title="Permalink to this headline">¶</a></h3>
<p>There are four available processing methods:</p>
<ul class="simple">
<li>$this-&gt;image_lib-&gt;resize()</li>
<li>$this-&gt;image_lib-&gt;crop()</li>
<li>$this-&gt;image_lib-&gt;rotate()</li>
<li>$this-&gt;image_lib-&gt;watermark()</li>
</ul>
<p>These methods return boolean TRUE upon success and FALSE for failure.
If they fail you can retrieve the error message using this function:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">echo</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">();</span>
</pre></div>
</div>
<p>A good practice is to use the processing function conditionally, showing an
error upon failure, like this:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">if</span> <span class="p">(</span> <span class="o">!</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">resize</span><span class="p">())</span>
<span class="p">{</span>
        <span class="k">echo</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p>You can optionally specify the HTML formatting to be applied to
the errors, by submitting the opening/closing tags in the function,
like this:</p>
<div class="last highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">(</span><span class="s1">&#39;&lt;p&gt;&#39;</span><span class="p">,</span> <span class="s1">&#39;&lt;/p&gt;&#39;</span><span class="p">);</span>
</pre></div>
</div>
</div>
</div>
<div class="section" id="preferences">
<span id="processing-preferences"></span><h3><a class="toc-backref" href="#id4">Preferences</a><a class="headerlink" href="#preferences" title="Permalink to this headline">¶</a></h3>
<p>The preferences described below allow you to tailor the image processing
to suit your needs.</p>
<p>Note that not all preferences are available for every function. For
example, the x/y axis preferences are only available for image cropping.
Likewise, the width and height preferences have no effect on cropping.
The “availability” column indicates which functions support a given
preference.</p>
<p>Availability Legend:</p>
<ul class="simple">
<li>R - Image Resizing</li>
<li>C - Image Cropping</li>
<li>X - Image Rotation</li>
<li>W - Image Watermarking</li>
</ul>
<table border="1" class="docutils">
<colgroup>
<col width="14%" />
<col width="14%" />
<col width="19%" />
<col width="45%" />
<col width="8%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">Preference</th>
<th class="head">Default Value</th>
<th class="head">Options</th>
<th class="head">Description</th>
<th class="head">Availability</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td><strong>image_library</strong></td>
<td>GD2</td>
<td>GD, GD2, ImageMagick, NetPBM</td>
<td>Sets the image library to be used.</td>
<td>R, C, X, W</td>
</tr>
<tr class="row-odd"><td><strong>library_path</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the server path to your ImageMagick or NetPBM library. If you use
either of those libraries you must supply the path.</td>
<td>R, C, X
R, C, S, W</td>
</tr>
<tr class="row-even"><td><strong>source_image</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the source image name/path. The path must be a relative or absolute
server path, not a URL.</td>
<td>&#160;</td>
</tr>
<tr class="row-odd"><td><strong>dynamic_output</strong></td>
<td>FALSE</td>
<td>TRUE/FALSE (boolean)</td>
<td>Determines whether the new image file should be written to disk or
generated dynamically. Note: If you choose the dynamic setting, only one
image can be shown at a time, and it can’t be positioned on the page. It
simply outputs the raw image dynamically to your browser, along with
image headers.</td>
<td>R, C, X, W</td>
</tr>
<tr class="row-even"><td><strong>file_permissions</strong></td>
<td>0644</td>
<td>(integer)</td>
<td>File system permissions to apply on the resulting image file,
writing it to the disk. WARNING: Use octal integer notation!</td>
<td>R, C, X, W</td>
</tr>
<tr class="row-odd"><td><strong>quality</strong></td>
<td>90%</td>
<td>1 - 100%</td>
<td>Sets the quality of the image. The higher the quality the larger the
file size.</td>
<td>R, C, X, W</td>
</tr>
<tr class="row-even"><td><strong>new_image</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the destination image name/path. You’ll use this preference when
creating an image copy. The path must be a relative or absolute server
path, not a URL.</td>
<td>R, C, X, W</td>
</tr>
<tr class="row-odd"><td><strong>width</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the width you would like the image set to.</td>
<td>R, C</td>
</tr>
<tr class="row-even"><td><strong>height</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the height you would like the image set to.</td>
<td>R, C</td>
</tr>
<tr class="row-odd"><td><strong>create_thumb</strong></td>
<td>FALSE</td>
<td>TRUE/FALSE (boolean)</td>
<td>Tells the image processing function to create a thumb.</td>
<td>R</td>
</tr>
<tr class="row-even"><td><strong>thumb_marker</strong></td>
<td>_thumb</td>
<td>None</td>
<td>Specifies the thumbnail indicator. It will be inserted just before the
file extension, so mypic.jpg would become mypic_thumb.jpg</td>
<td>R</td>
</tr>
<tr class="row-odd"><td><strong>maintain_ratio</strong></td>
<td>TRUE</td>
<td>TRUE/FALSE (boolean)</td>
<td>Specifies whether to maintain the original aspect ratio when resizing or
use hard values.</td>
<td>R, C</td>
</tr>
<tr class="row-even"><td><strong>master_dim</strong></td>
<td>auto</td>
<td>auto, width, height</td>
<td>Specifies what to use as the master axis when resizing or creating
thumbs. For example, let’s say you want to resize an image to 100 X 75
pixels. If the source image size does not allow perfect resizing to
those dimensions, this setting determines which axis should be used as
the hard value. “auto” sets the axis automatically based on whether the
image is taller than wider, or vice versa.</td>
<td>R</td>
</tr>
<tr class="row-odd"><td><strong>rotation_angle</strong></td>
<td>None</td>
<td>90, 180, 270, vrt, hor</td>
<td>Specifies the angle of rotation when rotating images. Note that PHP
rotates counter-clockwise, so a 90 degree rotation to the right must be
specified as 270.</td>
<td>X</td>
</tr>
<tr class="row-even"><td><strong>x_axis</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the X coordinate in pixels for image cropping. For example, a
setting of 30 will crop an image 30 pixels from the left.</td>
<td>C</td>
</tr>
<tr class="row-odd"><td><strong>y_axis</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the Y coordinate in pixels for image cropping. For example, a
setting of 30 will crop an image 30 pixels from the top.</td>
<td>C</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="setting-preferences-in-a-config-file">
<h3><a class="toc-backref" href="#id5">Setting preferences in a config file</a><a class="headerlink" href="#setting-preferences-in-a-config-file" title="Permalink to this headline">¶</a></h3>
<p>If you prefer not to set preferences using the above method, you can
instead put them into a config file. Simply create a new file called
image_lib.php, add the $config array in that file. Then save the file
in <em>config/image_lib.php</em> and it will be used automatically. You will
NOT need to use the <code class="docutils literal"><span class="pre">$this-&gt;image_lib-&gt;initialize()</span></code> method if you save
your preferences in a config file.</p>
</div>
</div>
<div class="section" id="image-watermarking">
<h2><a class="toc-backref" href="#id6">Image Watermarking</a><a class="headerlink" href="#image-watermarking" title="Permalink to this headline">¶</a></h2>
<p>The Watermarking feature requires the GD/GD2 library.</p>
<div class="section" id="two-types-of-watermarking">
<h3><a class="toc-backref" href="#id7">Two Types of Watermarking</a><a class="headerlink" href="#two-types-of-watermarking" title="Permalink to this headline">¶</a></h3>
<p>There are two types of watermarking that you can use:</p>
<ul class="simple">
<li><strong>Text</strong>: The watermark message will be generated using text, either
with a True Type font that you specify, or using the native text
output that the GD library supports. If you use the True Type version
your GD installation must be compiled with True Type support (most
are, but not all).</li>
<li><strong>Overlay</strong>: The watermark message will be generated by overlaying an
image (usually a transparent PNG or GIF) containing your watermark
over the source image.</li>
</ul>
</div>
<div class="section" id="watermarking-an-image">
<span id="watermarking"></span><h3><a class="toc-backref" href="#id8">Watermarking an Image</a><a class="headerlink" href="#watermarking-an-image" title="Permalink to this headline">¶</a></h3>
<p>Just as with the other methods (resizing, cropping, and rotating) the
general process for watermarking involves setting the preferences
corresponding to the action you intend to perform, then calling the
watermark function. Here is an example:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;source_image&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/path/to/image/mypic.jpg&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_text&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;Copyright 2006 - John Doe&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_type&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;text&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_font_path&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;./system/fonts/texb.ttf&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_font_size&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;16&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_font_color&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;ffffff&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_vrt_alignment&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;bottom&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_hor_alignment&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;center&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;wm_padding&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;20&#39;</span><span class="p">;</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">initialize</span><span class="p">(</span><span class="nv">$config</span><span class="p">);</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">watermark</span><span class="p">();</span>
</pre></div>
</div>
<p>The above example will use a 16 pixel True Type font to create the text
“Copyright 2006 - John Doe”. The watermark will be positioned at the
bottom/center of the image, 20 pixels from the bottom of the image.</p>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">In order for the image class to be allowed to do any
processing, the image file must have “write” file permissions
For example, 777.</p>
</div>
</div>
<div class="section" id="watermarking-preferences">
<h3><a class="toc-backref" href="#id9">Watermarking Preferences</a><a class="headerlink" href="#watermarking-preferences" title="Permalink to this headline">¶</a></h3>
<p>This table shows the preferences that are available for both types of
watermarking (text or overlay)</p>
<table border="1" class="docutils">
<colgroup>
<col width="17%" />
<col width="14%" />
<col width="17%" />
<col width="53%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">Preference</th>
<th class="head">Default Value</th>
<th class="head">Options</th>
<th class="head">Description</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td><strong>wm_type</strong></td>
<td>text</td>
<td>text, overlay</td>
<td>Sets the type of watermarking that should be used.</td>
</tr>
<tr class="row-odd"><td><strong>source_image</strong></td>
<td>None</td>
<td>None</td>
<td>Sets the source image name/path. The path must be a relative or absolute
server path, not a URL.</td>
</tr>
<tr class="row-even"><td><strong>dynamic_output</strong></td>
<td>FALSE</td>
<td>TRUE/FALSE (boolean)</td>
<td>Determines whether the new image file should be written to disk or
generated dynamically. Note: If you choose the dynamic setting, only one
image can be shown at a time, and it can’t be positioned on the page. It
simply outputs the raw image dynamically to your browser, along with
image headers.</td>
</tr>
<tr class="row-odd"><td><strong>quality</strong></td>
<td>90%</td>
<td>1 - 100%</td>
<td>Sets the quality of the image. The higher the quality the larger the
file size.</td>
</tr>
<tr class="row-even"><td><strong>wm_padding</strong></td>
<td>None</td>
<td>A number</td>
<td>The amount of padding, set in pixels, that will be applied to the
watermark to set it away from the edge of your images.</td>
</tr>
<tr class="row-odd"><td><strong>wm_vrt_alignment</strong></td>
<td>bottom</td>
<td>top, middle, bottom</td>
<td>Sets the vertical alignment for the watermark image.</td>
</tr>
<tr class="row-even"><td><strong>wm_hor_alignment</strong></td>
<td>center</td>
<td>left, center, right</td>
<td>Sets the horizontal alignment for the watermark image.</td>
</tr>
<tr class="row-odd"><td><strong>wm_hor_offset</strong></td>
<td>None</td>
<td>None</td>
<td>You may specify a horizontal offset (in pixels) to apply to the
watermark position. The offset normally moves the watermark to the
right, except if you have your alignment set to “right” then your offset
value will move the watermark toward the left of the image.</td>
</tr>
<tr class="row-even"><td><strong>wm_vrt_offset</strong></td>
<td>None</td>
<td>None</td>
<td>You may specify a vertical offset (in pixels) to apply to the watermark
position. The offset normally moves the watermark down, except if you
have your alignment set to “bottom” then your offset value will move the
watermark toward the top of the image.</td>
</tr>
</tbody>
</table>
<div class="section" id="text-preferences">
<h4><a class="toc-backref" href="#id10">Text Preferences</a><a class="headerlink" href="#text-preferences" title="Permalink to this headline">¶</a></h4>
<p>This table shows the preferences that are available for the text type of
watermarking.</p>
<table border="1" class="docutils">
<colgroup>
<col width="17%" />
<col width="14%" />
<col width="14%" />
<col width="55%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">Preference</th>
<th class="head">Default Value</th>
<th class="head">Options</th>
<th class="head">Description</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td><strong>wm_text</strong></td>
<td>None</td>
<td>None</td>
<td>The text you would like shown as the watermark. Typically this will be a
copyright notice.</td>
</tr>
<tr class="row-odd"><td><strong>wm_font_path</strong></td>
<td>None</td>
<td>None</td>
<td>The server path to the True Type Font you would like to use. If you do
not use this option, the native GD font will be used.</td>
</tr>
<tr class="row-even"><td><strong>wm_font_size</strong></td>
<td>16</td>
<td>None</td>
<td>The size of the text. Note: If you are not using the True Type option
above, the number is set using a range of 1 - 5. Otherwise, you can use
any valid pixel size for the font you’re using.</td>
</tr>
<tr class="row-odd"><td><strong>wm_font_color</strong></td>
<td>ffffff</td>
<td>None</td>
<td>The font color, specified in hex. Both the full 6-length (ie, 993300) and
the short three character abbreviated version (ie, fff) are supported.</td>
</tr>
<tr class="row-even"><td><strong>wm_shadow_color</strong></td>
<td>None</td>
<td>None</td>
<td>The color of the drop shadow, specified in hex. If you leave this blank
a drop shadow will not be used. Both the full 6-length (ie, 993300) and
the short three character abbreviated version (ie, fff) are supported.</td>
</tr>
<tr class="row-odd"><td><strong>wm_shadow_distance</strong></td>
<td>3</td>
<td>None</td>
<td>The distance (in pixels) from the font that the drop shadow should
appear.</td>
</tr>
</tbody>
</table>
</div>
<div class="section" id="overlay-preferences">
<h4><a class="toc-backref" href="#id11">Overlay Preferences</a><a class="headerlink" href="#overlay-preferences" title="Permalink to this headline">¶</a></h4>
<p>This table shows the preferences that are available for the overlay type
of watermarking.</p>
<table border="1" class="docutils">
<colgroup>
<col width="17%" />
<col width="14%" />
<col width="14%" />
<col width="55%" />
</colgroup>
<thead valign="bottom">
<tr class="row-odd"><th class="head">Preference</th>
<th class="head">Default Value</th>
<th class="head">Options</th>
<th class="head">Description</th>
</tr>
</thead>
<tbody valign="top">
<tr class="row-even"><td><strong>wm_overlay_path</strong></td>
<td>None</td>
<td>None</td>
<td>The server path to the image you wish to use as your watermark. Required
only if you are using the overlay method.</td>
</tr>
<tr class="row-odd"><td><strong>wm_opacity</strong></td>
<td>50</td>
<td>1 - 100</td>
<td>Image opacity. You may specify the opacity (i.e. transparency) of your
watermark image. This allows the watermark to be faint and not
completely obscure the details from the original image behind it. A 50%
opacity is typical.</td>
</tr>
<tr class="row-even"><td><strong>wm_x_transp</strong></td>
<td>4</td>
<td>A number</td>
<td>If your watermark image is a PNG or GIF image, you may specify a color
on the image to be “transparent”. This setting (along with the next)
will allow you to specify that color. This works by specifying the “X”
and “Y” coordinate pixel (measured from the upper left) within the image
that corresponds to a pixel representative of the color you want to be
transparent.</td>
</tr>
<tr class="row-odd"><td><strong>wm_y_transp</strong></td>
<td>4</td>
<td>A number</td>
<td>Along with the previous setting, this allows you to specify the
coordinate to a pixel representative of the color you want to be
transparent.</td>
</tr>
</tbody>
</table>
</div>
</div>
</div>
<div class="section" id="class-reference">
<h2><a class="toc-backref" href="#id12">Class Reference</a><a class="headerlink" href="#class-reference" title="Permalink to this headline">¶</a></h2>
<dl class="class">
<dt id="CI_Image_lib">
<em class="property">class </em><code class="descname">CI_Image_lib</code><a class="headerlink" href="#CI_Image_lib" title="Permalink to this definition">¶</a></dt>
<dd><dl class="method">
<dt id="CI_Image_lib::initialize">
<code class="descname">initialize</code><span class="sig-paren">(</span><span class="optional">[</span><em>$props = array()</em><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::initialize" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Parameters:</th><td class="field-body"><ul class="first simple">
<li><strong>$props</strong> (<em>array</em>) – Image processing preferences</li>
</ul>
</td>
</tr>
<tr class="field-even field"><th class="field-name">Returns:</th><td class="field-body"><p class="first">TRUE on success, FALSE in case of invalid settings</p>
</td>
</tr>
<tr class="field-odd field"><th class="field-name">Return type:</th><td class="field-body"><p class="first last">bool</p>
</td>
</tr>
</tbody>
</table>
<p>Initializes the class for processing an image.</p>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::resize">
<code class="descname">resize</code><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::resize" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Returns:</th><td class="field-body">TRUE on success, FALSE on failure</td>
</tr>
<tr class="field-even field"><th class="field-name">Return type:</th><td class="field-body">bool</td>
</tr>
</tbody>
</table>
<p>The image resizing method lets you resize the original image, create a
copy (with or without resizing), or create a thumbnail image.</p>
<p>For practical purposes there is no difference between creating a copy
and creating a thumbnail except a thumb will have the thumbnail marker
as part of the name (i.e. mypic_thumb.jpg).</p>
<p>All preferences listed in the <a class="reference internal" href="#processing-preferences"><span class="std std-ref">Preferences</span></a> table are available for this
method except these three: <em>rotation_angle</em>, <em>x_axis</em> and <em>y_axis</em>.</p>
<p><strong>Creating a Thumbnail</strong></p>
<p>The resizing method will create a thumbnail file (and preserve the
original) if you set this preference to TRUE:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;create_thumb&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="k">TRUE</span><span class="p">;</span>
</pre></div>
</div>
<p>This single preference determines whether a thumbnail is created or not.</p>
<p><strong>Creating a Copy</strong></p>
<p>The resizing method will create a copy of the image file (and preserve
the original) if you set a path and/or a new filename using this
preference:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;new_image&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/path/to/new_image.jpg&#39;</span><span class="p">;</span>
</pre></div>
</div>
<p>Notes regarding this preference:</p>
<ul class="simple">
<li>If only the new image name is specified it will be placed in the same
folder as the original</li>
<li>If only the path is specified, the new image will be placed in the
destination with the same name as the original.</li>
<li>If both the path and image name are specified it will placed in its
own destination and given the new name.</li>
</ul>
<p><strong>Resizing the Original Image</strong></p>
<p>If neither of the two preferences listed above (create_thumb, and
new_image) are used, the resizing method will instead target the
original image for processing.</p>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::crop">
<code class="descname">crop</code><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::crop" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Returns:</th><td class="field-body">TRUE on success, FALSE on failure</td>
</tr>
<tr class="field-even field"><th class="field-name">Return type:</th><td class="field-body">bool</td>
</tr>
</tbody>
</table>
<p>The cropping method works nearly identically to the resizing function
except it requires that you set preferences for the X and Y axis (in
pixels) specifying where to crop, like this:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;x_axis&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">100</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;y_axis&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">40</span><span class="p">;</span>
</pre></div>
</div>
<p>All preferences listed in the <a class="reference internal" href="#processing-preferences"><span class="std std-ref">Preferences</span></a> table are available for this
method except these: <em>rotation_angle</em>, <em>create_thumb</em> and <em>new_image</em>.</p>
<p>Here’s an example showing how you might crop an image:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;image_library&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;imagemagick&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;library_path&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/usr/X11R6/bin/&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;source_image&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/path/to/image/mypic.jpg&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;x_axis&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">100</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;y_axis&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="mi">60</span><span class="p">;</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">initialize</span><span class="p">(</span><span class="nv">$config</span><span class="p">);</span>

<span class="k">if</span> <span class="p">(</span> <span class="o">!</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">crop</span><span class="p">())</span>
<span class="p">{</span>
        <span class="k">echo</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
<div class="admonition note">
<p class="first admonition-title">Note</p>
<p class="last">Without a visual interface it is difficult to crop images, so this
method is not very useful unless you intend to build such an
interface. That’s exactly what we did using for the photo gallery module
in ExpressionEngine, the CMS we develop. We added a JavaScript UI that
lets the cropping area be selected.</p>
</div>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::rotate">
<code class="descname">rotate</code><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::rotate" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Returns:</th><td class="field-body">TRUE on success, FALSE on failure</td>
</tr>
<tr class="field-even field"><th class="field-name">Return type:</th><td class="field-body">bool</td>
</tr>
</tbody>
</table>
<p>The image rotation method requires that the angle of rotation be set
via its preference:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;rotation_angle&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;90&#39;</span><span class="p">;</span>
</pre></div>
</div>
<p>There are 5 rotation options:</p>
<ol class="arabic simple">
<li>90 - rotates counter-clockwise by 90 degrees.</li>
<li>180 - rotates counter-clockwise by 180 degrees.</li>
<li>270 - rotates counter-clockwise by 270 degrees.</li>
<li>hor - flips the image horizontally.</li>
<li>vrt - flips the image vertically.</li>
</ol>
<p>Here’s an example showing how you might rotate an image:</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;image_library&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;netpbm&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;library_path&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/usr/bin/&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;source_image&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;/path/to/image/mypic.jpg&#39;</span><span class="p">;</span>
<span class="nv">$config</span><span class="p">[</span><span class="s1">&#39;rotation_angle&#39;</span><span class="p">]</span> <span class="o">=</span> <span class="s1">&#39;hor&#39;</span><span class="p">;</span>

<span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">initialize</span><span class="p">(</span><span class="nv">$config</span><span class="p">);</span>

<span class="k">if</span> <span class="p">(</span> <span class="o">!</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">rotate</span><span class="p">())</span>
<span class="p">{</span>
        <span class="k">echo</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">();</span>
<span class="p">}</span>
</pre></div>
</div>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::watermark">
<code class="descname">watermark</code><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::watermark" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Returns:</th><td class="field-body">TRUE on success, FALSE on failure</td>
</tr>
<tr class="field-even field"><th class="field-name">Return type:</th><td class="field-body">bool</td>
</tr>
</tbody>
</table>
<p>Creates a watermark over an image, please refer to the <a class="reference internal" href="#watermarking"><span class="std std-ref">Watermarking an Image</span></a>
section for more info.</p>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::clear">
<code class="descname">clear</code><span class="sig-paren">(</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::clear" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Return type:</th><td class="field-body">void</td>
</tr>
</tbody>
</table>
<p>The clear method resets all of the values used when processing an
image. You will want to call this if you are processing images in a
loop.</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">clear</span><span class="p">();</span>
</pre></div>
</div>
</dd></dl>

<dl class="method">
<dt id="CI_Image_lib::display_errors">
<code class="descname">display_errors</code><span class="sig-paren">(</span><span class="optional">[</span><em>$open = '&lt;p&gt;</em><span class="optional">[</span>, <em>$close = '&lt;/p&gt;'</em><span class="optional">]</span><span class="optional">]</span><span class="sig-paren">)</span><a class="headerlink" href="#CI_Image_lib::display_errors" title="Permalink to this definition">¶</a></dt>
<dd><table class="docutils field-list" frame="void" rules="none">
<col class="field-name" />
<col class="field-body" />
<tbody valign="top">
<tr class="field-odd field"><th class="field-name">Parameters:</th><td class="field-body"><ul class="first simple">
<li><strong>$open</strong> (<em>string</em>) – Error message opening tag</li>
<li><strong>$close</strong> (<em>string</em>) – Error message closing tag</li>
</ul>
</td>
</tr>
<tr class="field-even field"><th class="field-name">Returns:</th><td class="field-body"><p class="first">Error messages</p>
</td>
</tr>
<tr class="field-odd field"><th class="field-name">Return type:</th><td class="field-body"><p class="first last">string</p>
</td>
</tr>
</tbody>
</table>
<p>Returns all detected errors formatted as a string.</p>
<div class="highlight-ci"><div class="highlight"><pre><span></span><span class="k">echo</span> <span class="nv">$this</span><span class="o">-&gt;</span><span class="na">image_lib</span><span class="o">-&gt;</span><span class="na">display_errors</span><span class="p">();</span>
</pre></div>
</div>
</dd></dl>

</dd></dl>

</div>
</div>


          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="input.html" class="btn btn-neutral float-right" title="Input Class">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
        <a href="ftp.html" class="btn btn-neutral" title="FTP Class"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      
    </div>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2014 - 2019, British Columbia Institute of Technology.
      Last updated on Sep 19, 2019.
    </p>
  </div>

  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
</footer>
        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'../',
            VERSION:'3.1.11',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  false
        };
    </script>
      <script type="text/javascript" src="../_static/jquery.js"></script>
      <script type="text/javascript" src="../_static/underscore.js"></script>
      <script type="text/javascript" src="../_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="../_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>