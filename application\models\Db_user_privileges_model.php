<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Db_user_privileges_model extends CI_Model
{

    public $table = 'db_user_privileges';
    public $id = '';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }
	function give_access($arr,$user){
		if($arr){

			for($i=0;$i <count($arr);$i++){


				$menu=array(
					'user'=>$user,
					'company_id'=>$this->session->userdata('company_id'),
					'privilege' =>$arr[$i],

				);

				$this->db->insert($this->table,$menu);


			}
			return true;

		}
		else{
			echo "array not passed";
			exit();
		}
	}
	function give_access2($arr,$user){
		if($arr){
          $this->db->where('user',$user)->delete($this->table);
			for($i=0;$i <count($arr);$i++){


				$menu=array(
					'user'=>$user,
					'company_id'=>$this->session->userdata('company_id'),
					'privilege' =>$arr[$i],

				);

				$this->db->insert($this->table,$menu);


			}
			return true;

		}
		else{
			return  true;

		}
	}
    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('', $q);
	$this->db->or_like('p_id', $q);
	$this->db->or_like('user', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('privilege', $q);
	$this->db->or_like('date_added', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('', $q);
	$this->db->or_like('p_id', $q);
	$this->db->or_like('user', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('privilege', $q);
	$this->db->or_like('date_added', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Db_user_privileges_model.php */
/* Location: ./application/models/Db_user_privileges_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-23 05:44:18 */
/* http://harviacode.com */
