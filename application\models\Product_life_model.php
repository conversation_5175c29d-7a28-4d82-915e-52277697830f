<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Product_life_model extends CI_Model
{

    public $table = 'product_life';
    public $id = 'product_life_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }
    function get_all_by_id($owener)
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->select('*')
            ->where('product_owner',$owener);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('product_life_id', $q);
	$this->db->or_like('product_type', $q);
	$this->db->or_like('product_id', $q);
	$this->db->or_like('product_name', $q);
	$this->db->or_like('product_price', $q);
	$this->db->or_like('expire_date', $q);
	$this->db->or_like('product_owner', $q);
	$this->db->or_like('updated_date', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('product_life_id', $q);
	$this->db->or_like('product_type', $q);
	$this->db->or_like('product_id', $q);
	$this->db->or_like('product_name', $q);
	$this->db->or_like('product_price', $q);
	$this->db->or_like('expire_date', $q);
	$this->db->or_like('product_owner', $q);
	$this->db->or_like('updated_date', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}
