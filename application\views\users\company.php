<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <div class="card">
                <div class="card-body">
                    <div class="row" style="margin-bottom: 10px">
                        <div class="col-md-4">
                            <?php echo anchor(site_url('users/add'),'Create user', 'class="btn btn-primary"'); ?>
                        </div>
                        <div class="col-md-4 text-center">
                            <div style="margin-top: 8px" id="message">
                                <?php echo $this->session->userdata('message') <> '' ? $this->session->userdata('message') : ''; ?>
                            </div>
                        </div>
                        <div class="col-md-1 text-right">
                        </div>
                        <div class="col-md-3 text-right">

                        </div>
                    </div>
                    <table id="copy-print-csv" class="table v-middle">
                        <thead>
                        <tr>

                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Profile Photo</th>
                            <th>Date Added</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <?php
                        foreach ($users_data as $users)
                        {
                            ?>
                            <tr>

                                <td><?php echo $users->full_name ?></td>
                                <td><?php echo $users->email ?></td>


                                <td><?php echo $users->profile_photo ?></td>
                                
                                <td><?php echo $users->date_added ?></td>
                                <td style="text-align:center" width="200px">
                                    <?php

                                    echo anchor(site_url('users/update_user/'.$users->user_id),'Update','class="btn btn-success"');
                                    echo ' | ';
                                    echo anchor(site_url('users/delete_user/'.$users->user_id),'Delete','class="btn btn-danger"');
                                    ?>
                                </td>
                            </tr>
                            <?php
                        }
                        ?>
                        </tbody>
                    </table>
                    <div class="row">


                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
