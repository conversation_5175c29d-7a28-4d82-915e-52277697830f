<?php
$company = check_exist_in_table('company','company_id ',$this->session->userdata('company_id'));
$company_db_user = $this->Database_user_model->get_by_company();
?>
<!-- Content wrapper start -->
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">
				<div class="card-body">
					<h5>MySQL databases user privilege wizard</h5>
					<hr>
					<div style="text-align:center;margin-top:40px;">
						<span class="step">1</span>Database name
						<span class="step">2</span>Database user
						<span class="step">3</span>Database privileges
					</div>

					<form id="regForm" action="<?php echo base_url('company_database/update_it')?>" method="post">
						<?php
						if($this->session->flashdata('error')){
							?>
							<div class="alert alert-danger" role="alert">
								<ul>

									<?php
									foreach ($this->session->flashdata('error') as $err=>$value){
										echo "<li>-";
										echo $value;
										echo "</li>";
									}
									?>
								</ul>
							</div>
							<?php
						}
						?>
						<h5>Database user privilege update</h5>
						<!-- One "tab" for each step in the form: -->
						<div class="tab">Database Name:
							<div class="field-wrapper">
								<div class="input-group">
									<span class="input-group-text"><?php echo $company->company_dir; ?>_</span>

									<input type="text" class="form-control" name="dbname"  readonly value="<?php  echo $name; ?>" placeholder="Enter database name">
								</div>
								<div class="field-placeholder">Database name</div>
							</div>

						</div>
						<div class="tab">Database user:
							<div class="field-wrapper">
								<div class="input-group">
									<select  name="dbuser"  id="" class="form-control" required>
										<option value="">--Select database user--</option>
										<?php

										foreach ($company_db_user as $dd){
											?>
											<option value="<?php echo  $dd->database_user ?>"><?php  echo $dd->username ?></option>
										<?php
										}
										?>
									</select>

								</div>
								<div class="field-placeholder">database user</div>
							</div>


						</div>
						<div class="tab">User Privileges:
							<div class="row">
								<div class="col-lg-6">
									<table>

									<tr>
										<td>ALTER</td><td><input type="checkbox"  name='p_id[]' value="ALTER" ></td>
									</tr>
									<tr>
										<td>ALTER ROUTINE</td><td><input type="checkbox" value="ALTER ROUTINE"></td>
									</tr>
									<tr>
										<td>CREATE</td><td>	<input type="checkbox" name='p_id[]' value="CREATE" ></td>
									</tr>
										<tr>
										<td>CREATE ROUTINE</td><td>	<input type="checkbox"  name="p_id[]" value="CREATE ROUTINE"></td>
									</tr>
									<tr>
										<td>CREATE TEMPORARY TABLES</td><td><input type="checkbox" name="p_id[]" value="CREATE TEMPORARY TABLES"></td>
									</tr>
									<tr>
										<td>CREATE VIEW</td><td>	<input type="checkbox" value="CREATE VIEW" name="p_id[]"></td>
									</tr>
										<tr>
										<td>DELETE</td><td>	<input type="checkbox" value="DELETE" name="p_id[]"></td>
									</tr>
										<tr>
										<td>DROP</td><td><input type="checkbox" value="DROP" name="p_id[]"></td>
									</tr>
									<tr>
										<td>EVENT</td><td><input type="checkbox" value="EVENT" name="p_id[]"></td>
									</tr>

									</table>
								</div>
								<div class="col-lg-6">
									<table>
										<tr>
											<td>EXECUTE</td><td><input type="checkbox" value="EXECUTE" name="p_id[]"></td>
										</tr>
										<tr>
											<td>INDEX</td><td><input type="checkbox"  value="INDEX" name="p_id[]"></td>
										</tr>
										<tr>
											<td>INSERT</td><td><input type="checkbox"  value="INSERT" name="p_id[]"></td>
										</tr>
										<tr>
											<td>LOCK TABLES</td><td><input type="checkbox" value="LOCK TABLES" name="p_id[]"></td>
										</tr>
										<tr>
											<td>REFERENCES</td><td><input type="checkbox" value="REFERENCES" name="p_id[]"></td>
										</tr>
										<tr>
											<td>SELECT</td><td><input type="checkbox" value="SELECT" name="p_id[]"></td>
										</tr>
										<tr>
											<td>SHOW VIEW</td><td><input type="checkbox" value="SHOW VIEW" name="p_id[]"></td>
										</tr>
										<tr>
											<td>TRIGGER</td><td><input type="checkbox" value="TRIGGER" name="p_id[]"></td>
										</tr>
										<tr>
											<td>UPDATE</td><td><input type="checkbox" value="UPDATE" name="p_id[]"></td>
										</tr>
									</table>

								</div>
							</div>


						</div>

						<div style="overflow:auto;">
							<div style="float:right;">
								<button type="button" id="prevBtn" onclick="nextPrev(-1)">Previous</button>
								<button type="button" id="nextBtn" onclick="nextPrev(1)">Next</button>
							</div>
						</div>
						<!-- Circles which indicates the steps of the form: -->

					</form>

				</div>
			</div>

		</div>
	</div>
	<!-- Row end -->

</div>
<!-- Content wrapper end -->
