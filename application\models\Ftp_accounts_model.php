<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Ftp_accounts_model extends CI_Model
{

    public $table = 'ftp_accounts';
    public $id = 'ftp_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    function get_by_id2($id)
    {
        $this->db->where($this->id, $id);
		$this->db->where('company_id', $this->session->userdata('company_id'));
        return $this->db->get($this->table)->row();
    }
    function get_mine($id)
    {
        $this->db->where('company_id', $id);
        return $this->db->get($this->table)->result();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('ftp_id', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('username', $q);
	$this->db->or_like('domain', $q);
	$this->db->or_like('homedir', $q);
	$this->db->or_like('quota', $q);
	$this->db->or_like('quota_date', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('ftp_id', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('username', $q);
	$this->db->or_like('domain', $q);
	$this->db->or_like('homedir', $q);
	$this->db->or_like('quota', $q);
	$this->db->or_like('quota_date', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Ftp_accounts_model.php */
/* Location: ./application/models/Ftp_accounts_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-18 18:07:49 */
/* http://harviacode.com */
