<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Company_domains_model extends CI_Model
{

    public $table = 'company_domains';
    public $id = 'domain_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->join('company','company.company_id=company_domains.company_id');
        return $this->db->get($this->table)->result();
    }
    function get_all_by_id($id)
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('company_id',$id);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function count_my_domains($company) {
    	$this->db->from($this->table)
			->where('company_id',$company);
        return $this->db->count_all_results();
    }
    function get_my_domains($company) {
    	$this->db->from($this->table)
			->where('company_id',$company);
        return $this->db->get()->result();
    }
    function total_rows($q = NULL) {
        $this->db->like('domain_id', $q);
	$this->db->or_like('domain_name', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('domain_file_path', $q);
	$this->db->or_like('domain_stamp', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('domain_id', $q);
	$this->db->or_like('domain_name', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('domain_file_path', $q);
	$this->db->or_like('domain_stamp', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
        return $this->db->insert_id();
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}


