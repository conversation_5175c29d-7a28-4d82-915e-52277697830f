<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Product_payments_model extends CI_Model
{

    public $table = 'product_payments';
    public $id = 'product_payment_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->select('*')->from($this->table)->join('product_life','product_life.product_life_id = product_payments.product_life_id')
        ->join('payment_methods','payment_methods.payment_method_id = product_payments.payment_method');
        return $this->db->get()->result();
    }
    function get_co($owner)
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->select('*')->from($this->table)->join('product_life','product_life.product_life_id = product_payments.product_life_id')
        ->join('payment_methods','payment_methods.payment_method_id = product_payments.payment_method');
        $this->db->where('product_life.product_owner',$owner);
        return $this->db->get()->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('product_payment_id', $q);
	$this->db->or_like('product_life_id', $q);
	$this->db->or_like('payment_method', $q);
	$this->db->or_like('reference', $q);
	$this->db->or_like('amount', $q);
	$this->db->or_like('payment_date', $q);
	$this->db->or_like('pop', $q);
	$this->db->or_like('stamp', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('product_payment_id', $q);
	$this->db->or_like('product_life_id', $q);
	$this->db->or_like('payment_method', $q);
	$this->db->or_like('reference', $q);
	$this->db->or_like('amount', $q);
	$this->db->or_like('payment_date', $q);
	$this->db->or_like('pop', $q);
	$this->db->or_like('stamp', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Product_payments_model.php */
/* Location: ./application/models/Product_payments_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2022-05-17 12:52:06 */
/* http://harviacode.com */
