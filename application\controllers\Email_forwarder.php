<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');
require APPPATH . '/libraries/CpanelApi.php';
class Email_forwarder extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->confi = config_cpanel();
        $this->cpanel = new cpanelAPI($this->confi['servername'],$this->confi['password'],$this->confi['ip']);
        $this->load->model('Email_forwarder_model');
        $this->load->model('Company_domains_model');
        $this->load->model('Company_model');
        
        $this->load->library('form_validation');
    }

    public function index()
    {
        $data['email_forwarder_data'] = $this->Email_forwarder_model->get_all($this->session->userdata('company_id'));
        $config['active_menu'] = "emails";
        $config['current_link'] = "email_forwarder";
        $this->load->view('header',$config);
        $this->load->view('email_forwarder/email_forwarder_list',$data);
        $this->load->view('footer');

    }

    public function read($id) 
    {
        $row = $this->Email_forwarder_model->get_by_id($id);
        if ($row) {
            $data = array(
		'email_forwarder_id' => $row->email_forwarder_id,
		'company_id' => $row->company_id,
		'forwarder' => $row->forwarder,
		'forwarded_to' => $row->forwarded_to,
		'date_added' => $row->date_added,
	    );
            $this->load->view('email_forwarder/email_forwarder_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('email_forwarder'));
        }
    }
    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('email_forwarder/create_action'),
	    'email_forwarder_id' => set_value('email_forwarder_id'),
	    'company_id' => set_value('company_id'),
	    'forwarder' => set_value('forwarder'),
	    'forwarded_to' => set_value('forwarded_to'),
	    'date_added' => set_value('date_added'),
	);
        $config['active_menu'] = "emails";
        $config['current_link'] = "email_forwarder";
        $this->load->view('header',$config);
        $this->load->view('email_forwarder/email_forwarder_form', $data);
        $this->load->view('footer');

    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'company_id' => $this->session->userdata('company_id'),
		'forwarder' => $this->input->post('forwarder',TRUE),
		'forwarded_to' => $this->input->post('forwarded_to',TRUE),

	    );

            $response = $this->cpanel->uapi->Email->add_forwarder( array (
                'email' => $data['forwarder'],
                'domain' => $this->input->post('domain',TRUE),
                'fwdopt' => 'fwd',
                'fwdemail'=>  $this->input->post('forwarded_to',TRUE)


            ));
            if ($response->status == 0){

                $this->session->set_flashdata('error',$response->errors);
                redirect($_SERVER["HTTP_REFERER"]);
            }else{
                $this->Email_forwarder_model->insert($data);
                $this->toaster->success('Success !, email forwarder was added successfully');
                redirect(site_url('email_forwarder'));
            }

            $this->Email_forwarder_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('email_forwarder'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Email_forwarder_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('email_forwarder/update_action'),
		'email_forwarder_id' => set_value('email_forwarder_id', $row->email_forwarder_id),
		'company_id' => set_value('company_id', $row->company_id),
		'forwarder' => set_value('forwarder', $row->forwarder),
		'forwarded_to' => set_value('forwarded_to', $row->forwarded_to),
		'date_added' => set_value('date_added', $row->date_added),
	    );
            $this->load->view('email_forwarder/email_forwarder_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('email_forwarder'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('email_forwarder_id', TRUE));
        } else {
            $data = array(
		'company_id' => $this->input->post('company_id',TRUE),
		'forwarder' => $this->input->post('forwarder',TRUE),
		'forwarded_to' => $this->input->post('forwarded_to',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Email_forwarder_model->update($this->input->post('email_forwarder_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('email_forwarder'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Email_forwarder_model->get_by_id($id);

        if ($row) {

            $response = $this->cpanel->uapi->Email->delete_forwarder( array (
                'address' => $row->forwarder,
                'forwarder' => $row->forwarded_to



            ));
            if ($response->status == 0){

                $this->toaster->error($response->errors);

                redirect($_SERVER["HTTP_REFERER"]);
            }else{
                $this->Email_forwarder_model->delete($id);
                $this->toaster->success('Success !, email forwarder was deleted successfully');
                redirect(site_url('email_forwarder'));
            }


        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('email_forwarder'));
        }
    }

    public function _rules() 
    {

	$this->form_validation->set_rules('forwarder', 'forwarder', 'trim|required');
	$this->form_validation->set_rules('forwarded_to', 'forwarded to', 'trim|required');


	$this->form_validation->set_rules('email_forwarder_id', 'email_forwarder_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

