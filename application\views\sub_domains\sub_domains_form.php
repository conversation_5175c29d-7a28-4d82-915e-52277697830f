<?php
$domains = $this->Company_domains_model->get_my_domains($this->session->userdata('company_id'));
$company = $this->Company_model->company_details($this->session->userdata('company_id'));

?>
<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-lg-12">

			<div class="card">
				<div class="card-body" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">
        <h2 style="margin-top:0px">Sub domains creation</h2>
					<?php
					if($this->session->flashdata('error')){
						?>
						<div class="alert alert-danger" role="alert">
							<ul>

							<?php
							foreach ($this->session->flashdata('error') as $err=>$value){
								echo "<li>-";
								echo $value;
								echo "</li>";
							}
							?>
							</ul>
						</div>
					<?php
					}
					?>
        <form action="<?php echo $action; ?>" method="post">
<div class="row">
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">

				<div class="field-wrapper">
					<select class="select-single js-states" name="domain"  title="Select Domain">
						<option value="">--select domain--</option>
						<?php
						foreach ($domains as $d){
							?>
							<option value="<?php echo $d->domain_name ?>"><?php echo $d->domain_name ?></option>
						<?php
						}
						?>
					</select>
					<div class="field-placeholder">Domain <?php echo form_error('domain') ?></div>
				</div>
			</div>
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
				<div class="field-wrapper">
					<input class="form-control" name="name" type="text" placeholder="Enter Subdomain name">
					<div class="field-placeholder">Subdomain name <?php echo form_error('name') ?> <span class="text-danger">*</span></div>
				</div>
			</div>
			<div class="col-xl-4 col-lg-4 col-md-4 col-sm-4 col-12">
				<div class="field-wrapper">
					<div class="input-group">
						<span class="input-group-text"><?php echo $company->company_dir; ?>/</span>
						<input type="text" name="fullpath" value="<?php echo $company->company_dir; ?>" hidden>
						<input type="text" class="form-control" name="dir"  placeholder="Enter folder name">
					</div>
					<div class="field-placeholder">Directory path  <?php echo form_error('dir') ?></div>
				</div>
			</div>
</div>
	    <input type="hidden" name="sub_domain_id" value="<?php echo $sub_domain_id; ?>" /> 
	    <button type="submit" class="btn btn-primary"><?php echo $button ?></button> 

	</form>
				</div>
			</div>
		</div>
	</div>
</div>
