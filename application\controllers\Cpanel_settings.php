<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Cpanel_settings extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Cpanel_settings_model');
        $this->load->library('form_validation');
    }

    public function upload()
    {
        if ( ! empty($_FILES))
        {
            $res=array();
            $config['upload_path'] = "./uploads";

            $config['allowed_types'] = "jpg|png|jpeg|PNG|pdf|doc|docx";
            $config['file_name'] = uniqid('').rand(100,9999);
            $this->load->library('upload', $config);
            if (! $this->upload->do_upload("file")) {
                $res['status']='error';

                $res['message']='Sorry Something went wrong, we could not upload your file ';

            }else{
                $uploadData = $this->upload->data();
                $file = $uploadData['file_name'];
                $res['status']='success';

                $res['message']='Success, File uploaded ';

                $res['data']=array(
                    'file_name'=>$file
                );


            }

        } else {
            $res['status']='error';

            $res['message']='Sorry file can not be empty ';

        }
        echo json_encode($res);
    }
    public function update($id) 
    {
        $row = $this->Cpanel_settings_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('cpanel_settings/update_action'),
		'id' => set_value('id', $row->id),
		'cpanel_user' => set_value('cpanel_user', $row->cpanel_user),
		'cpanel_root_path' => set_value('cpanel_root_path', $row->cpanel_root_path),
		'logo' => set_value('logo', $row->logo),
		'company_name' => set_value('company_name', $row->company_name),
		'login_page_heading' => set_value('login_page_heading', $row->login_page_heading),
		'login_page_subtextt' => set_value('login_page_subtextt', $row->login_page_subtextt),
		'username' => set_value('username', $row->username),
		'hosting_ip' => set_value('hosting_ip', $row->hosting_ip),
		'password' => set_value('password', $row->password),
		'company_phone' => set_value('company_phone', $row->company_phone),
		'company_email' => set_value('company_email', $row->company_email),
		'currency' => set_value('currency', $row->currency),
		'protocal' => set_value('protocal', $row->protocal),
		'email_host' => set_value('email_host', $row->email_host),
		'email_port' => set_value('email_port', $row->email_port),
		'email_user' => set_value('email_user', $row->email_user),
		'email_pass' => set_value('email_pass', $row->email_pass)
	    );
			$config['active_menu'] = "server_config";
			$config['current_link'] = "server_config";
			$this->load->view('header',$config);
            $this->load->view('cpanel_settings/cpanel_settings_form', $data);
			$this->load->view('footer');
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('cpanel_settings/update/1'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('id', TRUE));
        } else {
            $data = array(
		'cpanel_user' => $this->input->post('cpanel_user',TRUE),
		'cpanel_root_path' => $this->input->post('cpanel_root_path',TRUE),
		'company_name' => $this->input->post('company_name',TRUE),
		'logo' => $this->input->post('logo',TRUE),
		'password' => $this->input->post('password',TRUE),
		'username' => $this->input->post('username',TRUE),
		'hosting_ip' => $this->input->post('hosting_ip',TRUE),
		'company_phone' => $this->input->post('company_phone',TRUE),
		'company_email' => $this->input->post('company_email',TRUE),
         'currency' => $this->input->post('currency',TRUE),
                'protocal' => $this->input->post('protocal',TRUE),
                'email_host' => $this->input->post('email_host',TRUE),
                'email_port' => $this->input->post('email_port',TRUE),
                'email_user' => $this->input->post('email_user',TRUE),
                'email_pass' => $this->input->post('email_pass',TRUE)
	    );

            $this->Cpanel_settings_model->update($this->input->post('id', TRUE), $data);
			$this->toaster->success('Success, Settings were updated successfully');
			redirect(site_url('cpanel_settings/update/1'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Cpanel_settings_model->get_by_id($id);

        if ($row) {
            $this->Cpanel_settings_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('cpanel_settings'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('cpanel_settings'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('cpanel_user', 'cpanel user', 'trim|required');
	$this->form_validation->set_rules('cpanel_root_path', 'cpanel root path', 'trim|required');

	$this->form_validation->set_rules('id', 'id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

