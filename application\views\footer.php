<!-- App footer start -->



<div class="app-footer">© Extend Cpanel by Infocus technologies Malawi 2021</div>
<!-- App footer end -->

</div>
<!-- Content wrapper scroll end -->

</div>
<!-- *************
	************ Main container end *************
************* -->

</div>
<!-- Page wrapper end -->

<!-- *************
	************ Required JavaScript Files *************
************* -->
<!-- Required jQuery first, then Bootstrap Bundle JS -->
<script src="<?php echo base_url('cpanel_assets/')?>js/jquery.min.js"></script>

<script src="<?php echo base_url('cpanel_assets/')?>js/bootstrap.bundle.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>js/modernizr.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>js/moment.js"></script>

<!-- *************
	************ Vendor Js Files *************
************* -->

<!-- Megamenu JS -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/megamenu/js/megamenu.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/megamenu/js/custom.js"></script>

<!-- Slimscroll JS -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/slimscroll/slimscroll.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/slimscroll/custom-scrollbar.js"></script>

<!-- Search Filter JS -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/search-filter/search-filter.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/search-filter/custom-search-filter.js"></script>

<!-- Apex Charts -->

<!-- Data Tables -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/dataTables.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/dataTables.bootstrap.min.js"></script>

<!-- Custom Data tables -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/custom/custom-datatables.js"></script>

<!-- Download / CSV / Copy / Print -->
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/buttons.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/jszip.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/pdfmake.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/vfs_fonts.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/html5.min.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>vendor/datatables/buttons.print.min.js"></script>
<!-- Main Js Required -->
<script src="<?php echo base_url('cpanel_assets/')?>js/main.js"></script>
<script src="<?php echo base_url('assets/')?>js/select2.js"></script>
<script src="<?php echo base_url('cpanel_assets/')?>toaster/toastr.min.js"></script>
<script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.12.1/jquery-ui.min.js"></script>
<!-- elFinder JS (REQUIRED) -->
<script type="text/javascript" src="<?php echo base_url() ?>js/elfinder.full.js"></script>

<!-- elFinder translation (OPTIONAL) -->
<script type="text/javascript" src="<?php echo base_url() ?>js/i18n/elfinder.ru.js"></script>

<!-- elFinder initialization (REQUIRED) -->
<script type="text/javascript" charset="utf-8">
	$(document).ready(function() {
		var elf = $('#elfinder').elfinder({
			url : '<?php echo base_url("index.php/Fmgr/elfinder_init") ?>	'  // connector URL (REQUIRED)
			// lang: 'ru',             // language (OPTIONAL)
		}).elfinder('instance');
	});
    $(document).ready(function() {
        $('#sele').select2();
        $('.sele').select2();
    });
</script>
<script>
    let baseURL = '<?php echo base_url()?>';
	var currentTab = 0; // Current tab is set to be the first tab (0)
	showTab(currentTab); // Display the current tab

	function showTab(n) {
		// This function will display the specified tab of the form...
		var x = document.getElementsByClassName("tab");
		x[n].style.display = "block";
		//... and fix the Previous/Next buttons:
		if (n == 0) {
			document.getElementById("prevBtn").style.display = "none";
		} else {
			document.getElementById("prevBtn").style.display = "inline";
		}
		if (n == (x.length - 1)) {
			document.getElementById("nextBtn").innerHTML = "Submit";
		} else {
			document.getElementById("nextBtn").innerHTML = "Next";
		}
		//... and run a function that will display the correct step indicator:
		fixStepIndicator(n)
	}

	function nextPrev(n) {
		// This function will figure out which tab to display
		var x = document.getElementsByClassName("tab");
		// Exit the function if any field in the current tab is invalid:
		if (n == 1 && !validateForm()) return false;
		// Hide the current tab:
		x[currentTab].style.display = "none";
		// Increase or decrease the current tab by 1:
		currentTab = currentTab + n;
		// if you have reached the end of the form...
		if (currentTab >= x.length) {
			// ... the form gets submitted:
			document.getElementById("regForm").submit();
			return false;
		}
		// Otherwise, display the correct tab:
		showTab(currentTab);
	}

	function validateForm() {
		// This function deals with validation of the form fields
		var x, y, i, valid = true;
		x = document.getElementsByClassName("tab");
		y = x[currentTab].getElementsByTagName("input");
		// A loop that checks every input field in the current tab:
		for (i = 0; i < y.length; i++) {
			// If a field is empty...
			if (y[i].value == "") {
				// add an "invalid" class to the field:
				y[i].className += " invalid";
				// and set the current valid status to false
				valid = false;
			}
		}
		// If the valid status is true, mark the step as finished and valid:
		if (valid) {
			document.getElementsByClassName("step")[currentTab].className += " finish";
		}
		return valid; // return the valid status
	}

	function fixStepIndicator(n) {
		// This function removes the "active" class of all steps...
		var i, x = document.getElementsByClassName("step");
		for (i = 0; i < x.length; i++) {
			x[i].className = x[i].className.replace(" active", "");
		}
		//... and adds the "active" class on the current step:
		x[n].className += " active";
	}
</script>
<script>
	<?php if ($this->session->flashdata('success')) {?>
	toastr["success"]("<?php echo $this->session->flashdata('success'); ?>");
	<?php } else if ($this->session->flashdata('error')) {?>
	toastr["error"]("<?php echo $this->session->flashdata('error'); ?>");
	<?php } else if ($this->session->flashdata('warning')) {?>
	toastr["warning"]("<?php echo $this->session->flashdata('warning'); ?>");
	<?php } else if ($this->session->flashdata('info')) {?>
	toastr["info"]("<?php echo $this->session->flashdata('info'); ?>");
	<?php }?>
	toastr.options = {
		"closeButton": false,
		"debug": true,
		"newestOnTop": false,
		"progressBar": true,
		"rtl": false,
		"positionClass": "toast-top-center",
		"preventDuplicates": false,
		"onclick": null,
		"showDuration": 300,
		"hideDuration": 1000,
		"timeOut": 5000,
		"extendedTimeOut": 1000,
		"showEasing": "swing",
		"hideEasing": "linear",
		"showMethod": "fadeIn",
		"hideMethod": "fadeOut"
	}
</script>
<script>
	$(document).ready(function (){
		$("#dm").change(function (){
			$("#cdomain").html($("#dm").val())
		})
        $("#dyes").click(function (){
			$("#disd").show();
		})
        $("#dno").click(function (){
			$("#disd").hide();
		})
        $("#domain").change(function (){
            $.ajax({
                url: "<?php echo base_url()?>Company_emails/get_my_emails/"+$("#domain").val(),
                method: "GET",
                contentType:false,
                cache: false,
                processData: false,
              
                beforeSend: () => {
                    // $("#ppp").attr("disabled", true);
                    // $("#ppp").html("<i class='fa fa-spinner fa-spin'></i>Uploading file please wait");
                },
                success: (response)=>{
                    // $("#ppp").attr("disabled", false);
                    // $("#ppp").html("<i class='fa fa-check bg-success' style='color: green;'>File was uploaded</i>");



                        // alert(response.data.file_name);
                        // successToast('Success','file was successfully you may proceed')
                        // $("#"+id+"1").val(response.data.file_name);
                        $("#forwarder").html(response)








                }, error: (xht, error, e)=>{
                    // $("#pvu").html("");
                    alert("Error "+xht.status);

                }
            });


		})
        $("#product_life_id").change(function (){
            $.ajax({
                url: "<?php echo base_url()?>Product_life/get_d/"+$("#product_life_id").val(),
                method: "GET",
                contentType:false,
                cache: false,
                processData: false,

                beforeSend: () => {
                    // $("#ppp").attr("disabled", true);
                    // $("#ppp").html("<i class='fa fa-spinner fa-spin'></i>Uploading file please wait");
                },
                success: (response)=>{
                    // $("#ppp").attr("disabled", false);
                    // $("#ppp").html("<i class='fa fa-check bg-success' style='color: green;'>File was uploaded</i>");



                        // alert(response.data.file_name);
                        // successToast('Success','file was successfully you may proceed')
                        // $("#"+id+"1").val(response.data.file_name);
                        $("#exp_d").val(response)








                }, error: (xht, error, e)=>{
                    // $("#pvu").html("");
                    alert("Error "+xht.status);

                }
            });


		})
		//check if password match



		$('#pass2').focusout(function(){
			var pass = $('#pass').val();
			var pass2 = $('#pass2').val();
			if(pass !== pass2){
				$("#sbtn").attr("disabled", true);
				$("#passdiv").html('<div class="alert alert-danger" role="alert">Sorry passwords did not match </div>')
			}
			if(pass === pass2){
				$("#sbtn").attr("disabled", false);
				$("#passdiv").html('<div class="alert alert-success" role="alert">Passwords matched</div>')
			}

		});
	})
	function ajaxb(){
		var user_id ={
			'session':'<EMAIL>:1orQKCxXI5MT5UXv:CREATE_WEBMAIL_SESSION_FOR_MAIL_USER,9f4695236e2bf8469233f5882b8c4674'
		}
		$.ajax({
			url: 'https://cpanel-box2150.bluehost.com/cpsess6809962673/login',

			dataType: 'text',
			type: 'POST',
			cache:false,
			data: user_id,

			success: function(data){


			},
			error: function(jqXHR, textStatus, errorThrown){
				alert('Error: ' + textStatus + ' - ' + errorThrown);
			}
		});
	}


    function uploadfile(id) {

        uploadd(document.getElementById(id).files[0],id);
    }
    function uploadd(file,id){

        let formData = new FormData();
        let photo = file;
        formData.append("file", photo);

        $.ajax({
            url: "<?php echo base_url()?>Cpanel_settings/upload",
            method: "POST",
            contentType:false,
            data: formData,
            cache: false,
            processData: false,
            dataType:"json",
            beforeSend: () => {
                $("#pp").attr("disabled", true);
                $("#pp").html("<i class='fa fa-spinner fa-spin'></i>Uploading file please wait");
            },
            success: (response)=>{
                $("#pp").attr("disabled", false);
                $("#pp").html("<i class='fa fa-check bg-success' style='color: green;'>File was uploaded</i>");

                if (response.status == "success") {

                    // alert(response.data.file_name);
                    // successToast('Success','file was successfully you may proceed')
                    $("#"+id+"1").val(response.data.file_name);
                    $("#preview_data").html('<img style="border: solid blue thick; border-radius: 15px;" src="'+baseURL+'uploads/'+response.data.file_name+'" alt="featured image" width="150" height="70">')


                } else {
                    // $("#pvu").html("");

                    alert('Sorry something went wrong when uploading, please try again');
                }

            }, error: (xht, error, e)=>{
                // $("#pvu").html("");
                alert("Error "+xht.status);

            }
        });


    }
</script>

</body>
</html>
