<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Home extends CI_Controller {

    public function __construct()
    {
        parent::__construct();
        $this->load->helper('url');
        $this->load->library('form_validation');
        $this->load->library('email');
        $this->load->helper('common_queries');
    }

    /**
     * Landing page
     */
    public function index()
    {
        $data['page_title'] = 'Professional Email Hosting - 98,000 Kwacha Only';
        $data['meta_description'] = 'Get professional email hosting with free domain. 5 emails for just 98,000 Kwacha per year. Reliable, secure, and affordable hosting solutions.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        $this->load->view('landing/index', $data);
    }

    /**
     * About page
     */
    public function about()
    {
        $data['page_title'] = 'About Us - Professional Hosting Services';
        $data['meta_description'] = 'Learn about our professional hosting services, our mission, and why thousands of customers trust us with their email and web hosting needs.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        $this->load->view('landing/about', $data);
    }

    /**
     * Contact page
     */
    public function contact()
    {
        $data['page_title'] = 'Contact Us - Get in Touch';
        $data['meta_description'] = 'Contact our support team for any questions about our hosting services. We\'re here to help you get started.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        // Handle contact form submission
        if ($this->input->post()) {
            $this->form_validation->set_rules('name', 'Name', 'required|trim');
            $this->form_validation->set_rules('email', 'Email', 'required|valid_email|trim');
            $this->form_validation->set_rules('subject', 'Subject', 'required|trim');
            $this->form_validation->set_rules('message', 'Message', 'required|trim');

            if ($this->form_validation->run()) {
                // Process contact form
                $this->_send_contact_email();
                $data['success_message'] = 'Thank you for your message. We will get back to you soon!';
            } else {
                $data['error_message'] = 'Please fill in all required fields correctly.';
            }
        }

        $this->load->view('landing/contact', $data);
    }

    /**
     * Pricing page
     */
    public function pricing()
    {
        $data['page_title'] = 'Pricing Plans - Affordable Email Hosting';
        $data['meta_description'] = 'View our affordable email hosting pricing plans. Starting from 98,000 Kwacha per year with free domain included.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        // Define pricing plans
        $data['plans'] = [
            [
                'name' => 'Starter Email',
                'price' => '98,000',
                'period' => 'per year',
                'emails' => '5',
                'storage' => '5GB per email',
                'domain' => 'Free domain included',
                'support' => 'Email support',
                'features' => [
                    '5 Professional email accounts',
                    '5GB storage per email',
                    'Free domain registration',
                    'Webmail access',
                    'Mobile sync (IMAP/POP3)',
                    'Spam protection',
                    'Email forwarding',
                    '99.9% uptime guarantee'
                ],
                'popular' => true
            ],
            [
                'name' => 'Business Email',
                'price' => '158,000',
                'period' => 'per year',
                'emails' => '10',
                'storage' => '10GB per email',
                'domain' => 'Free domain included',
                'support' => 'Priority support',
                'features' => [
                    '10 Professional email accounts',
                    '10GB storage per email',
                    'Free domain registration',
                    'Webmail access',
                    'Mobile sync (IMAP/POP3)',
                    'Advanced spam protection',
                    'Email forwarding & autoresponders',
                    'Calendar & contacts sync',
                    '99.9% uptime guarantee',
                    'Priority email support'
                ],
                'popular' => false
            ],
            [
                'name' => 'Enterprise Email',
                'price' => '258,000',
                'period' => 'per year',
                'emails' => 'Unlimited',
                'storage' => '25GB per email',
                'domain' => 'Free domain included',
                'support' => '24/7 phone support',
                'features' => [
                    'Unlimited email accounts',
                    '25GB storage per email',
                    'Free domain registration',
                    'Advanced webmail interface',
                    'Mobile sync (IMAP/POP3)',
                    'Enterprise spam protection',
                    'Email forwarding & autoresponders',
                    'Calendar & contacts sync',
                    'Email archiving',
                    'Advanced security features',
                    '99.9% uptime guarantee',
                    '24/7 phone & email support'
                ],
                'popular' => false
            ]
        ];

        $this->load->view('landing/pricing', $data);
    }

    /**
     * Privacy Policy page
     */
    public function privacy()
    {
        $data['page_title'] = 'Privacy Policy - emailhost-plus';
        $data['meta_description'] = 'Read our privacy policy to understand how we collect, use, and protect your personal information.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        $this->load->view('landing/privacy', $data);
    }

    /**
     * Terms of Service page
     */
    public function terms()
    {
        $data['page_title'] = 'Terms of Service - emailhost-plus';
        $data['meta_description'] = 'Read our terms of service that govern the use of our email hosting services.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        $this->load->view('landing/terms', $data);
    }

    /**
     * Refund Policy page
     */
    public function refund()
    {
        $data['page_title'] = 'Refund Policy - emailhost-plus';
        $data['meta_description'] = 'Learn about our 30-day money-back guarantee and refund policy for email hosting services.';
        $data['settings'] = get_by_id('cpanel_settings','id','1');

        $this->load->view('landing/refund', $data);
    }

    /**
     * Domain search AJAX endpoint
     */
    public function search_domain()
    {
        if (!$this->input->is_ajax_request()) {
            show_404();
        }

        $domain = $this->input->post('domain');

        if (empty($domain)) {
            echo json_encode(['status' => 'error', 'message' => 'Please enter a domain name']);
            return;
        }

        // Simple domain validation
        $domain = strtolower(trim($domain));
        $domain = str_replace(['http://', 'https://', 'www.'], '', $domain);

        // Add .com if no extension provided
        if (strpos($domain, '.') === false) {
            $domain .= '.com';
        }

        // Simulate domain availability check (in real implementation, you'd use a domain API)
        $available_domains = ['example.com', 'test.com', 'demo.com', 'sample.com'];
        $is_available = in_array($domain, $available_domains) || rand(0, 1);

        echo json_encode([
            'status' => 'success',
            'domain' => $domain,
            'available' => $is_available,
            'message' => $is_available ? 'Domain is available!' : 'Domain is already taken. Try another one.'
        ]);
    }

    /**
     * Send contact form email
     */
    private function _send_contact_email()
    {
        $name = $this->input->post('name');
        $email = $this->input->post('email');
        $subject = $this->input->post('subject');
        $message = $this->input->post('message');

        // Configure email
        $config['protocol'] = 'mail';
        $config['mailtype'] = 'html';
        $config['charset'] = 'utf-8';

        $this->email->initialize($config);

        $this->email->from($email, $name);
        $this->email->to('<EMAIL>');
        $this->email->subject('Contact Form: ' . $subject);

        $email_message = "
        <h3>New Contact Form Submission</h3>
        <p><strong>Name:</strong> {$name}</p>
        <p><strong>Email:</strong> {$email}</p>
        <p><strong>Subject:</strong> {$subject}</p>
        <p><strong>Message:</strong></p>
        <p>{$message}</p>
        ";

        $this->email->message($email_message);

        return $this->email->send();
    }
}
