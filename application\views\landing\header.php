<?php
// Settings are now passed from the controller
if (!isset($settings)) {
    $settings = get_by_id('cpanel_settings','id','1');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title : 'Professional Email Hosting - Affordable & Reliable'; ?></title>
    <meta name="description" content="<?php echo isset($meta_description) ? $meta_description : 'Get professional email hosting with free domain. Starting from 98,000 Kwacha per year. Reliable, secure, and affordable hosting solutions.'; ?>">
    <meta name="keywords" content="email hosting, web hosting, domain registration, professional email, business email, Malawi hosting">
    <meta name="author" content="<?php echo isset($settings->company_name) ? $settings->company_name : 'Professional Hosting'; ?>">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? $page_title : 'Professional Email Hosting'; ?>">
    <meta property="og:description" content="<?php echo isset($meta_description) ? $meta_description : 'Professional email hosting starting from 98,000 Kwacha per year'; ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo current_url(); ?>">
    <meta property="og:image" content="<?php echo base_url('uploads/' . (isset($settings->logo) ? $settings->logo : 'logo.png')); ?>">

    <!-- Favicon -->
    <link rel="shortcut icon" href="<?php echo base_url('cpanel_assets/img/fav.png'); ?>" />

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo base_url('assets/css/landing.css'); ?>">

    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Header -->
    <header class="landing-header">
        <nav class="navbar navbar-expand-lg">
            <div class="container">
                <!-- Brand -->
                <a class="navbar-brand d-flex align-items-center" href="<?php echo base_url(); ?>">
                    <?php if (isset($settings->logo) && !empty($settings->logo)): ?>
                        <img src="<?php echo base_url('uploads/' . $settings->logo); ?>" alt="<?php echo isset($settings->company_name) ? $settings->company_name : 'Professional Hosting'; ?>" height="40" class="me-2">
                    <?php endif; ?>
                    <span><?php echo isset($settings->company_name) ? $settings->company_name : 'Professional Hosting'; ?></span>
                </a>

                <!-- Mobile menu button -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <!-- Navigation -->
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav ms-auto me-4">
                        <li class="nav-item">
                            <a class="nav-link <?php echo (uri_string() == '' || uri_string() == 'home') ? 'active' : ''; ?>" href="<?php echo base_url(); ?>">Home</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (uri_string() == 'pricing') ? 'active' : ''; ?>" href="<?php echo base_url('pricing'); ?>">Pricing</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (uri_string() == 'about') ? 'active' : ''; ?>" href="<?php echo base_url('about'); ?>">About</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link <?php echo (uri_string() == 'contact') ? 'active' : ''; ?>" href="<?php echo base_url('contact'); ?>">Contact</a>
                        </li>
                    </ul>

                    <!-- Login Button -->
                    <a href="<?php echo base_url('login'); ?>" class="btn-login">
                        <i class="fas fa-sign-in-alt me-1"></i>
                        Login
                    </a>
                </div>
            </div>
        </nav>
    </header>

    <!-- Main Content -->
    <main>
