<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Email_forwarder_model extends CI_Model
{

    public $table = 'email_forwarder';
    public $id = 'email_forwarder_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }

    // get all
    function get_all($id)
    {
        $this->db->order_by($this->id, $this->order);
        $this->db->where('company_id',$id);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('email_forwarder_id', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('forwarder', $q);
	$this->db->or_like('forwarded_to', $q);
	$this->db->or_like('date_added', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('email_forwarder_id', $q);
	$this->db->or_like('company_id', $q);
	$this->db->or_like('forwarder', $q);
	$this->db->or_like('forwarded_to', $q);
	$this->db->or_like('date_added', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Email_forwarder_model.php */
/* Location: ./application/models/Email_forwarder_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-11-23 07:31:18 */
/* http://harviacode.com */
