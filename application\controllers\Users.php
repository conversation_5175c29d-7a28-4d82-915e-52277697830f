<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Users extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Users_model');
        $this->load->model('Company_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'users/index?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'users/index?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'users/index';
            $config['first_url'] = base_url() . 'users/index';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Users_model->total_rows($q);
        $users = $this->Users_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'users_data' => $this->Users_model->get_all(),
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
		$config['active_menu'] = "server_config";
		$config['current_link'] = "users";
		$this->load->view('header',$config);
        $this->load->view('users/users_list', $data);
		$this->load->view('footer');
    }

    public function read($id) 
    {
        $row = $this->Users_model->get_by_id($id);
        if ($row) {
            $data = array(
		'user_id' => $row->user_id,
		'full_name' => $row->full_name,
		'email' => $row->email,
		'password' => $row->password,
		'company' => $row->company,
		'profile_photo' => $row->profile_photo,
		'user_role' => $row->user_role,
		'date_added' => $row->date_added,
	    );
            $this->load->view('users/users_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('users'));
        }
    }
    public function company_users(){
        $data = array(
            'users_data' => $this->Users_model->get_all_by_id($this->session->userdata('company_id')),

        );
        $config['active_menu'] = "company";
        $config['current_link'] = "users";
        $this->load->view('header',$config);
        $this->load->view('users/company', $data);
        $this->load->view('footer');
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('users/create_action'),
	    'user_id' => set_value('user_id'),
	    'full_name' => set_value('full_name'),
	    'email' => set_value('email'),
	    'password' => set_value('password'),
	    'company' => set_value('company'),
	    'profile_photo' => set_value('profile_photo'),
	    'user_role' => set_value('user_role'),
	    'date_added' => set_value('date_added'),
	);
		$config['active_menu'] = "server_config";
		$config['current_link'] = "users";
		$this->load->view('header',$config);
		$this->load->view('users/users_form', $data);
		$this->load->view('footer');
    }
    public function add()
    {
        $data = array(
            'button' => 'Add user',
            'action' => site_url('users/add_action'),
            'user_id' => set_value('user_id'),
            'full_name' => set_value('full_name'),
            'email' => set_value('email'),
            'password' => set_value('password'),
            'company' => set_value('company'),
            'profile_photo' => set_value('profile_photo'),
            'user_role' => set_value('user_role'),
            'date_added' => set_value('date_added'),
        );
        $config['active_menu'] = "company";
        $config['current_link'] = "users";
        $this->load->view('header',$config);
        $this->load->view('users/user_form', $data);
        $this->load->view('footer');
    }
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'full_name' => $this->input->post('full_name',TRUE),
		'email' => $this->input->post('email',TRUE),
		'password' => md5($this->input->post('password',TRUE)),
		'company' => $this->input->post('company',TRUE),

	    );

            $this->Users_model->insert($data);
			$this->toaster->success('Success, user was added successfully');
            redirect(site_url('users'));
        }
    } public function add_action()
    {
        $this->_rules1();

        if ($this->form_validation->run() == FALSE) {
            $this->add();
        } else {
            $data = array(
                'full_name' => $this->input->post('full_name',TRUE),
                'email' => $this->input->post('email',TRUE),
                'password' => md5($this->input->post('password',TRUE)),
                'company' => $this->session->userdata('company_id'),

            );

            $this->Users_model->insert($data);
            $this->toaster->success('Success, user was added successfully');
            redirect(site_url('users/company_users'));
        }

    }
    public function profile(){
		$config['active_menu'] = "settings";
		$config['current_link'] = "profile";
		$this->load->view('header',$config);
		$this->load->view('profile');
		$this->load->view('footer');
	}
	public function change_password(){

	}
    public function update($id) 
    {
        $row = $this->Users_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('users/update_action'),
		'user_id' => set_value('user_id', $row->user_id),
		'full_name' => set_value('full_name', $row->full_name),
		'email' => set_value('email', $row->email),
		'password' => set_value('password', $row->password),
		'company' => set_value('company', $row->company),
		'profile_photo' => set_value('profile_photo', $row->profile_photo),
		'user_role' => set_value('user_role', $row->user_role),
		'date_added' => set_value('date_added', $row->date_added),
	    );
			$config['active_menu'] = "server_config";
			$config['current_link'] = "users";
			$this->load->view('header',$config);
			$this->load->view('users/users_form', $data);
			$this->load->view('footer');
        } else {
			$this->toaster->error('Ops!,record not found');
            redirect(site_url('users'));
        }
    }
    public function update_user($id)
    {
        $row = $this->Users_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('users/update_act'),
		'user_id' => set_value('user_id', $row->user_id),
		'full_name' => set_value('full_name', $row->full_name),
		'email' => set_value('email', $row->email),
		'password' => set_value('password', $row->password),
		'company' => set_value('company', $row->company),
		'profile_photo' => set_value('profile_photo', $row->profile_photo),
		'user_role' => set_value('user_role', $row->user_role),
		'date_added' => set_value('date_added', $row->date_added),
	    );
			$config['active_menu'] = "company";
			$config['current_link'] = "users";
			$this->load->view('header',$config);
			$this->load->view('users/user_form', $data);
			$this->load->view('footer');
        } else {
			$this->toaster->error('Ops!,record not found');
            redirect(site_url('users/company_users'));
        }
    }
    
    public function update_action() 
    {
        $this->_ruless();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('user_id', TRUE));
        } else {
            $data = array(
		'full_name' => $this->input->post('full_name',TRUE),

		'company' => $this->input->post('company',TRUE),

	    );

            $this->Users_model->update($this->input->post('user_id', TRUE), $data);
			$this->toaster->success('Success, user was updated successfully');
			redirect(site_url('users'));

        }
    }
    public function update_act()
    {

            $data = array(
		'full_name' => $this->input->post('full_name',TRUE),
	                );

            $this->Users_model->update($this->input->post('user_id', TRUE), $data);
			$this->toaster->success('Success, user was updated successfully');
			redirect(site_url('users/company_users'));


    }
    
    public function delete($id) 
    {
        $row = $this->Users_model->get_by_id($id);

        if ($row) {
            $this->Users_model->delete($id);
            $this->toaster->success('Success, user was deleted successfully');
            redirect(site_url('users'));
        } else {
            $this->toaster->error('Error, user was not deleted ');
            redirect(site_url('users'));
        }
    } public function delete_user($id)
    {
        $row = $this->Users_model->get_by_id($id);

        if ($row) {
            $this->Users_model->delete($id);
            $this->toaster->success('Success, user was deleted successfully');
            redirect(site_url('users/company_users'));
        } else {
            $this->toaster->error('Error, user was not deleted ');
            redirect(site_url('users/company_users'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('full_name', 'full name', 'trim|required');
	$this->form_validation->set_rules('email', 'email', 'trim|required|is_unique[users.email]');

	$this->form_validation->set_rules('company', 'company', 'trim|required');


	$this->form_validation->set_rules('user_id', 'user_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }
    public function _rules1()
    {
	$this->form_validation->set_rules('full_name', 'full name', 'trim|required');
	$this->form_validation->set_rules('email', 'email', 'trim|required|is_unique[users.email]');

	$this->form_validation->set_rules('user_id', 'user_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }
    public function _ruless()
    {
	$this->form_validation->set_rules('full_name', 'full name', 'trim|required');


	$this->form_validation->set_rules('company', 'company', 'trim|required');


	$this->form_validation->set_rules('user_id', 'user_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}


