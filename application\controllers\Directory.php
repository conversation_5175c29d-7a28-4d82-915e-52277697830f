<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Directory extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Directory_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'directory/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'directory/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'directory/index.html';
            $config['first_url'] = base_url() . 'directory/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Directory_model->total_rows($q);
        $directory = $this->Directory_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'directory_data' => $directory,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('directory/directory_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Directory_model->get_by_id($id);
        if ($row) {
            $data = array(
		'id' => $row->id,
		'user' => $row->user,
		'user_dir' => $row->user_dir,
		'date' => $row->date,
	    );
            $this->load->view('directory/directory_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('directory'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('directory/create_action'),
	    'id' => set_value('id'),
	    'user' => set_value('user'),
	    'user_dir' => set_value('user_dir'),
	    'date' => set_value('date'),
	);
        $this->load->view('directory/directory_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'user' => $this->input->post('user',TRUE),
		'user_dir' => $this->input->post('user_dir',TRUE),
		'date' => $this->input->post('date',TRUE),
	    );

            $this->Directory_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('directory'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Directory_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('directory/update_action'),
		'id' => set_value('id', $row->id),
		'user' => set_value('user', $row->user),
		'user_dir' => set_value('user_dir', $row->user_dir),
		'date' => set_value('date', $row->date),
	    );
            $this->load->view('directory/directory_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('directory'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('id', TRUE));
        } else {
            $data = array(
		'user' => $this->input->post('user',TRUE),
		'user_dir' => $this->input->post('user_dir',TRUE),
		'date' => $this->input->post('date',TRUE),
	    );

            $this->Directory_model->update($this->input->post('id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('directory'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Directory_model->get_by_id($id);

        if ($row) {
            $this->Directory_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('directory'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('directory'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('user', 'user', 'trim|required');
	$this->form_validation->set_rules('user_dir', 'user dir', 'trim|required');
	$this->form_validation->set_rules('date', 'date', 'trim|required');

	$this->form_validation->set_rules('id', 'id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Directory.php */
/* Location: ./application/controllers/Directory.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-15 06:58:39 */
/* http://harviacode.com */