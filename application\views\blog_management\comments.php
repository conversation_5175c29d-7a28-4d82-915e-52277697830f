<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <!-- Stats Cards -->
            <div class="row gutters mb-4">
                <div class="col-md-3">
                    <div class="card l-bg-orange-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large">
                                <i class="icon-clock"></i>
                            </div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0 text-white">
                                    <?php 
                                    $pending = array_filter($comments, function($c) { return $c->status === 'pending'; });
                                    echo count($pending);
                                    ?>
                                </h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0 text-white">Pending</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card l-bg-green-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large">
                                <i class="icon-check"></i>
                            </div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0 text-white">
                                    <?php 
                                    $approved = array_filter($comments, function($c) { return $c->status === 'approved'; });
                                    echo count($approved);
                                    ?>
                                </h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0 text-white">Approved</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card l-bg-cherry">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large">
                                <i class="icon-warning"></i>
                            </div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0 text-white">
                                    <?php 
                                    $spam = array_filter($comments, function($c) { return $c->status === 'spam'; });
                                    echo count($spam);
                                    ?>
                                </h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0 text-white">Spam</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card l-bg-blue-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large">
                                <i class="icon-comments"></i>
                            </div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0 text-white"><?php echo count($comments); ?></h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0 text-white">Total</h2>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Card start -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title d-flex justify-content-between align-items-center">
                        <h4>Comments Management</h4>
                        <a href="<?php echo base_url('Blog_management'); ?>" class="btn btn-outline-secondary">
                            <i class="icon-arrow-left"></i> Back to Posts
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <?php if ($this->session->flashdata('message')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('message'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($comments)): ?>
                        <div class="table-responsive">
                            <table id="copy-print-csv" class="table v-middle">
                                <thead>
                                    <tr>
                                        <th>Author</th>
                                        <th>Comment</th>
                                        <th>Post</th>
                                        <th>Status</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($comments as $comment): ?>
                                        <tr class="<?php echo $comment->status === 'pending' ? 'table-warning' : ''; ?>">
                                            <td>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($comment->author_name); ?></strong>
                                                    <div class="comment-meta">
                                                        <small class="text-muted">
                                                            <i class="icon-email me-1"></i>
                                                            <?php echo htmlspecialchars($comment->author_email); ?>
                                                        </small>
                                                    </div>
                                                    <?php if ($comment->author_website): ?>
                                                        <div class="comment-meta">
                                                            <small class="text-muted">
                                                                <i class="icon-globe me-1"></i>
                                                                <a href="<?php echo htmlspecialchars($comment->author_website); ?>" target="_blank">
                                                                    Website
                                                                </a>
                                                            </small>
                                                        </div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="comment-content" title="<?php echo htmlspecialchars($comment->content); ?>">
                                                    <?php echo htmlspecialchars(word_limiter($comment->content, 15)); ?>
                                                </div>
                                                <?php if ($comment->parent_id): ?>
                                                    <small class="text-muted">
                                                        <i class="icon-reply me-1"></i>Reply to comment
                                                    </small>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <a href="<?php echo base_url('blog/post/' . $comment->post_slug); ?>" target="_blank">
                                                    <?php echo htmlspecialchars(word_limiter($comment->post_title, 5)); ?>
                                                </a>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch($comment->status) {
                                                    case 'approved': $status_class = 'bg-success'; break;
                                                    case 'pending': $status_class = 'bg-warning'; break;
                                                    case 'spam': $status_class = 'bg-danger'; break;
                                                    default: $status_class = 'bg-secondary'; break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($comment->status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('M j, Y', strtotime($comment->created_at)); ?><br>
                                                    <span class="text-muted"><?php echo date('g:i A', strtotime($comment->created_at)); ?></span>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <?php if ($comment->status === 'pending'): ?>
                                                        <button type="button" class="btn btn-outline-success btn-sm" 
                                                                onclick="approveComment(<?php echo $comment->id; ?>)" title="Approve">
                                                            <i class="icon-check"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <?php if ($comment->status !== 'spam'): ?>
                                                        <button type="button" class="btn btn-outline-warning btn-sm" 
                                                                onclick="markSpam(<?php echo $comment->id; ?>)" title="Mark as Spam">
                                                            <i class="icon-warning"></i>
                                                        </button>
                                                    <?php endif; ?>
                                                    
                                                    <button type="button" class="btn btn-outline-primary btn-sm" 
                                                            onclick="viewComment(<?php echo $comment->id; ?>)" title="View Full Comment">
                                                        <i class="icon-eye"></i>
                                                    </button>
                                                    
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="deleteComment(<?php echo $comment->id; ?>)" title="Delete">
                                                        <i class="icon-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="icon-comments display-1 text-muted mb-3"></i>
                            <h5>No comments yet</h5>
                            <p class="text-muted">Comments will appear here when visitors start engaging with your blog posts.</p>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
            <!-- Card end -->

        </div>
    </div>
    <!-- Row end -->

</div>
<!-- Content wrapper end -->

<!-- View Comment Modal -->
<div class="modal fade" id="viewCommentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">View Comment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="commentModalBody">
                <!-- Comment details will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function approveComment(commentId) {
    if (confirm('Approve this comment?')) {
        window.location.href = '<?php echo base_url("Blog_management/approve_comment/"); ?>' + commentId;
    }
}

function markSpam(commentId) {
    if (confirm('Mark this comment as spam?')) {
        window.location.href = '<?php echo base_url("Blog_management/mark_spam/"); ?>' + commentId;
    }
}

function deleteComment(commentId) {
    if (confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
        window.location.href = '<?php echo base_url("Blog_management/delete_comment/"); ?>' + commentId;
    }
}

function viewComment(commentId) {
    // Find the comment data from the table
    const comments = <?php echo json_encode($comments); ?>;
    const comment = comments.find(c => c.id == commentId);
    
    if (comment) {
        const modalBody = document.getElementById('commentModalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Author Information</h6>
                    <p><strong>Name:</strong> ${comment.author_name}</p>
                    <p><strong>Email:</strong> ${comment.author_email}</p>
                    ${comment.author_website ? `<p><strong>Website:</strong> <a href="${comment.author_website}" target="_blank">${comment.author_website}</a></p>` : ''}
                    <p><strong>IP Address:</strong> ${comment.ip_address || 'N/A'}</p>
                </div>
                <div class="col-md-6">
                    <h6>Comment Details</h6>
                    <p><strong>Post:</strong> ${comment.post_title}</p>
                    <p><strong>Status:</strong> <span class="badge bg-${comment.status === 'approved' ? 'success' : comment.status === 'pending' ? 'warning' : 'danger'}">${comment.status.charAt(0).toUpperCase() + comment.status.slice(1)}</span></p>
                    <p><strong>Date:</strong> ${new Date(comment.created_at).toLocaleString()}</p>
                    ${comment.parent_id ? '<p><strong>Type:</strong> Reply</p>' : ''}
                </div>
            </div>
            <hr>
            <h6>Comment Content</h6>
            <div class="bg-light p-3 rounded">
                ${comment.content.replace(/\n/g, '<br>')}
            </div>
        `;
        
        const modal = new bootstrap.Modal(document.getElementById('viewCommentModal'));
        modal.show();
    }
}
</script>

<style>
.actions {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.badge {
    font-size: 0.75rem;
}

.table td {
    vertical-align: middle;
}

.display-1 {
    font-size: 4rem;
}

.comment-content {
    max-width: 300px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.comment-meta {
    font-size: 0.875rem;
    color: #6b7280;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}
</style>
