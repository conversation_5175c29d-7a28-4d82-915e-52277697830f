

<!DOCTYPE html>
<!--[if IE 8]><html class="no-js lt-ie9" lang="en" > <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en" > <!--<![endif]-->
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  
  <title>Change Log &mdash; CodeIgniter 3.1.11 documentation</title>
  

  
  
    <link rel="shortcut icon" href="_static/ci-icon.ico"/>
  

  
  <link href='https://fonts.googleapis.com/css?family=Lato:400,700,400italic,700italic|Roboto+Slab:400,700|Inconsolata:400,700&subset=latin,cyrillic' rel='stylesheet' type='text/css'>

  
  
    

  

  
  
    <link rel="stylesheet" href="_static/css/citheme.css" type="text/css" />
  

  
        <link rel="index" title="Index"
              href="genindex.html"/>
        <link rel="search" title="Search" href="search.html"/>
    <link rel="top" title="CodeIgniter 3.1.11 documentation" href="index.html"/>
        <link rel="next" title="The MIT License (MIT)" href="license.html"/>
        <link rel="prev" title="XML Helper" href="helpers/xml_helper.html"/> 

  
  <script src="https://cdnjs.cloudflare.com/ajax/libs/modernizr/2.6.2/modernizr.min.js"></script>

</head>

<body class="wy-body-for-nav" role="document">

  <div id="nav">
  <div id="nav_inner">
    
    
    
      <div id="pulldown-menu" class="ciNav">
        <ul>
<li class="toctree-l1"><a class="reference internal" href="general/welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="general/index.html">General Topics</a><ul>
<li class="toctree-l2"><a class="reference internal" href="general/urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/styleguide.html">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="libraries/index.html">Libraries</a><ul>
<li class="toctree-l2"><a class="reference internal" href="libraries/benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/ftp.html">FTP Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/image_lib.html">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

      </div>
    
      
  </div>
</div>
<div id="nav2">
  <a href="#" id="openToc">
    <img src="data:image/jpeg;base64,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" title="Toggle Table of Contents" alt="Toggle Table of Contents" />
  </a>
</div>

  <div class="wy-grid-for-nav">

    
    <nav data-toggle="wy-nav-shift" class="wy-nav-side">
      <div class="wy-side-nav-search">
        
          <a href="index.html" class="fa fa-home"> CodeIgniter</a>
        
        
<div role="search">
  <form id="rtd-search-form" class="wy-form" action="search.html" method="get">
    <input type="text" name="q" placeholder="Search docs" />
    <input type="hidden" name="check_keywords" value="yes" />
    <input type="hidden" name="area" value="default" />
  </form>
</div>
      </div>

      <div class="wy-menu wy-menu-vertical" data-spy="affix" role="navigation" aria-label="main navigation">
        
          
          
              <ul>
<li class="toctree-l1"><a class="reference internal" href="general/welcome.html">Welcome to CodeIgniter</a></li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="installation/index.html">Installation Instructions</a><ul>
<li class="toctree-l2"><a class="reference internal" href="installation/downloads.html">Downloading CodeIgniter</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/index.html">Installation Instructions</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/upgrading.html">Upgrading From a Previous Version</a></li>
<li class="toctree-l2"><a class="reference internal" href="installation/troubleshooting.html">Troubleshooting</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="overview/index.html">CodeIgniter Overview</a><ul>
<li class="toctree-l2"><a class="reference internal" href="overview/getting_started.html">Getting Started</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/at_a_glance.html">CodeIgniter at a Glance</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/features.html">Supported Features</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/appflow.html">Application Flow Chart</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/mvc.html">Model-View-Controller</a></li>
<li class="toctree-l2"><a class="reference internal" href="overview/goals.html">Architectural Goals</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="tutorial/index.html">Tutorial</a><ul>
<li class="toctree-l2"><a class="reference internal" href="tutorial/static_pages.html">Static pages</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/news_section.html">News section</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/create_news_items.html">Create news items</a></li>
<li class="toctree-l2"><a class="reference internal" href="tutorial/conclusion.html">Conclusion</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="contributing/index.html">Contributing to CodeIgniter</a><ul>
<li class="toctree-l2"><a class="reference internal" href="documentation/index.html">Writing CodeIgniter Documentation</a></li>
<li class="toctree-l2"><a class="reference internal" href="DCO.html">Developer’s Certificate of Origin 1.1</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="general/index.html">General Topics</a><ul>
<li class="toctree-l2"><a class="reference internal" href="general/urls.html">CodeIgniter URLs</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/controllers.html">Controllers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/reserved_names.html">Reserved Names</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/views.html">Views</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/models.html">Models</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/helpers.html">Helpers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/libraries.html">Using CodeIgniter Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/creating_libraries.html">Creating Libraries</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/drivers.html">Using CodeIgniter Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/creating_drivers.html">Creating Drivers</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/core_classes.html">Creating Core System Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/ancillary_classes.html">Creating Ancillary Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/hooks.html">Hooks - Extending the Framework Core</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/autoloader.html">Auto-loading Resources</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/common_functions.html">Common Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/compatibility_functions.html">Compatibility Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/routing.html">URI Routing</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/errors.html">Error Handling</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/caching.html">Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/profiling.html">Profiling Your Application</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/cli.html">Running via the CLI</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/managing_apps.html">Managing your Applications</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/environments.html">Handling Multiple Environments</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/alternative_php.html">Alternate PHP Syntax for View Files</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/security.html">Security</a></li>
<li class="toctree-l2"><a class="reference internal" href="general/styleguide.html">PHP Style Guide</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="libraries/index.html">Libraries</a><ul>
<li class="toctree-l2"><a class="reference internal" href="libraries/benchmark.html">Benchmarking Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/caching.html">Caching Driver</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/calendar.html">Calendaring Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/cart.html">Shopping Cart Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/config.html">Config Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/email.html">Email Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/encrypt.html">Encrypt Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/encryption.html">Encryption Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/file_uploading.html">File Uploading Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/form_validation.html">Form Validation</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/ftp.html">FTP Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/image_lib.html">Image Manipulation Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/input.html">Input Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/javascript.html">Javascript Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/language.html">Language Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/loader.html">Loader Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/migration.html">Migrations Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/output.html">Output Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/pagination.html">Pagination Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/parser.html">Template Parser Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/security.html">Security Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/sessions.html">Session Library</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/table.html">HTML Table Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/trackback.html">Trackback Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/typography.html">Typography Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/unit_testing.html">Unit Testing Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/uri.html">URI Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/user_agent.html">User Agent Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/xmlrpc.html">XML-RPC and XML-RPC Server Classes</a></li>
<li class="toctree-l2"><a class="reference internal" href="libraries/zip.html">Zip Encoding Class</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="database/index.html">Database Reference</a><ul>
<li class="toctree-l2"><a class="reference internal" href="database/examples.html">Quick Start: Usage Examples</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/configuration.html">Database Configuration</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/connecting.html">Connecting to a Database</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/queries.html">Running Queries</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/results.html">Generating Query Results</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/helpers.html">Query Helper Functions</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/query_builder.html">Query Builder Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/transactions.html">Transactions</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/metadata.html">Getting MetaData</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/call_function.html">Custom Function Calls</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/caching.html">Query Caching</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/forge.html">Database Manipulation with Database Forge</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/utilities.html">Database Utilities Class</a></li>
<li class="toctree-l2"><a class="reference internal" href="database/db_driver_reference.html">Database Driver Reference</a></li>
</ul>
</li>
</ul>
<ul>
<li class="toctree-l1"><a class="reference internal" href="helpers/index.html">Helpers</a><ul>
<li class="toctree-l2"><a class="reference internal" href="helpers/array_helper.html">Array Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/captcha_helper.html">CAPTCHA Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/cookie_helper.html">Cookie Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/date_helper.html">Date Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/directory_helper.html">Directory Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/download_helper.html">Download Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/email_helper.html">Email Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/file_helper.html">File Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/form_helper.html">Form Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/html_helper.html">HTML Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/inflector_helper.html">Inflector Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/language_helper.html">Language Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/number_helper.html">Number Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/path_helper.html">Path Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/security_helper.html">Security Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/smiley_helper.html">Smiley Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/string_helper.html">String Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/text_helper.html">Text Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/typography_helper.html">Typography Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/url_helper.html">URL Helper</a></li>
<li class="toctree-l2"><a class="reference internal" href="helpers/xml_helper.html">XML Helper</a></li>
</ul>
</li>
</ul>

          
        
      </div>
      &nbsp;
    </nav>

    <section data-toggle="wy-nav-shift" class="wy-nav-content-wrap">

      
      <nav class="wy-nav-top" role="navigation" aria-label="top navigation">
        <i data-toggle="wy-nav-top" class="fa fa-bars"></i>
        <a href="index.html">CodeIgniter</a>
      </nav>


      
      <div class="wy-nav-content">
        <div class="rst-content">
          <div role="navigation" aria-label="breadcrumbs navigation">
  <ul class="wy-breadcrumbs">
    <li><a href="index.html">Docs</a> &raquo;</li>
      
    <li>Change Log</li>
    <li class="wy-breadcrumbs-aside">
      
    </li>
    <div style="float:right;margin-left:5px;" id="closeMe">
      <img title="Classic Layout" alt="classic layout" src="data:image/gif;base64,R0lGODlhFAAUAJEAAAAAADMzM////wAAACH5BAUUAAIALAAAAAAUABQAAAImlI+py+0PU5gRBRDM3DxbWoXis42X13USOLauUIqnlsaH/eY6UwAAOw==" />
    </div>
  </ul>
  <hr/>
</div>
          <div role="main" class="document">
            
  <div class="section" id="change-log">
<h1>Change Log<a class="headerlink" href="#change-log" title="Permalink to this headline">¶</a></h1>
<div class="section" id="version-3-1-11">
<h2>Version 3.1.11<a class="headerlink" href="#version-3-1-11" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Sep 19, 2019</p>
<ul class="simple">
<li>General Changes<ul>
<li>Changed <code class="docutils literal"><span class="pre">CI_Log</span></code> to append <code class="docutils literal"><span class="pre">PHP_EOL</span></code> instead of <code class="docutils literal"><span class="pre">\n</span></code> at the end of log messages.</li>
<li>Improved performance in <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘redis’ driver with non-scalar variables.</li>
<li>Altered the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘files’ driver to log error and trigger a session start failure instead of throwing an <code class="docutils literal"><span class="pre">Exception</span></code> in case of unusable <code class="docutils literal"><span class="pre">$config['sess_save_path']</span></code>.</li>
<li>Updated the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session</span></a> and <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache</span></a> libraries’ ‘redis’ driver to work with phpRedis 5.</li>
</ul>
</li>
</ul>
</div>
<div class="section" id="bug-fixes-for-3-1-11">
<h2>Bug fixes for 3.1.11<a class="headerlink" href="#bug-fixes-for-3-1-11" title="Permalink to this headline">¶</a></h2>
<ul class="simple">
<li>Fixed a bug (#5681) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">modify_column()</span></code> produced erroneous SQL for <code class="docutils literal"><span class="pre">DEFAULT</span></code> attribute changes under PostgreSQL, Firebird.</li>
<li>Fixed a bug (#5692) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> didn’t handle column nullability with the ‘oci8’, ‘pdo/oci’ drivers.</li>
<li>Fixed a bug (#5701) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘pdo/pgsql’ produced incorrect DSNs when constructing from a configuration array.</li>
<li>Fixed a bug (#5708) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘redis’ driver too often failed with locking-related errors that could’ve been avoided.</li>
<li>Fixed a bug (#5703) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> triggered an <code class="docutils literal"><span class="pre">E_WARNING</span></code> message about changing <code class="docutils literal"><span class="pre">session.save_path</span></code> during an active session when it fails to obtain a lock.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘database’ driver didn’t trigger a failure if it can’t obtain a lock.</li>
<li>Fixed a bug (#5755) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>valid_url</strong> accepted digit-only domains due to a PHP bug.</li>
<li>Fixed a bug (#5753) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘redis’ driver methods <code class="docutils literal"><span class="pre">increment()</span></code>, <code class="docutils literal"><span class="pre">decrement()</span></code> ignored their <code class="docutils literal"><span class="pre">$offset</span></code> parameter.</li>
<li>Fixed a bug (#5779) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘redis’ only attempted to validate session IDs in case the connection to Redis failed.</li>
<li>Fixed a bug (#5774) - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">custom_result_object()</span></code> didn’t properly handle empty result sets, triggering <code class="docutils literal"><span class="pre">E_WARNING</span></code> messages on PHP 7.2+.</li>
<li>Fixed a bug (#5788) - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">field_data()</span></code> triggered an <code class="docutils literal"><span class="pre">E_NOTICE</span></code> error with PDO when a field type is not recognized by PHP.</li>
<li>Fixed a bug (#5796) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">list_tables()</span></code> triggered an SQL syntax error under MySQL when the database schema is a numeric string.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/security.html"><span class="doc">Security Class</span></a> would trigger an <code class="docutils literal"><span class="pre">E_WARNING</span></code> if CSRF inputs are arrays instead of strings.</li>
</ul>
</div>
<div class="section" id="version-3-1-10">
<h2>Version 3.1.10<a class="headerlink" href="#version-3-1-10" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Jan 16, 2019</p>
<ul class="simple">
<li>General Changes<ul>
<li>Added ‘ssl_verify’ support to the ‘pdo/mysql’ <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver.</li>
<li>Renamed <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector Helper</span></a> function <code class="docutils literal"><span class="pre">is_countable()</span></code> to <a class="reference internal" href="helpers/inflector_helper.html#word_is_countable" title="word_is_countable"><code class="xref php php-func docutils literal"><span class="pre">word_is_countable()</span></code></a> due to the former colliding with one introduced in PHP 7.3.0.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-10">
<h3>Bug fixes for 3.1.10<a class="headerlink" href="#bug-fixes-for-3-1-10" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#5526) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> had a syntax error in its ‘memcached’ driver.</li>
<li>Fixed a bug (#5542) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">modify_column()</span></code> always made fields <code class="docutils literal"><span class="pre">NOT</span> <span class="pre">NULL</span></code> when attempting to modify their nullable property under PostgreSQL.</li>
<li>Fixed a bug (#5561) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> didn’t allow SSL connection configuration with only the ‘ssl_verify’ option when using the ‘mysqli’ driver.</li>
<li>Fixed a bug (#5545) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> crashed due to a caching-related error with the ‘files’ driver.</li>
<li>Fixed a bug (#5571) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> had a typo that triggered an <code class="docutils literal"><span class="pre">E_WARNING</span></code> message on PHP 7.2.</li>
<li>Fixed a bug (#5587) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> generated an <code class="docutils literal"><span class="pre">E_WARNING</span></code> message.</li>
<li>Fixed a bug (#5590) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>valid_base64</strong> didn’t have a default error message.</li>
<li>Fixed a bug (#5624) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> methods <code class="docutils literal"><span class="pre">list_fields()</span></code>, <code class="docutils literal"><span class="pre">field_exists()</span></code> returned incorrect results after tables are modified.</li>
<li>Fixed a bug (#5627) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘mysqli’ triggered an <code class="docutils literal"><span class="pre">E_WARNING</span></code> message if there’s no <code class="docutils literal"><span class="pre">'port'</span></code> specified in the database configuration.</li>
<li>Fixed a bug (#5651) - <a class="reference internal" href="database/caching.html"><span class="doc">Database Caching</span></a> could try to delete non-existent cache files due to a race condition.</li>
<li>Fixed a bug (#5652) - <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> function <a class="reference internal" href="helpers/captcha_helper.html#create_captcha" title="create_captcha"><code class="xref php php-func docutils literal"><span class="pre">create_captcha()</span></code></a> didn’t comply with CSS standards.</li>
<li>Fixed a bug (#5605) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t nullify array inputs that are expected to be strings.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-9">
<h2>Version 3.1.9<a class="headerlink" href="#version-3-1-9" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Jun 12, 2018</p>
<ul>
<li><p class="first"><strong>Security</strong></p>
<blockquote>
<div><ul class="simple">
<li>Updated <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#auto_link" title="auto_link"><code class="xref php php-func docutils literal"><span class="pre">auto_link()</span></code></a> to add <code class="docutils literal"><span class="pre">rel=&quot;noopener&quot;</span></code> to generated links in order to prevent tab hijacking.</li>
<li>Fixed a possible session fixation vulnerability where the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> enabled <code class="docutils literal"><span class="pre">session.use_strict_mode</span></code> but it didn’t actually do anything (thanks to Aamer Shah, Prasanna Kumar).</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">General Changes</p>
<ul class="simple">
<li>Updated <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">limit()</span></code> to allow <code class="docutils literal"><span class="pre">0</span></code> values.</li>
<li>Updated <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> and <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> to discard the results of failed <code class="docutils literal"><span class="pre">idn_to_ascii()</span></code> calls while validating e-mail addresses.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-9">
<h3>Bug fixes for 3.1.9<a class="headerlink" href="#bug-fixes-for-3-1-9" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a regression (#5448) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">like()</span></code>, <code class="docutils literal"><span class="pre">or_like()</span></code> (and siblings) didn’t apply <em>dbprefix</em> or identifier escaping.</li>
<li>Fixed a regression (#5462) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">like()</span></code>, <code class="docutils literal"><span class="pre">or_like()</span></code> (and siblings) produced incorrect SQL syntax when used with <code class="docutils literal"><span class="pre">'before'</span></code> as the third parameter.</li>
<li>Fixed a bug (#5516) - <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML Helper</span></a> functions <a class="reference internal" href="helpers/html_helper.html#img" title="img"><code class="xref php php-func docutils literal"><span class="pre">img()</span></code></a>, <a class="reference internal" href="helpers/html_helper.html#link_tag" title="link_tag"><code class="xref php php-func docutils literal"><span class="pre">link_tag()</span></code></a> would output results with double slashes if a prefix slash was included in their path inputs.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-8">
<h2>Version 3.1.8<a class="headerlink" href="#version-3-1-8" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Mar 22, 2018</p>
<ul>
<li><p class="first"><strong>Security</strong></p>
<blockquote>
<div><ul class="simple">
<li>Updated <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> to also filter JavaScript tag functions.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> didn’t check for parentheses around JavaScript’s <code class="docutils literal"><span class="pre">document</span></code>.</li>
</ul>
</div></blockquote>
</li>
<li><p class="first">General Changes</p>
<ul class="simple">
<li>Updated <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> to always negotiate between TLS 1.0, 1.1, 1.2 when possible (PHP 5.6+) for SMTP connections.</li>
<li>Updated <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> method <code class="docutils literal"><span class="pre">version()</span></code> to exclude suffixes to the main version numbers with the ‘postgre’ driver.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-8">
<h3>Bug fixes for 3.1.8<a class="headerlink" href="#bug-fixes-for-3-1-8" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug where <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a>, <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> tried to use <code class="docutils literal"><span class="pre">INTL_IDNA_VARIANT_UTS46</span></code> when it was undeclared.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">where()</span></code>, <code class="docutils literal"><span class="pre">having()</span></code> treated values passed to them as arbitrary SQL.</li>
<li>Fixed a bug (#5423) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> method <code class="docutils literal"><span class="pre">insert_id()</span></code> failed due to incorrect server version parsing with the ‘postgre’ driver.</li>
<li>Fixed a bug (#5425) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> produced an error message related to <code class="docutils literal"><span class="pre">count()</span></code> on PHP 7.2.</li>
<li>Fixed a bug (#5434) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> attempted to <code class="docutils literal"><span class="pre">chmod()</span></code> while rendering images with the <code class="docutils literal"><span class="pre">dynamic_output</span></code> option.</li>
<li>Fixed a bug (#5435) - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">field_data()</span></code> hid info about one field if <code class="docutils literal"><span class="pre">limit()</span></code> was previously used with the ‘oci8’ driver.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-7">
<h2>Version 3.1.7<a class="headerlink" href="#version-3-1-7" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Jan 13, 2018</p>
<ul class="simple">
<li>General Changes<ul>
<li>Updated <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <code class="docutils literal"><span class="pre">valid_email</span></code> to use <code class="docutils literal"><span class="pre">INTL_IDNA_VARIANT_UTS46</span></code> for non-ASCII domain names.</li>
<li>Updated <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> to use <code class="docutils literal"><span class="pre">INTL_IDNA_VARIANT_UTS46</span></code> for non-ASCII domain names.</li>
<li>Updated <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">model()</span></code> to log both <code class="docutils literal"><span class="pre">CI_Model</span></code> class loading and individual models’ initialization.</li>
<li>Updated <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> to preserve previously set attributes while calling <code class="docutils literal"><span class="pre">initialize()</span></code>.</li>
<li>Updated <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> to automatically add items to cache on <code class="docutils literal"><span class="pre">increment()</span></code>, <code class="docutils literal"><span class="pre">decrement()</span></code> calls for missing keys.</li>
<li>Deprecated usage of <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> function <a class="reference internal" href="helpers/captcha_helper.html#create_captcha" title="create_captcha"><code class="xref php php-func docutils literal"><span class="pre">create_captcha()</span></code></a> with parameters other than <code class="docutils literal"><span class="pre">$data</span></code>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-7">
<h3>Bug fixes for 3.1.7<a class="headerlink" href="#bug-fixes-for-3-1-7" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a regression (#5276) - <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a> method <code class="docutils literal"><span class="pre">backup()</span></code> generated incorrect <code class="docutils literal"><span class="pre">INSERT</span></code> statements with the ‘mysqli’ driver.</li>
<li>Fixed a regression where <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">field_data()</span></code> returned incorrect type names.</li>
<li>Fixed a bug (#5278) - <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#auto_link" title="auto_link"><code class="xref php php-func docutils literal"><span class="pre">auto_link()</span></code></a> didn’t detect trailing slashes in URLs.</li>
<li>Fixed a regression (#5282) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> breaks <code class="docutils literal"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clauses for subsequent queries.</li>
<li>Fixed a bug (#5279) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t account for already escaped identifiers while applying database name prefixes.</li>
<li>Fixed a bug (#5331) - <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#auto_link" title="auto_link"><code class="xref php php-func docutils literal"><span class="pre">auto_link()</span></code></a> converted e-mail addresses starting with ‘www.’ to both “url” and “email” links.</li>
<li>Fixed a bug where <code class="docutils literal"><span class="pre">$config['allow_get_array']</span></code> defaulted to <code class="docutils literal"><span class="pre">FALSE</span></code> if it didn’t exist in the config file.</li>
<li>Fixed a bug (#5379) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> would incorrectly fail to obtain a lock that it already has on PHP 7 with the ‘memcached’ driver.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-6">
<h2>Version 3.1.6<a class="headerlink" href="#version-3-1-6" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Sep 25, 2017</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed a potential object injection in <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘apc’ driver when <code class="docutils literal"><span class="pre">save()</span></code> is used with <code class="docutils literal"><span class="pre">$raw</span> <span class="pre">=</span> <span class="pre">TRUE</span></code> (thanks to Tomas Bortoli).</li>
</ul>
</li>
<li>General Changes<ul>
<li>Deprecated <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library Library</span></a> driver ‘apc’.</li>
<li>Updated the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘redis’, ‘memcached’ drivers to reduce the potential of a locking race conditions.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-6">
<h3>Bug fixes for 3.1.6<a class="headerlink" href="#bug-fixes-for-3-1-6" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#5164) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">library()</span></code> ignored requests to load libraries previously assigned to super-object properties named differently than the library name.</li>
<li>Fixed a bug (#5168) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> produced erroneous queries on Microsoft SQL Server when <code class="docutils literal"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clauses are cached.</li>
<li>Fixed a bug (#5128) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a> didn’t wrap <code class="docutils literal"><span class="pre">$_SESSION</span></code> and configuration arrays in <code class="docutils literal"><span class="pre">&lt;pre&gt;</span></code> tags.</li>
<li>Fixed a bug (#5183) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> method <code class="docutils literal"><span class="pre">is_write_type()</span></code> didn’t return TRUE for <code class="docutils literal"><span class="pre">MERGE</span></code> statements.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> didn’t escape image source paths passed to NetPBM as shell arguments.</li>
<li>Fixed a bug (#5236) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">limit()</span></code>, <code class="docutils literal"><span class="pre">offset()</span></code> break SQL Server 2005, 2008 queries with <code class="docutils literal"><span class="pre">&quot;&lt;tablename&gt;&quot;.*</span></code> in the <code class="docutils literal"><span class="pre">SELECT</span></code> clause.</li>
<li>Fixed a bug (#5243) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> method <code class="docutils literal"><span class="pre">version()</span></code> didn’t work with the ‘pdo/dblib’ driver.</li>
<li>Fixed a bug (#5246) - <a class="reference internal" href="database/transactions.html"><span class="doc">Database transactions</span></a> status wasn’t reset unless <code class="docutils literal"><span class="pre">trans_complete()</span></code> was called.</li>
<li>Fixed a bug (#5260) - <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a> method <code class="docutils literal"><span class="pre">backup()</span></code> generated incorrect <code class="docutils literal"><span class="pre">INSERT</span></code> statements with the ‘mysqli’ driver.</li>
<li>Fixed a bug where <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">field_data()</span></code> didn’t parse field types with the ‘mysqli’ driver.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-5">
<h2>Version 3.1.5<a class="headerlink" href="#version-3-1-5" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Jun 19, 2017</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li><a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <code class="docutils literal"><span class="pre">valid_email</span></code> could be bypassed if <code class="docutils literal"><span class="pre">idn_to_ascii()</span></code> is available.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Updated <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> function <a class="reference internal" href="helpers/form_helper.html#form_label" title="form_label"><code class="xref php php-func docutils literal"><span class="pre">form_label()</span></code></a> to accept HTML attributes as a string.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-5">
<h3>Bug fixes for 3.1.5<a class="headerlink" href="#bug-fixes-for-3-1-5" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#5070) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t properly detect 7-bit encoding.</li>
<li>Fixed a bug (#5084) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> errored because of a variable name typo.</li>
<li>Fixed a bug (#5108) - <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector Helper</span></a> function <a class="reference internal" href="helpers/inflector_helper.html#singular" title="singular"><code class="xref php php-func docutils literal"><span class="pre">singular()</span></code></a> didn’t properly handle ‘quizzes’.</li>
<li>Fixed a regression (#5131) - private controller methods triggered PHP errors instead of a 404 response.</li>
<li>Fixed a bug (#5150) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">modify_column()</span></code> triggered an error while renaming columns with the ‘oci8’, ‘pdo/oci’ drivers.</li>
<li>Fixed a bug (#5155) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> returned incorrect result for queries using <code class="docutils literal"><span class="pre">LIMIT</span></code>, <code class="docutils literal"><span class="pre">OFFSET</span></code>.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-4">
<h2>Version 3.1.4<a class="headerlink" href="#version-3-1-4" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Mar 20, 2017</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed a header injection vulnerability in <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#set_status_header" title="set_status_header"><code class="xref php php-func docutils literal"><span class="pre">set_status_header()</span></code></a> under Apache (thanks to Guillermo Caminer from <a class="reference external" href="https://flowgate.net/">Flowgate</a>).</li>
<li>Fixed byte-safety issues in <a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a> (DEPRECATED) when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed byte-safety issues in <a class="reference internal" href="libraries/encryption.html"><span class="doc">Encryption Library</span></a> when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed byte-safety issues in <a class="reference internal" href="general/compatibility_functions.html"><span class="doc">compatibility functions</span></a> <code class="docutils literal"><span class="pre">password_hash()</span></code>, <code class="docutils literal"><span class="pre">hash_pbkdf2()</span></code> when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Updated <a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a> (DEPRECATED) to call <code class="docutils literal"><span class="pre">mcrypt_create_iv()</span></code> with <code class="docutils literal"><span class="pre">MCRYPT_DEV_URANDOM</span></code>.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Updated the <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> to work-around an issue with some JPEGs when using GD.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-4">
<h3>Bug fixes for 3.1.4<a class="headerlink" href="#bug-fixes-for-3-1-4" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a regression (#4975) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> couldn’t handle objects passed as view variables.</li>
<li>Fixed a bug (#4977) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">helper()</span></code> could accept any character as a filename extension separator.</li>
<li>Fixed a regression where the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> would fail on a <code class="docutils literal"><span class="pre">session_regenerate_id(TRUE)</span></code> call with the ‘database’ driver.</li>
<li>Fixed a bug (#4987) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> caching didn’t keep track of table aliases.</li>
<li>Fixed a bug where <a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text Helper</span></a> function <code class="docutils literal"><span class="pre">ascii_to_entities()</span></code> wasn’t byte-safe when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed a bug where <code class="docutils literal"><span class="pre">CI_Log</span></code>, <code class="docutils literal"><span class="pre">CI_Output</span></code>, <code class="docutils literal"><span class="pre">CI_Email</span></code> and <code class="docutils literal"><span class="pre">CI_Zip</span></code> didn’t handle strings in a byte-safe manner when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t read session data in a byte-safe manner when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed a bug (#4990) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a> didn’t close <code class="docutils literal"><span class="pre">&lt;pre&gt;</span></code> tags it generated.</li>
<li>Fixed a bug (#4990) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a> didn’t HTML-escape quotes for <code class="docutils literal"><span class="pre">$_SESSION</span></code> variables.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">set_cookie()</span></code> didn’t allow its <em>httponly</em> and <em>secure</em> parameters to be overriden to <code class="docutils literal"><span class="pre">FALSE</span></code>.</li>
<li>Fixed a bug (#5006) - <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#get_mimes" title="get_mimes"><code class="xref php php-func docutils literal"><span class="pre">get_mimes()</span></code></a> didn’t load <em>application/config/mimes.php</em> if an environment specific config exists.</li>
<li>Fixed a bug (#5006) - <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#remove_invisible_characters" title="remove_invisible_characters"><code class="xref php php-func docutils literal"><span class="pre">remove_invisible_characters()</span></code></a> didn’t remove URL-encoded <code class="docutils literal"><span class="pre">0x7F</span></code>.</li>
<li>Fixed a bug (#4815) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> stripped URL-encoded sequences while escaping strings with the ‘mssql’ driver.</li>
<li>Fixed a bug (#5044) - <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML Helper</span></a> function <a class="reference internal" href="helpers/html_helper.html#img" title="img"><code class="xref php php-func docutils literal"><span class="pre">img()</span></code></a> didn’t accept <code class="docutils literal"><span class="pre">data:</span></code> URI schemes for the image source.</li>
<li>Fixed a bug (#5050) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> tried to access an undefined property in a number of error handling cases.</li>
<li>Fixed a bug (#5057) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘postgre’ didn’t actually apply extra options (such as ‘connect_timeout’) to its DSN.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-3">
<h2>Version 3.1.3<a class="headerlink" href="#version-3-1-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Jan 09, 2017</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed an XSS vulnerability in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
<li>Fixed a possible file inclusion vulnerability in <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">vars()</span></code>.</li>
<li>Fixed a possible remote code execution vulnerability in the <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> when ‘mail’ or ‘sendmail’ are used (thanks to Paul Buonopane from <a class="reference external" href="https://www.namepros.com/">NamePros</a>).</li>
<li>Added protection against timing side-channel attacks in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">csrf_verify()</span></code>.</li>
<li>Added protection against BREACH attacks targeting the CSRF token field generated by <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> function <a class="reference internal" href="helpers/form_helper.html#form_open" title="form_open"><code class="xref php php-func docutils literal"><span class="pre">form_open()</span></code></a>.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Deprecated <code class="docutils literal"><span class="pre">$config['allow_get_array']</span></code>.</li>
<li>Deprecated <code class="docutils literal"><span class="pre">$config['standardize_newlines']</span></code>.</li>
<li>Deprecated <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> function <a class="reference internal" href="helpers/date_helper.html#nice_date" title="nice_date"><code class="xref php php-func docutils literal"><span class="pre">nice_date()</span></code></a>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-3">
<h3>Bug fixes for 3.1.3<a class="headerlink" href="#bug-fixes-for-3-1-3" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4886) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> didn’t differentiate bind markers inside double-quoted strings in queries.</li>
<li>Fixed a bug (#4890) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> didn’t work on PHP 7.</li>
<li>Fixed a regression (#4887) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> triggered fatal errors due to numerous PHP distribution channels (XAMPP and cPanel confirmed) explicitly disabling ext/fileinfo by default.</li>
<li>Fixed a bug (#4679) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">ip_address()</span></code> didn’t properly resolve <code class="docutils literal"><span class="pre">$config['proxy_ips']</span></code> IPv6 addresses.</li>
<li>Fixed a bug (#4902) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> processing via ImageMagick didn’t work.</li>
<li>Fixed a bug (#4905) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> didn’t take into account possible user-provided directory paths when loading helpers.</li>
<li>Fixed a bug (#4916) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> with <code class="docutils literal"><span class="pre">sess_match_ip</span></code> enabled was unusable for IPv6 clients when using the ‘database’ driver on MySQL 5.7.5+.</li>
<li>Fixed a bug (#4917) - <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> function <a class="reference internal" href="helpers/date_helper.html#nice_date" title="nice_date"><code class="xref php php-func docutils literal"><span class="pre">nice_date()</span></code></a> didn’t handle YYYYMMDD inputs properly.</li>
<li>Fixed a bug (#4923) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> could execute an erroneous SQL query with the ‘database’ driver, if the lock attempt times out.</li>
<li>Fixed a bug (#4927) - <a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a> method <code class="docutils literal"><span class="pre">get_header()</span></code> returned the first matching header, regardless of whether it would be replaced by a second <code class="docutils literal"><span class="pre">set_header()</span></code> call.</li>
<li>Fixed a bug (#4844) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t apply <code class="docutils literal"><span class="pre">escapeshellarg()</span></code> to the while passing the Sendmail <code class="docutils literal"><span class="pre">-f</span></code> parameter through <code class="docutils literal"><span class="pre">popen()</span></code>.</li>
<li>Fixed a bug (#4928) - the bootstrap file didn’t check if <em>config/constants.php</em> exists before trying to load it.</li>
<li>Fixed a bug (#4937) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <code class="docutils literal"><span class="pre">initialize()</span></code> didn’t translate <em>new_image</em> inputs to absolute paths.</li>
<li>Fixed a bug (#4941) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">order_by()</span></code> didn’t work with ‘RANDOM’ under the ‘pdo/sqlite’ driver.</li>
<li>Fixed a regression (#4892) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">update_batch()</span></code> didn’t properly handle identifier escaping.</li>
<li>Fixed a bug (#4953) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> didn’t update an internal tables list cache if it exists but is empty.</li>
<li>Fixed a bug (#4958) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> didn’t take into account cached <code class="docutils literal"><span class="pre">ORDER</span> <span class="pre">BY</span></code> clauses.</li>
<li>Fixed a bug (#4804) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">insert_batch()</span></code> could fail if the input array pointer was modified.</li>
<li>Fixed a bug (#4962) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Force</span></a> method <code class="docutils literal"><span class="pre">alter_table()</span></code> would fail with the ‘oci8’ driver.</li>
<li>Fixed a bug (#4457) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <code class="docutils literal"><span class="pre">get_image_properties()</span></code> didn’t detect invalid images.</li>
<li>Fixed a bug (#4765) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t send the <code class="docutils literal"><span class="pre">User-Agent</span></code> header without a prior call to <code class="docutils literal"><span class="pre">clear()</span></code>.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-2">
<h2>Version 3.1.2<a class="headerlink" href="#version-3-1-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Oct 28, 2016</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed a number of new vulnerabilities in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Allowed PHP 4-style constructors (<code class="docutils literal"><span class="pre">Matching_name::Matching_name()</span></code> methods) to be used as routes, if there’s a <code class="docutils literal"><span class="pre">__construct()</span></code> to override them.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-2">
<h3>Bug fixes for 3.1.2<a class="headerlink" href="#bug-fixes-for-3-1-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a regression (#4874) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t take into account <code class="docutils literal"><span class="pre">session.hash_bits_per_character</span></code> when validating session IDs.</li>
<li>Fixed a bug (#4871) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">update_batch()</span></code> didn’t properly handle identifier escaping.</li>
<li>Fixed a bug (#4884) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t properly parse field names ending in ‘is’ when used inside WHERE and HAVING statements.</li>
<li>Fixed a bug where <code class="docutils literal"><span class="pre">CI_Log</span></code>, <code class="docutils literal"><span class="pre">CI_Output</span></code>, <code class="docutils literal"><span class="pre">CI_Email</span></code> and <code class="docutils literal"><span class="pre">CI_Zip</span></code> didn’t handle strings in a byte-safe manner when <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-1">
<h2>Version 3.1.1<a class="headerlink" href="#version-3-1-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: Oct 22, 2016</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed a flaw in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">entity_decode()</span></code> (used by <code class="docutils literal"><span class="pre">xss_clean()</span></code>) that affects HTML 5 entities when using PHP 5.3.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Added <code class="docutils literal"><span class="pre">E_PARSE</span></code> to the list of error levels detected by the shutdown handler.</li>
<li>Updated <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector Helper</span></a> <code class="docutils literal"><span class="pre">is_countable()</span></code> with more words.</li>
<li>Updated <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#set_status_header" title="set_status_header"><code class="xref php php-func docutils literal"><span class="pre">set_status_header()</span></code></a> with new status codes from IETF RFCs
<a class="reference external" href="https://tools.ietf.org/html/rfc2817">2817</a> (426)
and <a class="reference external" href="https://tools.ietf.org/html/rfc6585">6585</a> (428, 429, 431, 511).</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-1">
<h3>Bug fixes for 3.1.1<a class="headerlink" href="#bug-fixes-for-3-1-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4732) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> triggered errors while writing data for a newly-created sessions with the ‘memcached’ driver.</li>
<li>Fixed a regression (#4736) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> processing via ImageMagick didn’t work.</li>
<li>Fixed a bug (#4737) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t add an <code class="docutils literal"><span class="pre">OFFSET</span></code> when <code class="docutils literal"><span class="pre">LIMIT</span></code> is zero or unused.</li>
<li>Fixed a regression (#4739) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> doesn’t properly separate attachment bodies from headers.</li>
<li>Fixed a bug (#4754) - <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a> method <code class="docutils literal"><span class="pre">result()</span></code> didn’t translate <code class="docutils literal"><span class="pre">res_datatype</span></code>.</li>
<li>Fixed a bug (#4759) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation</span></a>, <a class="reference internal" href="libraries/trackback.html"><span class="doc">Trackback</span></a> and <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC</span></a> libraries treated URI schemes in a case-sensitive manner.</li>
<li>Fixed a bug (#4762) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘file’ driver method <code class="docutils literal"><span class="pre">get_metadata()</span></code> checked TTL time against <code class="docutils literal"><span class="pre">mtime</span></code> instead of the cache item’s creation time.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> generated error messages on PHP 7.1.</li>
<li>Fixed a bug (#4780) - <a class="reference internal" href="general/compatibility_functions.html"><span class="doc">compatibility function</span></a> <code class="docutils literal"><span class="pre">hex2bin()</span></code> didn’t reject inputs of type “resource”.</li>
<li>Fixed a bug (#4787) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">valid_email()</span></code> triggered <code class="docutils literal"><span class="pre">E_WARNING</span></code> when input emails have empty domain names.</li>
<li>Fixed a bug (#4805) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘mysqli’ didn’t use the <code class="docutils literal"><span class="pre">MYSQLI_CLIENT_SSL_DONT_VERIFY_SERVER_CERT</span></code> flag properly.</li>
<li>Fixed a bug (#4808) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">is_write_type()</span></code> only looked at the first line of a queries using <code class="docutils literal"><span class="pre">RETURNING</span></code> with the ‘postgre’, ‘pdo/pgsql’, ‘odbc’ and ‘pdo/odbc’ drivers.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">insert_batch()</span></code> tried to execute an unsupported SQL query with the ‘ibase’ and ‘pdo/firebird’ drivers.</li>
<li>Fixed a bug (#4809) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘pdo/mysql’ didn’t turn off <code class="docutils literal"><span class="pre">AUTOCOMMIT</span></code> when starting a transaction.</li>
<li>Fixed a bug (#4822) - <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> didn’t clear expired PNG images.</li>
<li>Fixed a bug (#4823) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘files’ driver could enter an infinite loop if <code class="docutils literal"><span class="pre">mbstring.func_overload</span></code> is enabled.</li>
<li>Fixed a bug (#4851) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> didn’t quote schema names passed to its <code class="docutils literal"><span class="pre">create_database()</span></code> method.</li>
<li>Fixed a bug (#4863) - <a class="reference internal" href="libraries/table.html"><span class="doc">HTML Table Library</span></a> method <code class="docutils literal"><span class="pre">set_caption()</span></code> was missing method chaining support.</li>
<li>Fixed a bug (#4843) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> client class didn’t set a read/write socket timeout.</li>
<li>Fixed a bug (#4865) - uncaught exceptions didn’t set the HTTP Response status code to 500 unless <code class="docutils literal"><span class="pre">display_errors</span></code> was turned On.</li>
<li>Fixed a bug (#4830) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t take into account the new session INI settings in PHP 7.1.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-1-0">
<h2>Version 3.1.0<a class="headerlink" href="#version-3-1-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: July 26, 2016</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed an SQL injection in the ‘odbc’ database driver.</li>
<li>Updated <a class="reference internal" href="helpers/path_helper.html#set_realpath" title="set_realpath"><code class="xref php php-func docutils literal"><span class="pre">set_realpath()</span></code></a> <a class="reference internal" href="helpers/path_helper.html"><span class="doc">Path Helper</span></a> function to filter-out <code class="docutils literal"><span class="pre">php://</span></code> wrapper inputs.</li>
<li>Officially dropped any kind of support for PHP 5.2.x and anything under 5.3.7.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Updated <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> to validate <em>width</em> and <em>height</em> configuration values.</li>
<li>Updated <a class="reference internal" href="libraries/encryption.html"><span class="doc">Encryption Library</span></a> to always prefer <code class="docutils literal"><span class="pre">random_bytes()</span></code> when it is available.</li>
<li>Updated <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> to log ‘debug’ messages when using fallbacks to <em>session.save_path</em> (php.ini) or ‘sess_use_database’, ‘sess_table_name’ settings.</li>
<li>Added a ‘LONGTEXT’ to ‘STRING’ alias to <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> for the ‘cubrid’, ‘pdo/cubrid’ drivers.</li>
<li>Added ‘TINYINT’, ‘MEDIUMINT’, ‘INT’ and ‘BIGINT’ aliases to ‘NUMBER’ to <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> for the ‘oci8’, ‘pdo/oci’ drivers.</li>
<li><a class="reference internal" href="general/compatibility_functions.html#password_hash" title="password_hash"><code class="xref php php-func docutils literal"><span class="pre">password_hash()</span></code></a> <a class="reference internal" href="general/compatibility_functions.html"><span class="doc">compatibility function</span></a> changes:<ul>
<li>Changed salt-generation logic to prefer <code class="docutils literal"><span class="pre">random_bytes()</span></code> when it is available.</li>
<li>Changed salt-generation logic to prefer direct access to <em>/dev/urandom</em> over <code class="docutils literal"><span class="pre">openssl_random_pseudo_bytes()</span></code>.</li>
<li>Changed salt-generation logic to error if <code class="docutils literal"><span class="pre">openssl_random_pseudo_bytes()</span></code> sets its <code class="docutils literal"><span class="pre">$crypto_strong</span></code> flag to FALSE.</li>
</ul>
</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-1-0">
<h3>Bug fixes for 3.1.0<a class="headerlink" href="#bug-fixes-for-3-1-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug where <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> didn’t escape image source paths passed to ImageMagick as shell arguments.</li>
<li>Fixed a bug (#861) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> incorrectly accepts field width constraints for MSSQL/SQLSRV integer-type columns.</li>
<li>Fixed a bug (#4562) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> didn’t check if <code class="docutils literal"><span class="pre">Memcached::quit()</span></code> is available before calling it.</li>
<li>Fixed a bug (#4563) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">request_headers()</span></code> ignores <code class="docutils literal"><span class="pre">$xss_clean</span></code> parameter value after first call.</li>
<li>Fixed a bug (#4605) - <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> method <code class="docutils literal"><span class="pre">site_url()</span></code> stripped trailing slashes from relative URIs passed to it.</li>
<li>Fixed a bug (#4613) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> failed to send multiple emails via SMTP due to “already authenticated” errors when keep-alive is enabled.</li>
<li>Fixed a bug (#4633) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> ignored multiple “callback” rules for empty, non-required fields.</li>
<li>Fixed a bug (#4637) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">error()</span></code> returned <code class="docutils literal"><span class="pre">FALSE</span></code> with the ‘oci8’ driver if there was no error.</li>
<li>Fixed a bug (#4647) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> doesn’t take into account <code class="docutils literal"><span class="pre">GROUP</span> <span class="pre">BY</span></code> clauses while deciding whether to do a subquery or not.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> ‘redis’ driver didn’t properly detect if a connection is properly closed on PHP 5.x.</li>
<li>Fixed a bug (#4583) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t properly handle inline attachments in HTML emails.</li>
<li>Fixed a bug where <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">db_select()</span></code> didn’t clear metadata cached for the previously used database.</li>
<li>Fixed a bug (#4675) - <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File Helper</span></a> function <a class="reference internal" href="helpers/file_helper.html#delete_files" title="delete_files"><code class="xref php php-func docutils literal"><span class="pre">delete_files()</span></code></a> treated symbolic links as regular directories.</li>
<li>Fixed a bug (#4674) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘dblib’ triggered E_WARNING messages while connecting.</li>
<li>Fixed a bug (#4678) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> tried to use unsupported <code class="docutils literal"><span class="pre">IF</span> <span class="pre">NOT</span> <span class="pre">EXISTS</span></code> clause when creating tables on Oracle.</li>
<li>Fixed a bug (#4691) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> method <code class="docutils literal"><span class="pre">data()</span></code> returns wrong ‘raw_name’ when the filename extension is also contained in the raw filename.</li>
<li>Fixed a bug (#4679) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">ip_address()</span></code> errors with a matching <code class="docutils literal"><span class="pre">$config['proxy_ips']</span></code> IPv6 address.</li>
<li>Fixed a bug (#4695) - <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> didn’t load the <em>config/user_agents.php</em> file when there’s no <code class="docutils literal"><span class="pre">User-Agent</span></code> HTTP request header.</li>
<li>Fixed a bug (#4713) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">insert_batch()</span></code>, <code class="docutils literal"><span class="pre">update_batch()</span></code> could return wrong affected rows count.</li>
<li>Fixed a bug (#4712) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> doesn’t sent <code class="docutils literal"><span class="pre">RSET</span></code> to SMTP servers after a failure and while using keep-alive.</li>
<li>Fixed a bug (#4724) - <a class="reference internal" href="general/common_functions.html"><span class="doc">Common function</span></a> <a class="reference internal" href="general/common_functions.html#is_https" title="is_https"><code class="xref php php-func docutils literal"><span class="pre">is_https()</span></code></a> compared the <code class="docutils literal"><span class="pre">X-Forwarded-Proto</span></code> HTTP header case-sensitively.</li>
<li>Fixed a bug (#4725) - <a class="reference internal" href="general/common_functions.html"><span class="doc">Common function</span></a> <a class="reference internal" href="general/common_functions.html#remove_invisible_characters" title="remove_invisible_characters"><code class="xref php php-func docutils literal"><span class="pre">remove_invisible_characters()</span></code></a> searched case-sensitively for URL-encoded characters.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-6">
<h2>Version 3.0.6<a class="headerlink" href="#version-3-0-6" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 21, 2016</p>
<ul class="simple">
<li>General Changes<ul>
<li>Added a destructor to <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘memcached’ driver to ensure that Memcache(d) connections are properly closed.</li>
<li>Deprecated <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">prep_for_form()</span></code>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-6">
<h3>Bug fixes for 3.0.6<a class="headerlink" href="#bug-fixes-for-3-0-6" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4516) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> always accepted empty array inputs.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> allowed accessing <code class="docutils literal"><span class="pre">$_SESSION</span></code> values as class properties but <code class="docutils literal"><span class="pre">isset()</span></code> didn’t work on them.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> modified the <code class="docutils literal"><span class="pre">$_POST</span></code> array when the data being validated was actually provided via <code class="docutils literal"><span class="pre">set_data()</span></code>.</li>
<li>Fixed a bug (#4539) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> applied migrations before validating that all migrations within the requested version range are valid.</li>
<li>Fixed a bug (#4539) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> triggered failures for migrations that are out of the requested version range.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-5">
<h2>Version 3.0.5<a class="headerlink" href="#version-3-0-5" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 11, 2016</p>
<ul class="simple">
<li>Core<ul>
<li>Changed <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> to allow <code class="docutils literal"><span class="pre">$autoload['drivers']</span></code> assigning with custom property names.</li>
<li>Changed <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> to ignore variables prefixed with ‘_ci_’ when loading views.</li>
</ul>
</li>
<li>General Changes<ul>
<li>Updated the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> to produce friendlier error messages on failures with drivers other than ‘files’.</li>
</ul>
</li>
<li><a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a><ul>
<li>Added a <code class="docutils literal"><span class="pre">$batch_size</span></code> parameter to the <code class="docutils literal"><span class="pre">insert_batch()</span></code> method (defaults to 100).</li>
<li>Added a <code class="docutils literal"><span class="pre">$batch_size</span></code> parameter to the <code class="docutils literal"><span class="pre">update_batch()</span></code> method (defaults to 100).</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-5">
<h3>Bug fixes for 3.0.5<a class="headerlink" href="#bug-fixes-for-3-0-5" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4391) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> method <code class="docutils literal"><span class="pre">reply_to()</span></code> didn’t apply Q-encoding.</li>
<li>Fixed a bug (#4384) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> ignored (possible) <em>cur_page</em> configuration value.</li>
<li>Fixed a bug (#4395) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> still fails if an <code class="docutils literal"><span class="pre">ORDER</span> <span class="pre">BY</span></code> condition is used.</li>
<li>Fixed a bug (#4399) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">insert_batch()</span></code>, <code class="docutils literal"><span class="pre">update_batch()</span></code> produced confusing error messages when called with no data and <em>db_debug</em> is enabled.</li>
<li>Fixed a bug (#4401) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> breaks <code class="docutils literal"><span class="pre">WHERE</span></code> and <code class="docutils literal"><span class="pre">HAVING</span></code> conditions that use <code class="docutils literal"><span class="pre">IN()</span></code> with strings containing a closing parenthesis.</li>
<li>Fixed a regression in <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> functions <a class="reference internal" href="helpers/form_helper.html#set_checkbox" title="set_checkbox"><code class="xref php php-func docutils literal"><span class="pre">set_checkbox()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#set_radio" title="set_radio"><code class="xref php php-func docutils literal"><span class="pre">set_radio()</span></code></a> where “checked” inputs aren’t recognized after a form submit.</li>
<li>Fixed a bug (#4407) - <a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text Helper</span></a> function <a class="reference internal" href="helpers/text_helper.html#word_censor" title="word_censor"><code class="xref php php-func docutils literal"><span class="pre">word_censor()</span></code></a> doesn’t work under PHP 7 if there’s no custom replacement provided.</li>
<li>Fixed a bug (#4415) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>valid_url</strong> didn’t accept URLs with IPv6 addresses enclosed in square brackets under PHP 5 (upstream bug).</li>
<li>Fixed a bug (#4427) - <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> triggers an error if the provided character pool is too small.</li>
<li>Fixed a bug (#4430) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> option <strong>file_ext_tolower</strong> didn’t work.</li>
<li>Fixed a bug (#4431) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">join()</span></code> discarded opening parentheses.</li>
<li>Fixed a bug (#4424) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> triggered a PHP warning when writing a newly created session with the ‘redis’ driver.</li>
<li>Fixed a bug (#4437) - <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector Helper</span></a> function <a class="reference internal" href="helpers/inflector_helper.html#humanize" title="humanize"><code class="xref php php-func docutils literal"><span class="pre">humanize()</span></code></a> didn’t escape its <code class="docutils literal"><span class="pre">$separator</span></code> parameter while using it in a regular expression.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t properly handle its locks’ statuses with the ‘memcached’ driver.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> triggered a PHP warning when writing a newly created session with the ‘memcached’ driver.</li>
<li>Fixed a bug (#4449) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">join()</span></code> breaks conditions containing <code class="docutils literal"><span class="pre">IS</span> <span class="pre">NULL</span></code>, <code class="docutils literal"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">NULL</span></code>.</li>
<li>Fixed a bug (#4491) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t clean-up internal variables for emulated locks with the ‘redis’ driver.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t clean-up internal variables for emulated locks with the ‘memcached’ driver.</li>
<li>Fixed a bug where <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> transactions didn’t work with the ‘ibase’ driver.</li>
<li>Fixed a bug (#4475) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">strip_image_tags()</span></code> preserves only the first URL character from non-quoted <em>src</em> attributes.</li>
<li>Fixed a bug where <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler Library</span></a> didn’t apply <code class="docutils literal"><span class="pre">htmlspecialchars()</span></code> to all displayed inputs.</li>
<li>Fixed a bug (#4277) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> triggered fatal errors if accessing the Memcache(d) and/or Redis driver and they are not available on the system.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> method <code class="docutils literal"><span class="pre">is_supported()</span></code> logged an error message when it returns <code class="docutils literal"><span class="pre">FALSE</span></code> for the APC and Wincache drivers.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-4">
<h2>Version 3.0.4<a class="headerlink" href="#version-3-0-4" title="Permalink to this headline">¶</a></h2>
<p>Release Date: January 13, 2016</p>
<ul class="simple">
<li>General Changes<ul>
<li>Updated <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">get_random_bytes()</span></code> to use PHP 7’s <code class="docutils literal"><span class="pre">random_bytes()</span></code> function when possible.</li>
<li>Updated <a class="reference internal" href="libraries/security.html"><span class="doc">Encryption Library</span></a> method <code class="docutils literal"><span class="pre">create_key()</span></code> to use PHP 7’s <code class="docutils literal"><span class="pre">random_bytes()</span></code> function when possible.</li>
</ul>
</li>
<li><a class="reference internal" href="database/index.html"><span class="doc">Database</span></a><ul>
<li>Added support for <code class="docutils literal"><span class="pre">OFFSET-FETCH</span></code> with Oracle 12c for the ‘oci8’ and ‘pdo/oci’ drivers.</li>
<li>Added support for the new <code class="docutils literal"><span class="pre">MYSQLI_CLIENT_SSL_DONT_VERIFY_SERVER_CERT</span></code> constant from <a class="reference external" href="https://secure.php.net/ChangeLog-5.php#5.6.16">PHP 5.6.16</a> for the ‘mysqli’ driver.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-4">
<h3>Bug fixes for 3.0.4<a class="headerlink" href="#bug-fixes-for-3-0-4" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4212) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> could fail if an <code class="docutils literal"><span class="pre">ORDER</span> <span class="pre">BY</span></code> condition is used.</li>
<li>Fixed a bug where <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> functions <a class="reference internal" href="helpers/form_helper.html#set_checkbox" title="set_checkbox"><code class="xref php php-func docutils literal"><span class="pre">set_checkbox()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#set_radio" title="set_radio"><code class="xref php php-func docutils literal"><span class="pre">set_radio()</span></code></a> didn’t “uncheck” inputs on a submitted form if the default state is “checked”.</li>
<li>Fixed a bug (#4217) - <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> method <code class="docutils literal"><span class="pre">base_url()</span></code> didn’t use proper formatting for IPv6 when it falls back to <code class="docutils literal"><span class="pre">$_SERVER['SERVER_ADDR']</span></code>.</li>
<li>Fixed a bug where <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> entered an infinite loop while generating a random string.</li>
<li>Fixed a bug (#4223) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">simple_query()</span></code> blindly executes queries without checking if the connection was initialized properly.</li>
<li>Fixed a bug (#4244) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> could improperly use “unsafe” US-ASCII characters during Quoted-printable encoding.</li>
<li>Fixed a bug (#4245) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> couldn’t properly handle <code class="docutils literal"><span class="pre">SET</span></code> and <code class="docutils literal"><span class="pre">ENUM</span></code> type fields with string values.</li>
<li>Fixed a bug (#4283) - <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a> function <a class="reference internal" href="helpers/string_helper.html#alternator" title="alternator"><code class="xref php php-func docutils literal"><span class="pre">alternator()</span></code></a> couldn’t be called without arguments.</li>
<li>Fixed a bug (#4306) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">version()</span></code> didn’t work properly with the ‘mssql’ driver.</li>
<li>Fixed a bug (#4039) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> could generate multiple (redundant) warnings in case of a read failure with the ‘files’ driver, due to a bug in PHP.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> didn’t have proper error handling on PHP 5 (due to a PHP bug).</li>
<li>Fixed a bug (#4312) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t provide error feedback for failed validation on empty requests.</li>
<li>Fixed a bug where <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <cite>version()</cite> returned banner text instead of only the version number with the ‘oci8’ and ‘pdo/oci’ drivers.</li>
<li>Fixed a bug (#4331) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">error()</span></code> didn’t really work for connection errors with the ‘mysqli’ driver.</li>
<li>Fixed a bug (#4343) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> failing with a <em>“More than one ‘from’ person”</em> message when using <em>sendmail</em>.</li>
<li>Fixed a bug (#4350) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">model()</span></code> logic directly instantiated the <code class="docutils literal"><span class="pre">CI_Model</span></code> or <code class="docutils literal"><span class="pre">MY_Model</span></code> classes.</li>
<li>Fixed a bug (#4337) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">query()</span></code> didn’t return a result set for queries with the <code class="docutils literal"><span class="pre">RETURNING</span></code> statement on PostgreSQL.</li>
<li>Fixed a bug (#4362) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> doesn’t properly maintain its state after ID regeneration with the ‘redis’ and ‘memcached’ drivers on PHP 7.</li>
<li>Fixed a bug (#4349) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> drivers ‘mysql’, ‘mysqli’, ‘pdo/mysql’ discard other <code class="docutils literal"><span class="pre">sql_mode</span></code> flags when “stricton” is enabled.</li>
<li>Fixed a bug (#4349) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> drivers ‘mysql’, ‘mysqli’, ‘pdo/mysql’ don’t turn off <code class="docutils literal"><span class="pre">STRICT_TRANS_TABLES</span></code> on MySQL 5.7+ when “stricton” is disabled.</li>
<li>Fixed a bug (#4374) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> with the ‘database’ driver could be affected by userspace <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> conditions.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-3">
<h2>Version 3.0.3<a class="headerlink" href="#version-3-0-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 31, 2015</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed an XSS attack vector in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
<li>Changed <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> method <code class="docutils literal"><span class="pre">base_url()</span></code> to fallback to <code class="docutils literal"><span class="pre">$_SERVER['SERVER_ADDR']</span></code> when <code class="docutils literal"><span class="pre">$config['base_url']</span></code> is empty in order to avoid <em>Host</em> header injections.</li>
<li>Changed <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> to use the operating system’s PRNG when possible.</li>
</ul>
</li>
<li><a class="reference internal" href="database/index.html"><span class="doc">Database</span></a><ul>
<li>Optimized <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utility</span></a> method <code class="docutils literal"><span class="pre">csv_from_result()</span></code> for speed with larger result sets.</li>
<li>Added proper return values to <a class="reference internal" href="database/transactions.html"><span class="doc">Database Transactions</span></a> method <code class="docutils literal"><span class="pre">trans_start()</span></code>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-3">
<h3>Bug fixes for 3.0.3<a class="headerlink" href="#bug-fixes-for-3-0-3" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#4170) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">insert_id()</span></code> could return an identity from the wrong scope with the ‘sqlsrv’ driver.</li>
<li>Fixed a bug (#4179) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> doesn’t properly maintain its state after ID regeneration with the ‘database’ driver on PHP 7.</li>
<li>Fixed a bug (#4173) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">add_key()</span></code> didn’t allow creation of non-PRIMARY composite keys after the “bugfix” for #3968.</li>
<li>Fixed a bug (#4171) - <a class="reference internal" href="database/transactions.html"><span class="doc">Database Transactions</span></a> didn’t work with nesting in methods <code class="docutils literal"><span class="pre">trans_begin()</span></code>, <code class="docutils literal"><span class="pre">trans_commit()</span></code>, <code class="docutils literal"><span class="pre">trans_rollback()</span></code>.</li>
<li>Fixed a bug where <a class="reference internal" href="database/transactions.html"><span class="doc">Database Transaction</span></a> methods <code class="docutils literal"><span class="pre">trans_begin()</span></code>, <code class="docutils literal"><span class="pre">trans_commit()</span></code>, <code class="docutils literal"><span class="pre">trans_rollback()</span></code> ignored failures.</li>
<li>Fixed a bug where all <a class="reference internal" href="database/transactions.html"><span class="doc">Database Transaction</span></a> methods returned TRUE while transactions are actually disabled.</li>
<li>Fixed a bug where <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#html_escape" title="html_escape"><code class="xref php php-func docutils literal"><span class="pre">html_escape()</span></code></a> modified keys of its array inputs.</li>
<li>Fixed a bug (#4192) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> wouldn’t always have proper Quoted-printable encoding due to a bug in PHP’s own <code class="docutils literal"><span class="pre">mb_mime_encodeheader()</span></code> function.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-2">
<h2>Version 3.0.2<a class="headerlink" href="#version-3-0-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 8, 2015</p>
<ul class="simple">
<li><strong>Security</strong><ul>
<li>Fixed a number of XSS attack vectors in <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code>  (thanks to Frans Rosén from <a class="reference external" href="https://detectify.com/">Detectify</a>).</li>
</ul>
</li>
<li>General Changes<ul>
<li>Updated the <em>application/config/constants.php</em> file to check if constants aren’t already defined before doing that.</li>
<li>Changed <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">model()</span></code> to only apply <code class="docutils literal"><span class="pre">ucfirst()</span></code> and not <code class="docutils literal"><span class="pre">strtolower()</span></code> to the requested class name.</li>
<li>Changed <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> methods <code class="docutils literal"><span class="pre">base_url()</span></code>, <code class="docutils literal"><span class="pre">site_url()</span></code> to allow protocol-relative URLs by passing an empty string as the protocol.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-2">
<h3>Bug fixes for 3.0.2<a class="headerlink" href="#bug-fixes-for-3-0-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#2284) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> breaks when <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> isn’t enabled.</li>
<li>Fixed a bug (#4052) - <a class="reference internal" href="general/routing.html"><span class="doc">Routing</span></a> with anonymous functions didn’t work for routes that don’t use regular expressions.</li>
<li>Fixed a bug (#4056) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">get_request_header()</span></code> could not return a value unless <code class="docutils literal"><span class="pre">request_headers()</span></code> was called beforehand.</li>
<li>Fixed a bug where the <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> entered an endless loop if it fails to connect with the ‘sqlsrv’ driver.</li>
<li>Fixed a bug (#4065) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> treats a traling space as an alias separator if the input doesn’t contain ‘ AS ‘.</li>
<li>Fixed a bug (#4066) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> couldn’t fallback to a backup driver if the primary one is Memcache(d) or Redis.</li>
<li>Fixed a bug (#4073) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> method <code class="docutils literal"><span class="pre">send()</span></code> could return TRUE in case of an actual failure when an SMTP command fails.</li>
<li>Fixed a bug (#4086) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t apply <em>dbprefix</em> to LIKE conditions if the pattern included spaces.</li>
<li>Fixed a bug (#4091) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘file’ driver could be tricked into accepting empty cache item IDs.</li>
<li>Fixed a bug (#4093) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> modified string values containing ‘AND’, ‘OR’ while compiling WHERE conditions.</li>
<li>Fixed a bug (#4096) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t apply <em>dbprefix</em> when compiling BETWEEN conditions.</li>
<li>Fixed a bug (#4105) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t allow pipe characters inside “bracket parameters” when using a string ruleset.</li>
<li>Fixed a bug (#4109) - <a class="reference internal" href="general/routing.html"><span class="doc">Routing</span></a> to <em>default_controller</em> didn’t work when <em>enable_query_strings</em> is set to TRUE.</li>
<li>Fixed a bug (#4044) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> ‘redis’ driver didn’t catch <code class="docutils literal"><span class="pre">RedisException</span></code> that could be thrown during authentication.</li>
<li>Fixed a bug (#4120) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">error()</span></code> didn’t return error info when called after <code class="docutils literal"><span class="pre">query()</span></code> with the ‘mssql’ driver.</li>
<li>Fixed a bug (#4116) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> set the wrong page number on the “data-ci-pagination-page” attribute in generated links.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> added the ‘rel=”start”’ attribute to the first displayed link even if it’s not actually linking the first page.</li>
<li>Fixed a bug (#4137) - <a class="reference internal" href="general/errors.html"><span class="doc">Error Handling</span></a> breaks for the new <code class="docutils literal"><span class="pre">Error</span></code> exceptions under PHP 7.</li>
<li>Fixed a bug (#4126) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">reset_validation()</span></code> discarded validation rules from config files.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-1">
<h2>Version 3.0.1<a class="headerlink" href="#version-3-0-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: August 7, 2015</p>
<ul class="simple">
<li>Core<ul>
<li>Added DoS mitigation to <a class="reference internal" href="general/compatibility_functions.html#hash_pbkdf2" title="hash_pbkdf2"><code class="xref php php-func docutils literal"><span class="pre">hash_pbkdf2()</span></code></a> <a class="reference internal" href="general/compatibility_functions.html"><span class="doc">compatibility function</span></a>.</li>
</ul>
</li>
<li>Database<ul>
<li>Added <code class="docutils literal"><span class="pre">list_fields()</span></code> support for SQLite (‘sqlite3’ and ‘pdo_sqlite’ drivers).</li>
<li>Added SSL connection support for the ‘mysqli’ and ‘pdo_mysql’ drivers.</li>
</ul>
</li>
<li>Libraries<ul>
<li><a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> changes:<ul>
<li>Changed method <code class="docutils literal"><span class="pre">set_error()</span></code> to accept a custom log level (defaults to ‘error’).</li>
<li>Errors “no_file_selected”, “file_partial”, “stopped_by_extension”, “no_file_types”, “invalid_filetype”, “bad_filename” are now logged at the ‘debug’ level.</li>
<li>Errors “file_exceeds_limit”, “file_exceeds_form_limit”, “invalid_filesize”, “invalid_dimensions” are now logged at the ‘info’ level.</li>
</ul>
</li>
<li>Added ‘is_resource’ to the available expectations in <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a>.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added Unicode support to <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#url_title" title="url_title"><code class="xref php php-func docutils literal"><span class="pre">url_title()</span></code></a>.</li>
<li>Added support for passing the “extra” parameter as an array to all <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> functions that use it.</li>
</ul>
</li>
<li>Core<ul>
<li>Added support for defining a list of specific query parameters in <code class="docutils literal"><span class="pre">$config['cache_query_string']</span></code> for the <a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a>.</li>
<li>Added class existence and inheritance checks to <code class="docutils literal"><span class="pre">CI_Loader::model()</span></code> in order to ease debugging in case of name collisions.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0-1">
<h3>Bug fixes for 3.0.1<a class="headerlink" href="#bug-fixes-for-3-0-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#3733) - Autoloading of libraries with aliases didn’t work, although it was advertised to.</li>
<li>Fixed a bug (#3744) - Redis <a class="reference internal" href="libraries/caching.html"><span class="doc">Caching</span></a> driver didn’t handle authentication failures properly.</li>
<li>Fixed a bug (#3761) - <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#anchor" title="anchor"><code class="xref php php-func docutils literal"><span class="pre">anchor()</span></code></a> didn’t work with array inputs.</li>
<li>Fixed a bug (#3773) - <code class="docutils literal"><span class="pre">db_select()</span></code> didn’t work for MySQL with the PDO <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver.</li>
<li>Fixed a bug (#3771) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> was looking for a ‘form_validation_’ prefix when trying to translate field name labels.</li>
<li>Fixed a bug (#3787) - <a class="reference internal" href="libraries/ftp.html"><span class="doc">FTP Library</span></a> method <code class="docutils literal"><span class="pre">delete_dir()</span></code> failed when the target has subdirectories.</li>
<li>Fixed a bug (#3801) - <a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a> method <code class="docutils literal"><span class="pre">_display_cache()</span></code> incorrectly looked for the last modified time of a directory instead of the cache file.</li>
<li>Fixed a bug (#3816) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> treated empty string values as non-existing ones.</li>
<li>Fixed a bug (#3823) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> drivers Redis and Memcached didn’t properly handle locks that are blocking the request for more than 30 seconds.</li>
<li>Fixed a bug (#3846) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <cite>image_mirror_gd()</cite> didn’t properly initialize its variables.</li>
<li>Fixed a bug (#3854) - <cite>field_data()</cite> didn’t work properly with the Oracle (OCI8) database driver.</li>
<li>Fixed a bug in the <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utility Class</span></a> method <code class="docutils literal"><span class="pre">csv_from_result()</span></code> didn’t work with a whitespace CSV delimiter.</li>
<li>Fixed a bug (#3890) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> method <code class="docutils literal"><span class="pre">get_request_header()</span></code> treated header names as case-sensitive.</li>
<li>Fixed a bug (#3903) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> ignored “unnamed” closure validation rules.</li>
<li>Fixed a bug (#3904) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> ignored “named” callback rules when the field is empty and there’s no ‘required’ rule.</li>
<li>Fixed a bug (#3922) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email</span></a> and <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC</span></a> libraries could enter an infinite loop due to <a class="reference external" href="https://bugs.php.net/bug.php?id=39598">PHP bug #39598</a>.</li>
<li>Fixed a bug (#3913) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> didn’t work with the direct <code class="docutils literal"><span class="pre">$this-&gt;cache-&gt;$driver_name-&gt;method()</span></code> syntax with Redis and Memcache(d).</li>
<li>Fixed a bug (#3932) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t properly compile WHERE and HAVING conditions for field names that end with “and”, “or”.</li>
<li>Fixed a bug in <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> where <code class="docutils literal"><span class="pre">delete()</span></code> didn’t properly work on multiple tables with a WHERE condition previously set via <code class="docutils literal"><span class="pre">where()</span></code>.</li>
<li>Fixed a bug (#3952) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">list_fields()</span></code> didn’t work with SQLite3.</li>
<li>Fixed a bug (#3955) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> methods <code class="docutils literal"><span class="pre">increment()</span></code> and <code class="docutils literal"><span class="pre">decrement()</span></code> ignored the ‘key_prefix’ setting.</li>
<li>Fixed a bug (#3963) - <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a> wrongly tried to translate filenames, line numbers and notes values in test results.</li>
<li>Fixed a bug (#3965) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> ignored the “encrypt_name” setting when “overwrite” is enabled.</li>
<li>Fixed a bug (#3968) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">add_key()</span></code> didn’t treat array inputs as composite keys unless it’s a PRIMARY KEY.</li>
<li>Fixed a bug (#3715) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> could generate broken link when a protocol-relative base URL is used.</li>
<li>Fixed a bug (#3828) - <a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a> method <code class="docutils literal"><span class="pre">delete_cache()</span></code> couldn’t delete index page caches.</li>
<li>Fixed a bug (#3704) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> method <code class="docutils literal"><span class="pre">stored_procedure()</span></code> in the ‘oci8’ driver didn’t properly bind parameters.</li>
<li>Fixed a bug (#3778) - <a class="reference internal" href="helpers/download_helper.html"><span class="doc">Download Helper</span></a> function <a class="reference internal" href="helpers/download_helper.html#force_download" title="force_download"><code class="xref php php-func docutils literal"><span class="pre">force_download()</span></code></a> incorrectly sent a <em>Pragma</em> response header.</li>
<li>Fixed a bug (#3752) - <code class="docutils literal"><span class="pre">$routing['directory']</span></code> overrides were not properly handled and always resulted in a 404 “Not Found” error.</li>
<li>Fixed a bug (#3279) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">update()</span></code> and <code class="docutils literal"><span class="pre">get_compiled_update()</span></code> did double escaping on the table name if it was provided via <code class="docutils literal"><span class="pre">from()</span></code>.</li>
<li>Fixed a bug (#3991) - <code class="docutils literal"><span class="pre">$config['rewrite_short_tags']</span></code> never worked due to <code class="docutils literal"><span class="pre">function_exists('eval')</span></code> always returning FALSE.</li>
<li>Fixed a bug where the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> library will not properly configure its maximum file size unless the input value is of type integer.</li>
<li>Fixed a bug (#4000) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> didn’t enable “rel” attributes by default if no attributes-related config options were used.</li>
<li>Fixed a bug (#4004) - <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Class</span></a> didn’t properly parse the request URI if it contains a colon followed by a digit.</li>
<li>Fixed a bug in <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> where the <code class="docutils literal"><span class="pre">$escape</span></code> parameter for some methods only affected field names.</li>
<li>Fixed a bug (#4012) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">where_in()</span></code>, <code class="docutils literal"><span class="pre">or_where_in()</span></code>, <code class="docutils literal"><span class="pre">where_not_in()</span></code>, <code class="docutils literal"><span class="pre">or_where_not_in()</span></code> didn’t take into account previously cached WHERE conditions when query cache is in use.</li>
<li>Fixed a bug (#4015) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> method <code class="docutils literal"><span class="pre">set_header()</span></code> didn’t support method chaining, although it was advertised.</li>
<li>Fixed a bug (#4027) - <a class="reference internal" href="general/routing.html"><span class="doc">Routing</span></a> with HTTP verbs only worked if the route request method was declared in all-lowercase letters.</li>
<li>Fixed a bug (#4026) - <a class="reference internal" href="database/transactions.html"><span class="doc">Database Transactions</span></a> always rollback if any previous <code class="docutils literal"><span class="pre">query()</span></code> call fails.</li>
<li>Fixed a bug (#4023) - <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a> function <code class="docutils literal"><span class="pre">increment_string()</span></code> didn’t escape its <code class="docutils literal"><span class="pre">$separator</span></code> parameter.</li>
</ul>
</div>
</div>
<div class="section" id="version-3-0-0">
<h2>Version 3.0.0<a class="headerlink" href="#version-3-0-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 30, 2015</p>
<ul class="simple">
<li>License<ul>
<li>CodeIgniter has been relicensed with the <a class="reference external" href="http://opensource.org/licenses/MIT">MIT License</a>, eliminating its old proprietary licensing.</li>
</ul>
</li>
<li>General Changes<ul>
<li>PHP 5.1.6 is no longer supported. CodeIgniter now requires PHP 5.2.4 and recommends PHP 5.4+ or newer to be used.</li>
<li>Changed filenaming convention (class file names now must be Ucfirst and everything else in lowercase).</li>
<li>Changed the default database driver to ‘mysqli’ (the old ‘mysql’ driver is DEPRECATED).</li>
<li><code class="docutils literal"><span class="pre">$_SERVER['CI_ENV']</span></code> can now be set to control the <code class="docutils literal"><span class="pre">ENVIRONMENT</span></code> constant.</li>
<li>Added an optional backtrace to php-error template.</li>
<li>Added Android to the list of user agents.</li>
<li>Added Windows 7, Windows 8, Windows 8.1, Android, Blackberry, iOS and PlayStation 3 to the list of user platforms.</li>
<li>Added Fennec (Firefox for mobile) to the list of mobile user agents.</li>
<li>Ability to log certain error types, not all under a threshold.</li>
<li>Added support for pem, p10, p12, p7a, p7c, p7m, p7r, p7s, crt, crl, der, kdb, rsa, cer, sst, csr Certs to mimes.php.</li>
<li>Added support for pgp, gpg, zsh and cdr files to mimes.php.</li>
<li>Added support for 3gp, 3g2, mp4, wmv, f4v, vlc Video files to mimes.php.</li>
<li>Added support for m4a, aac, m4u, xspf, au, ac3, flac, ogg, wma Audio files to mimes.php.</li>
<li>Added support for kmz and kml (Google Earth) files to mimes.php.</li>
<li>Added support for ics Calendar files to mimes.php.</li>
<li>Added support for rar, jar and 7zip archives to mimes.php.</li>
<li>Updated support for xml (‘application/xml’) and xsl (‘application/xml’, ‘text/xsl’) files in mimes.php.</li>
<li>Updated support for doc files in mimes.php.</li>
<li>Updated support for docx files in mimes.php.</li>
<li>Updated support for php files in mimes.php.</li>
<li>Updated support for zip files in mimes.php.</li>
<li>Updated support for csv files in mimes.php.</li>
<li>Added Romanian, Greek, Vietnamese and Cyrilic characters in <em>application/config/foreign_characters.php</em>.</li>
<li>Changed logger to only chmod when file is first created.</li>
<li>Removed previously deprecated SHA1 Library.</li>
<li>Removed previously deprecated use of <code class="docutils literal"><span class="pre">$autoload['core']</span></code> in <em>application/config/autoload.php</em>.
Only entries in <code class="docutils literal"><span class="pre">$autoload['libraries']</span></code> are auto-loaded now.</li>
<li>Removed previously deprecated EXT constant.</li>
<li>Updated all classes to be written in PHP 5 style, with visibility declarations and no <code class="docutils literal"><span class="pre">var</span></code> usage for properties.</li>
<li>Added an Exception handler.</li>
<li>Moved error templates to <em>application/views/errors/</em> and made the path configurable via <code class="docutils literal"><span class="pre">$config['error_views_path']</span></code>.</li>
<li>Added support non-HTML error templates for CLI applications.</li>
<li>Moved the Log class to <em>application/core/</em></li>
<li>Global config files are loaded first, then environment ones. Environment config keys overwrite base ones, allowing to only set the keys we want changed per environment.</li>
<li>Changed detection of <code class="docutils literal"><span class="pre">$view_folder</span></code> so that if it’s not found in the current path, it will now also be searched for under the application folder.</li>
<li>Path constants BASEPATH, APPPATH and VIEWPATH are now (internally) defined as absolute paths.</li>
<li>Updated email validation methods to use <code class="docutils literal"><span class="pre">filter_var()</span></code> instead of PCRE.</li>
<li>Changed environment defaults to report all errors in <em>development</em> and only fatal ones in <em>testing</em>, <em>production</em> but only display them in <em>development</em>.</li>
<li>Updated <em>ip_address</em> database field lengths from 16 to 45 for supporting IPv6 address on <a class="reference internal" href="libraries/trackback.html"><span class="doc">Trackback Library</span></a> and <a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">Captcha Helper</span></a>.</li>
<li>Removed <em>cheatsheets</em> and <em>quick_reference</em> PDFs from the documentation.</li>
<li>Added availability checks where usage of dangerous functions like <code class="docutils literal"><span class="pre">eval()</span></code> and <code class="docutils literal"><span class="pre">exec()</span></code> is required.</li>
<li>Added support for changing the file extension of log files using <code class="docutils literal"><span class="pre">$config['log_file_extension']</span></code>.</li>
<li>Added support for turning newline standardization on/off via <code class="docutils literal"><span class="pre">$config['standardize_newlines']</span></code> and set it to FALSE by default.</li>
<li>Added configuration setting <code class="docutils literal"><span class="pre">$config['composer_autoload']</span></code> to enable loading of a <a class="reference external" href="https://getcomposer.org">Composer</a> auto-loader.</li>
<li>Removed the automatic conversion of ‘programmatic characters’ to HTML entities from the <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Library</span></a>.</li>
<li>Changed log messages that say a class or file was loaded to “info” level instead of “debug”, so that they don’t pollute log files when <code class="docutils literal"><span class="pre">$config['log_threshold']</span></code> is set to 2 (debug).</li>
</ul>
</li>
<li>Helpers<ul>
<li><a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> changes include:<ul>
<li>Added an optional third parameter to <a class="reference internal" href="helpers/date_helper.html#timespan" title="timespan"><code class="xref php php-func docutils literal"><span class="pre">timespan()</span></code></a> that constrains the number of time units displayed.</li>
<li>Added an optional parameter to <a class="reference internal" href="helpers/date_helper.html#timezone_menu" title="timezone_menu"><code class="xref php php-func docutils literal"><span class="pre">timezone_menu()</span></code></a> that allows more attributes to be added to the generated select tag.</li>
<li>Added function <a class="reference internal" href="helpers/date_helper.html#date_range" title="date_range"><code class="xref php php-func docutils literal"><span class="pre">date_range()</span></code></a> that generates a list of dates between a specified period.</li>
<li>Deprecated <code class="docutils literal"><span class="pre">standard_date()</span></code>, which now just uses the native <code class="docutils literal"><span class="pre">date()</span></code> with <a class="reference external" href="http://php.net/manual/en/class.datetime.php#datetime.constants.types">DateTime constants</a>.</li>
<li>Changed <a class="reference internal" href="helpers/date_helper.html#now" title="now"><code class="xref php php-func docutils literal"><span class="pre">now()</span></code></a> to work with all timezone strings supported by PHP.</li>
<li>Changed <a class="reference internal" href="helpers/date_helper.html#days_in_month" title="days_in_month"><code class="xref php php-func docutils literal"><span class="pre">days_in_month()</span></code></a> to use the native <code class="docutils literal"><span class="pre">cal_days_in_month()</span></code> PHP function, if available.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> changes include:<ul>
<li>Deprecated <em>separator</em> options <strong>dash</strong> and <strong>underscore</strong> for function <a class="reference internal" href="helpers/url_helper.html#url_title" title="url_title"><code class="xref php php-func docutils literal"><span class="pre">url_title()</span></code></a> (they are only aliases for ‘-‘ and ‘_’ respectively).</li>
<li><a class="reference internal" href="helpers/url_helper.html#url_title" title="url_title"><code class="xref php php-func docutils literal"><span class="pre">url_title()</span></code></a> will now trim extra dashes from beginning and end.</li>
<li><a class="reference internal" href="helpers/url_helper.html#anchor_popup" title="anchor_popup"><code class="xref php php-func docutils literal"><span class="pre">anchor_popup()</span></code></a> will now fill the <em>href</em> attribute with the URL and its JS code will return FALSE instead.</li>
<li>Added JS window name support to the <a class="reference internal" href="helpers/url_helper.html#anchor_popup" title="anchor_popup"><code class="xref php php-func docutils literal"><span class="pre">anchor_popup()</span></code></a> function.</li>
<li>Added support for menubar attribute to the <a class="reference internal" href="helpers/url_helper.html#anchor_popup" title="anchor_popup"><code class="xref php php-func docutils literal"><span class="pre">anchor_popup()</span></code></a>.</li>
<li>Added support (auto-detection) for HTTP/1.1 response codes 303, 307 in <a class="reference internal" href="helpers/url_helper.html#redirect" title="redirect"><code class="xref php php-func docutils literal"><span class="pre">redirect()</span></code></a>.</li>
<li>Changed <a class="reference internal" href="helpers/url_helper.html#redirect" title="redirect"><code class="xref php php-func docutils literal"><span class="pre">redirect()</span></code></a> to choose the <strong>refresh</strong> method only on IIS servers, instead of all servers on Windows (when <strong>auto</strong> is used).</li>
<li>Changed <a class="reference internal" href="helpers/url_helper.html#anchor" title="anchor"><code class="xref php php-func docutils literal"><span class="pre">anchor()</span></code></a>, <a class="reference internal" href="helpers/url_helper.html#anchor_popup" title="anchor_popup"><code class="xref php php-func docutils literal"><span class="pre">anchor_popup()</span></code></a>, and <a class="reference internal" href="helpers/url_helper.html#redirect" title="redirect"><code class="xref php php-func docutils literal"><span class="pre">redirect()</span></code></a> to support protocol-relative URLs (e.g. <em>//ellislab.com/codeigniter</em>).</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML Helper</span></a> changes include:<ul>
<li>Added more doctypes.</li>
<li>Changed application and environment config files to be loaded in a cascade-like manner.</li>
<li>Changed <a class="reference internal" href="helpers/html_helper.html#doctype" title="doctype"><code class="xref php php-func docutils literal"><span class="pre">doctype()</span></code></a> to cache and only load once the doctypes array.</li>
<li>Deprecated functions <code class="docutils literal"><span class="pre">nbs()</span></code> and <code class="docutils literal"><span class="pre">br()</span></code>, which are just aliases for the native <code class="docutils literal"><span class="pre">str_repeat()</span></code> with <code class="docutils literal"><span class="pre">&amp;nbsp;</span></code> and <code class="docutils literal"><span class="pre">&lt;br</span> <span class="pre">/&gt;</span></code> respectively.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector Helper</span></a> changes include:<ul>
<li>Changed <a class="reference internal" href="helpers/inflector_helper.html#humanize" title="humanize"><code class="xref php php-func docutils literal"><span class="pre">humanize()</span></code></a> to allow passing an input separator as its second parameter.</li>
<li>Changed <a class="reference internal" href="helpers/inflector_helper.html#humanize" title="humanize"><code class="xref php php-func docutils literal"><span class="pre">humanize()</span></code></a> and <a class="reference internal" href="helpers/inflector_helper.html#underscore" title="underscore"><code class="xref php php-func docutils literal"><span class="pre">underscore()</span></code></a> to utilize <a class="reference external" href="http://php.net/mbstring">mbstring</a>, if available.</li>
<li>Changed <a class="reference internal" href="helpers/inflector_helper.html#plural" title="plural"><code class="xref php php-func docutils literal"><span class="pre">plural()</span></code></a> and <a class="reference internal" href="helpers/inflector_helper.html#singular" title="singular"><code class="xref php php-func docutils literal"><span class="pre">singular()</span></code></a> to avoid double pluralization and support more words.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/download_helper.html"><span class="doc">Download Helper</span></a> changes include:<ul>
<li>Added an optional third parameter to <a class="reference internal" href="helpers/download_helper.html#force_download" title="force_download"><code class="xref php php-func docutils literal"><span class="pre">force_download()</span></code></a> that enables/disables sending the actual file MIME type in the Content-Type header (disabled by default).</li>
<li>Added a work-around in <a class="reference internal" href="helpers/download_helper.html#force_download" title="force_download"><code class="xref php php-func docutils literal"><span class="pre">force_download()</span></code></a> for a bug Android &lt;= 2.1, where the filename extension needs to be in uppercase.</li>
<li>Added support for reading from an existing file path by passing NULL as the second parameter to <a class="reference internal" href="helpers/download_helper.html#force_download" title="force_download"><code class="xref php php-func docutils literal"><span class="pre">force_download()</span></code></a> (useful for large files and/or safely transmitting binary data).</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> changes include:<ul>
<li><a class="reference internal" href="helpers/form_helper.html#form_dropdown" title="form_dropdown"><code class="xref php php-func docutils literal"><span class="pre">form_dropdown()</span></code></a> will now also take an array for unity with other form helpers.</li>
<li><a class="reference internal" href="helpers/form_helper.html#form_prep" title="form_prep"><code class="xref php php-func docutils literal"><span class="pre">form_prep()</span></code></a> is now DEPRECATED and only acts as an alias for <a class="reference internal" href="general/common_functions.html"><span class="doc">common function</span></a> <a class="reference internal" href="general/common_functions.html#html_escape" title="html_escape"><code class="xref php php-func docutils literal"><span class="pre">html_escape()</span></code></a>.</li>
<li><a class="reference internal" href="helpers/form_helper.html#set_value" title="set_value"><code class="xref php php-func docutils literal"><span class="pre">set_value()</span></code></a> will now also accept a third argument, allowing to turn off HTML escaping of the value.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/security_helper.html"><span class="doc">Security Helper</span></a> changes include:<ul>
<li><a class="reference internal" href="helpers/security_helper.html#do_hash" title="do_hash"><code class="xref php php-func docutils literal"><span class="pre">do_hash()</span></code></a> now uses PHP’s native <code class="docutils literal"><span class="pre">hash()</span></code> function (supporting more algorithms) and is deprecated.</li>
<li><a class="reference internal" href="helpers/security_helper.html#strip_image_tags" title="strip_image_tags"><code class="xref php php-func docutils literal"><span class="pre">strip_image_tags()</span></code></a> is now an alias for the same method in the <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a>.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/smiley_helper.html"><span class="doc">Smiley Helper</span></a> changes include:<ul>
<li>Deprecated the whole helper as too specific for CodeIgniter.</li>
<li>Removed previously deprecated function <code class="docutils literal"><span class="pre">js_insert_smiley()</span></code>.</li>
<li>Changed application and environment config files to be loaded in a cascade-like manner.</li>
<li>The smileys array is now cached and loaded only once.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/file_helper.html"><span class="doc">File Helper</span></a> changes include:<ul>
<li><a class="reference internal" href="helpers/path_helper.html#set_realpath" title="set_realpath"><code class="xref php php-func docutils literal"><span class="pre">set_realpath()</span></code></a> can now also handle file paths as opposed to just directories.</li>
<li>Added an optional paramater to <a class="reference internal" href="helpers/file_helper.html#delete_files" title="delete_files"><code class="xref php php-func docutils literal"><span class="pre">delete_files()</span></code></a> to enable it to skip deleting files such as <em>.htaccess</em> and <em>index.html</em>.</li>
<li>Deprecated function <code class="docutils literal"><span class="pre">read_file()</span></code> - it’s just an alias for PHP’s native <code class="docutils literal"><span class="pre">file_get_contents()</span></code>.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a> changes include:<ul>
<li>Deprecated function <code class="docutils literal"><span class="pre">repeater()</span></code> - it’s just an alias for PHP’s native <code class="docutils literal"><span class="pre">str_repeat()</span></code>.</li>
<li>Deprecated function <code class="docutils literal"><span class="pre">trim_slashes()</span></code> - it’s just an alias for PHP’s native <code class="docutils literal"><span class="pre">trim()</span></code> (with a slash as its second argument).</li>
<li>Deprecated randomization type options <strong>unique</strong> and <strong>encrypt</strong> for funcion <a class="reference internal" href="helpers/string_helper.html#random_string" title="random_string"><code class="xref php php-func docutils literal"><span class="pre">random_string()</span></code></a> (they are only aliases for <strong>md5</strong> and <strong>sha1</strong> respectively).</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">CAPTCHA Helper</span></a> changes include:<ul>
<li>Added <em>word_length</em> and <em>pool</em> options to allow customization of the generated word.</li>
<li>Added <em>colors</em> configuration to allow customization for the <em>background</em>, <em>border</em>, <em>text</em> and <em>grid</em> colors.</li>
<li>Added <em>filename</em> to the returned array elements.</li>
<li>Updated to use <cite>imagepng()</cite> in case that <cite>imagejpeg()</cite> isn’t available.</li>
<li>Added <strong>font_size</strong> option to allow customization of font size.</li>
<li>Added <strong>img_id</strong> option to set id attribute of captcha image.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text Helper</span></a> changes include:<ul>
<li>Changed the default tag for use in <a class="reference internal" href="helpers/text_helper.html#highlight_phrase" title="highlight_phrase"><code class="xref php php-func docutils literal"><span class="pre">highlight_phrase()</span></code></a> to <code class="docutils literal"><span class="pre">&lt;mark&gt;</span></code> (formerly <code class="docutils literal"><span class="pre">&lt;strong&gt;</span></code>).</li>
<li>Changed <a class="reference internal" href="helpers/text_helper.html#character_limiter" title="character_limiter"><code class="xref php php-func docutils literal"><span class="pre">character_limiter()</span></code></a>, <a class="reference internal" href="helpers/text_helper.html#word_wrap" title="word_wrap"><code class="xref php php-func docutils literal"><span class="pre">word_wrap()</span></code></a> and <a class="reference internal" href="helpers/text_helper.html#ellipsize" title="ellipsize"><code class="xref php php-func docutils literal"><span class="pre">ellipsize()</span></code></a> to utilize <a class="reference external" href="http://php.net/mbstring">mbstring</a> or <a class="reference external" href="http://php.net/iconv">iconv</a>, if available.</li>
</ul>
</li>
<li><a class="reference internal" href="helpers/directory_helper.html"><span class="doc">Directory Helper</span></a> <a class="reference internal" href="helpers/directory_helper.html#directory_map" title="directory_map"><code class="xref php php-func docutils literal"><span class="pre">directory_map()</span></code></a> will now append <code class="docutils literal"><span class="pre">DIRECTORY_SEPARATOR</span></code> to directory names in the returned array.</li>
<li><a class="reference internal" href="helpers/array_helper.html"><span class="doc">Array Helper</span></a> <a class="reference internal" href="helpers/array_helper.html#element" title="element"><code class="xref php php-func docutils literal"><span class="pre">element()</span></code></a> and <a class="reference internal" href="helpers/array_helper.html#elements" title="elements"><code class="xref php php-func docutils literal"><span class="pre">elements()</span></code></a> now return NULL instead of FALSE when the required elements don’t exist.</li>
<li><a class="reference internal" href="helpers/language_helper.html"><span class="doc">Language Helper</span></a> <a class="reference internal" href="helpers/language_helper.html#lang" title="lang"><code class="xref php php-func docutils literal"><span class="pre">lang()</span></code></a> now accepts an optional list of additional HTML attributes.</li>
<li>Deprecated the <a class="reference internal" href="helpers/email_helper.html"><span class="doc">Email Helper</span></a> as its <code class="docutils literal"><span class="pre">valid_email()</span></code>, <code class="docutils literal"><span class="pre">send_email()</span></code> functions are now only aliases for PHP native functions <code class="docutils literal"><span class="pre">filter_var()</span></code> and <code class="docutils literal"><span class="pre">mail()</span></code> respectively.</li>
</ul>
</li>
<li>Database<ul>
<li>DEPRECATED the ‘mysql’, ‘sqlite’, ‘mssql’ and ‘pdo/dblib’ (also known as ‘pdo/mssql’ or ‘pdo/sybase’) drivers.</li>
<li>Added <strong>dsn</strong> configuration setting for drivers that support DSN strings (PDO, PostgreSQL, Oracle, ODBC, CUBRID).</li>
<li>Added <strong>schema</strong> configuration setting (defaults to <em>public</em>) for drivers that might need it (currently used by PostgreSQL and ODBC).</li>
<li>Added <strong>save_queries</strong> configuration setting to <em>application/config/database.php</em> (defaults to <code class="docutils literal"><span class="pre">TRUE</span></code>).</li>
<li>Removed <strong>autoinit</strong> configuration setting as it doesn’t make sense to instantiate the database class but not connect to the database.</li>
<li>Added subdrivers support (currently only used by PDO).</li>
<li>Added an optional database name parameter to <code class="docutils literal"><span class="pre">db_select()</span></code>.</li>
<li>Removed <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> and renamed internal method <code class="docutils literal"><span class="pre">_protect_identifiers()</span></code> to it instead - it was just an alias.</li>
<li>Renamed internal method <code class="docutils literal"><span class="pre">_escape_identifiers()</span></code> to <code class="docutils literal"><span class="pre">escape_identifiers()</span></code>.</li>
<li>Updated <code class="docutils literal"><span class="pre">escape_identifiers()</span></code> to accept an array of fields as well as strings.</li>
<li>MySQL and MySQLi drivers now require at least MySQL version 5.1.</li>
<li>Added a <code class="docutils literal"><span class="pre">$persistent</span></code> parameter to <code class="docutils literal"><span class="pre">db_connect()</span></code> and changed <code class="docutils literal"><span class="pre">db_pconnect()</span></code> to be an alias for <code class="docutils literal"><span class="pre">db_connect(TRUE)</span></code>.</li>
<li><code class="docutils literal"><span class="pre">db_set_charset()</span></code> now only requires one parameter (collation was only needed due to legacy support for MySQL versions prior to 5.1).</li>
<li><code class="docutils literal"><span class="pre">db_select()</span></code> will now always (if required by the driver) be called by <code class="docutils literal"><span class="pre">db_connect()</span></code> instead of only when initializing.</li>
<li>Replaced the <code class="docutils literal"><span class="pre">_error_message()</span></code> and <code class="docutils literal"><span class="pre">_error_number()</span></code> methods with <code class="docutils literal"><span class="pre">error()</span></code>, which returns an array containing the last database error code and message.</li>
<li>Improved <code class="docutils literal"><span class="pre">version()</span></code> implementation so that drivers that have a native function to get the version number don’t have to be defined in the core <code class="docutils literal"><span class="pre">DB_driver</span></code> class.</li>
<li>Added capability for packages to hold <em>config/database.php</em> config files.</li>
<li>Added MySQL client compression support.</li>
<li>Added encrypted connections support (for <em>mysql</em>, <em>sqlsrv</em> and PDO with <em>sqlsrv</em>).</li>
<li>Removed <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Class</span></a> from Database error tracing to better find the likely culprit.</li>
<li>Added support for SQLite3 database driver.</li>
<li>Added Interbase/Firebird database support via the <em>ibase</em> driver.</li>
<li>Added ODBC support for <code class="docutils literal"><span class="pre">create_database()</span></code>, <code class="docutils literal"><span class="pre">drop_database()</span></code> and <code class="docutils literal"><span class="pre">drop_table()</span></code> in <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a>.</li>
<li>Added support to binding arrays as <code class="docutils literal"><span class="pre">IN()</span></code> sets in <code class="docutils literal"><span class="pre">query()</span></code>.</li>
<li><a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> changes include:<ul>
<li>Renamed the Active Record class to Query Builder to remove confusion with the Active Record design pattern.</li>
<li>Added the ability to insert objects with <code class="docutils literal"><span class="pre">insert_batch()</span></code>.</li>
<li>Added new methods that return the SQL string of queries without executing them: <code class="docutils literal"><span class="pre">get_compiled_select()</span></code>, <code class="docutils literal"><span class="pre">get_compiled_insert()</span></code>, <code class="docutils literal"><span class="pre">get_compiled_update()</span></code>, <code class="docutils literal"><span class="pre">get_compiled_delete()</span></code>.</li>
<li>Added an optional parameter that allows to disable escaping (useful for custom fields) for methods <code class="docutils literal"><span class="pre">join()</span></code>, <code class="docutils literal"><span class="pre">order_by()</span></code>, <code class="docutils literal"><span class="pre">where_in()</span></code>, <code class="docutils literal"><span class="pre">or_where_in()</span></code>, <code class="docutils literal"><span class="pre">where_not_in()</span></code>, <code class="docutils literal"><span class="pre">or_where_not_in()</span></code>, <code class="docutils literal"><span class="pre">insert()</span></code>, <code class="docutils literal"><span class="pre">insert_batch()</span></code>.</li>
<li>Added support for <code class="docutils literal"><span class="pre">join()</span></code> with multiple conditions.</li>
<li>Added support for <em>USING</em> in <code class="docutils literal"><span class="pre">join()</span></code>.</li>
<li>Added support for <em>EXISTS</em> in <code class="docutils literal"><span class="pre">where()</span></code>.</li>
<li>Added seed values support for random ordering with <code class="docutils literal"><span class="pre">order_by(seed,</span> <span class="pre">'RANDOM')</span></code>.</li>
<li>Changed <code class="docutils literal"><span class="pre">limit()</span></code> to ignore NULL values instead of always casting to integer.</li>
<li>Changed <code class="docutils literal"><span class="pre">offset()</span></code> to ignore empty values instead of always casting to integer.</li>
<li>Methods <code class="docutils literal"><span class="pre">insert_batch()</span></code> and <code class="docutils literal"><span class="pre">update_batch()</span></code> now return an integer representing the number of rows affected by them.</li>
<li>Methods <code class="docutils literal"><span class="pre">where()</span></code>, <code class="docutils literal"><span class="pre">or_where()</span></code>, <code class="docutils literal"><span class="pre">having()</span></code> and <code class="docutils literal"><span class="pre">or_having()</span></code> now convert trailing  <code class="docutils literal"><span class="pre">=</span></code> and <code class="docutils literal"><span class="pre">&lt;&gt;</span></code>,  <code class="docutils literal"><span class="pre">!=</span></code> SQL operators to <code class="docutils literal"><span class="pre">IS</span> <span class="pre">NULL</span></code> and <code class="docutils literal"><span class="pre">IS</span> <span class="pre">NOT</span> <span class="pre">NULL</span></code> respectively when the supplied comparison value is <code class="docutils literal"><span class="pre">NULL</span></code>.</li>
<li>Added method chaining support to <code class="docutils literal"><span class="pre">reset_query()</span></code>, <code class="docutils literal"><span class="pre">start_cache()</span></code>, <code class="docutils literal"><span class="pre">stop_cache()</span></code> and <code class="docutils literal"><span class="pre">flush_cache()</span></code>.</li>
<li>Added an optional second parameter to <code class="docutils literal"><span class="pre">count_all_results()</span></code> to disable resetting of QB values.</li>
</ul>
</li>
<li><a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> changes include:<ul>
<li>Added a constructor to the <code class="docutils literal"><span class="pre">DB_result</span></code> class and moved all driver-specific properties and logic out of the base <code class="docutils literal"><span class="pre">DB_driver</span></code> class to allow better abstraction.</li>
<li>Added method <code class="docutils literal"><span class="pre">unbuffered_row()</span></code> for fetching a row without prefetching the whole result (consume less memory).</li>
<li>Renamed former method <code class="docutils literal"><span class="pre">_data_seek()</span></code> to <code class="docutils literal"><span class="pre">data_seek()</span></code> and made it public.</li>
</ul>
</li>
<li>Improved support for the MySQLi driver, including:<ul>
<li>OOP style usage of the PHP extension is now used, instead of the procedural aliases.</li>
<li>Server version checking is now done via <code class="docutils literal"><span class="pre">mysqli::$server_info</span></code> instead of running an SQL query.</li>
<li>Added persistent connections support for PHP &gt;= 5.3.</li>
<li>Added support for configuring socket pipe connections.</li>
<li>Added support for <code class="docutils literal"><span class="pre">backup()</span></code> in <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a>.</li>
<li>Changed methods <code class="docutils literal"><span class="pre">trans_begin()</span></code>, <code class="docutils literal"><span class="pre">trans_commit()</span></code> and <code class="docutils literal"><span class="pre">trans_rollback()</span></code> to use the PHP API instead of sending queries.</li>
</ul>
</li>
<li>Improved support of the PDO driver, including:<ul>
<li>Added support for <code class="docutils literal"><span class="pre">create_database()</span></code>, <code class="docutils literal"><span class="pre">drop_database()</span></code> and <code class="docutils literal"><span class="pre">drop_table()</span></code> in <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a>.</li>
<li>Added support for <code class="docutils literal"><span class="pre">list_fields()</span></code> in <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a>.</li>
<li>Subdrivers are now isolated from each other instead of being in one large class.</li>
</ul>
</li>
<li>Improved support of the PostgreSQL driver, including:<ul>
<li><code class="docutils literal"><span class="pre">pg_version()</span></code> is now used to get the database version number, when possible.</li>
<li>Added <code class="docutils literal"><span class="pre">db_set_charset()</span></code> support.</li>
<li>Added support for <code class="docutils literal"><span class="pre">optimize_table()</span></code> in <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a> (rebuilds table indexes).</li>
<li>Added boolean data type support in <code class="docutils literal"><span class="pre">escape()</span></code>.</li>
<li>Added <code class="docutils literal"><span class="pre">update_batch()</span></code> support.</li>
<li>Removed <code class="docutils literal"><span class="pre">limit()</span></code> and <code class="docutils literal"><span class="pre">order_by()</span></code> support for <em>UPDATE</em> and <em>DELETE</em> queries as PostgreSQL does not support those features.</li>
<li>Added a work-around for dead persistent connections to be re-created after a database restart.</li>
<li>Changed <code class="docutils literal"><span class="pre">db_connect()</span></code> to include the (new) <strong>schema</strong> value into Postgre’s <strong>search_path</strong> session variable.</li>
<li><code class="docutils literal"><span class="pre">pg_escape_literal()</span></code> is now used for escaping strings, if available.</li>
</ul>
</li>
<li>Improved support of the CUBRID driver, including:<ul>
<li>Added DSN string support.</li>
<li>Added persistent connections support.</li>
<li>Improved <code class="docutils literal"><span class="pre">list_databases()</span></code> in <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utility</span></a> (until now only the currently used database was returned).</li>
</ul>
</li>
<li>Improved support of the MSSQL and SQLSRV drivers, including:<ul>
<li>Added random ordering support.</li>
<li>Added support for <code class="docutils literal"><span class="pre">optimize_table()</span></code> in <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utility</span></a>.</li>
<li>Added escaping with <em>QUOTE_IDENTIFIER</em> setting detection.</li>
<li>Added port handling support for UNIX-based systems (MSSQL driver).</li>
<li>Added <em>OFFSET</em> support for SQL Server 2005 and above.</li>
<li>Added <code class="docutils literal"><span class="pre">db_set_charset()</span></code> support (MSSQL driver).</li>
<li>Added a <em>scrollable</em> property to enable configuration of the cursor to use (SQLSRV driver).</li>
<li>Added support and auto-detection for the <code class="docutils literal"><span class="pre">SQLSRV_CURSOR_CLIENT_BUFFERED</span></code> scrollable cursor flag (SQLSRV driver).</li>
<li>Changed default behavior to not use <code class="docutils literal"><span class="pre">SQLSRV_CURSOR_STATIC</span></code> due to performance issues (SQLSRV driver).</li>
</ul>
</li>
<li>Improved support of the Oracle (OCI8) driver, including:<ul>
<li>Added DSN string support (Easy Connect and TNS).</li>
<li>Added support for <code class="docutils literal"><span class="pre">drop_table()</span></code> in <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a>.</li>
<li>Added support for <code class="docutils literal"><span class="pre">list_databases()</span></code> in <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a>.</li>
<li>Generally improved for speed and cleaned up all of its components.</li>
<li><code class="docutils literal"><span class="pre">num_rows()</span></code> is now only called explicitly by the developer and no longer re-executes statements.</li>
</ul>
</li>
<li>Improved support of the SQLite driver, including:<ul>
<li>Added support for <code class="docutils literal"><span class="pre">replace()</span></code> in <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a>.</li>
<li>Added support for <code class="docutils literal"><span class="pre">drop_table()</span></code> in <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a>.</li>
</ul>
</li>
<li><a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> changes include:<ul>
<li>Added an optional second parameter to <code class="docutils literal"><span class="pre">drop_table()</span></code> that allows adding the <strong>IF EXISTS</strong> condition, which is no longer the default.</li>
<li>Added support for passing a custom database object to the loader.</li>
<li>Added support for passing custom table attributes (such as <code class="docutils literal"><span class="pre">ENGINE</span></code> for MySQL) to <code class="docutils literal"><span class="pre">create_table()</span></code>.</li>
<li>Added support for usage of the <em>FIRST</em> clause in <code class="docutils literal"><span class="pre">add_column()</span></code> for MySQL and CUBRID.</li>
<li>Added partial support for field comments (MySQL, PostgreSQL, Oracle).</li>
<li>Deprecated <code class="docutils literal"><span class="pre">add_column()</span></code>’s third method. <em>AFTER</em> clause should now be added to the field definition array instead.</li>
<li>Overall improved support for all of the drivers.</li>
</ul>
</li>
<li><a class="reference internal" href="database/utilities.html"><span class="doc">Database Utility</span></a> changes include:<ul>
<li>Added support for passing a custom database object to the loader.</li>
<li>Modified the class to no longer extend <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a>, which has been a deprecated behavior for awhile.</li>
<li>Overall improved support for all of the drivers.</li>
<li>Added <em>foreign_key_checks</em> option to MySQL/MySQLi backup, allowing statement to disable/re-enable foreign key checks to be inserted into the backup output.</li>
</ul>
</li>
</ul>
</li>
<li>Libraries<ul>
<li>Added a new <a class="reference internal" href="libraries/encryption.html"><span class="doc">Encryption Library</span></a> to replace the old, largely insecure <a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a>.</li>
<li><a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a> changes include:<ul>
<li>Deprecated the library in favor of the new <a class="reference internal" href="libraries/encryption.html"><span class="doc">Encryption Library</span></a>.</li>
<li>Added support for hashing algorithms other than SHA1 and MD5.</li>
<li>Removed previously deprecated <code class="docutils literal"><span class="pre">sha1()</span></code> method.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> changes include:<ul>
<li>Completely re-written the library to use self-contained drivers via <code class="docutils literal"><span class="pre">$config['sess_driver']</span></code>.</li>
<li>Added ‘files’, ‘database’, ‘redis’ and ‘memcached’ drivers (using ‘files’ by default).</li>
<li>Added <code class="docutils literal"><span class="pre">$config['sess_save_path']</span></code> setting to specify where the session data is stored, depending on the driver.</li>
<li>Dropped support for storing session data in cookies (which renders <code class="docutils literal"><span class="pre">$config['sess_encrypt_cookie']</span></code> useless and is therefore also removed).</li>
<li>Dropped official  support for storing session data in databases other than MySQL and PostgreSQL.</li>
<li>Changed table structure for the ‘database’ driver.</li>
<li>Added a new <strong>tempdata</strong> feature that allows setting userdata items with expiration time (<code class="docutils literal"><span class="pre">mark_as_temp()</span></code>, <code class="docutils literal"><span class="pre">tempdata()</span></code>, <code class="docutils literal"><span class="pre">set_tempdata()</span></code>, <code class="docutils literal"><span class="pre">unset_tempdata()</span></code>).</li>
<li>Changed method <code class="docutils literal"><span class="pre">keep_flashdata()</span></code> to also accept an array of keys.</li>
<li>Changed methods <code class="docutils literal"><span class="pre">userdata()</span></code>, <code class="docutils literal"><span class="pre">flashdata()</span></code> to return an array of all userdata/flashdata when no parameter is passed.</li>
<li>Deprecated method <code class="docutils literal"><span class="pre">all_userdata()</span></code> - it is now just an alias for <code class="docutils literal"><span class="pre">userdata()</span></code> with no parameters.</li>
<li>Added method <code class="docutils literal"><span class="pre">has_userdata()</span></code> that verifies the existence of a userdata item.</li>
<li>Added <em>debug</em> level log messages for key events in the session validation process.</li>
<li>Dropped support for the <em>sess_match_useragent</em> option.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> changes include:<ul>
<li>Added method chaining support.</li>
<li>Added support for using array notation in file field names.</li>
<li>Added <strong>max_filename_increment</strong> and <strong>file_ext_tolower</strong> configuration settings.</li>
<li>Added <strong>min_width</strong> and <strong>min_height</strong> configuration settings for images.</li>
<li>Added <strong>mod_mime_fix</strong> configuration setting to disable suffixing multiple file extensions with an underscore.</li>
<li>Added the possibility pass <strong>allowed_types</strong> as an array.</li>
<li>Added an <code class="docutils literal"><span class="pre">$index</span></code> parameter to the method <code class="docutils literal"><span class="pre">data()</span></code>.</li>
<li>Added a <code class="docutils literal"><span class="pre">$reset</span></code> parameter to method <code class="docutils literal"><span class="pre">initialize()</span></code>.</li>
<li>Removed method <code class="docutils literal"><span class="pre">clean_file_name()</span></code> and its usage in favor of <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a>’s <code class="docutils literal"><span class="pre">sanitize_filename()</span></code>.</li>
<li>Removed method <code class="docutils literal"><span class="pre">mimes_types()</span></code>.</li>
<li>Changed <code class="docutils literal"><span class="pre">CI_Upload::_prep_filename()</span></code> to simply replace all (but the last) dots in the filename with underscores, instead of suffixing them.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/calendar.html"><span class="doc">Calendar Library</span></a> changes include:<ul>
<li>Added method chaining support.</li>
<li>Added configuration to generate days of other months instead of blank cells.</li>
<li>Added auto-configuration for <em>next_prev_url</em> if it is empty and <em>show_prev_next</em> is set to TRUE.</li>
<li>Added support for templating via an array in addition to the encoded string.</li>
<li>Changed method <code class="docutils literal"><span class="pre">get_total_days()</span></code> to be an alias for <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> <a class="reference internal" href="helpers/date_helper.html#days_in_month" title="days_in_month"><code class="xref php php-func docutils literal"><span class="pre">days_in_month()</span></code></a>.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/cart.html"><span class="doc">Cart Library</span></a> changes include:<ul>
<li>Deprecated the library as too specific for CodeIgniter.</li>
<li>Added method <code class="docutils literal"><span class="pre">remove()</span></code> to remove a cart item, updating with quantity of 0 seemed like a hack but has remained to retain compatibility.</li>
<li>Added method <code class="docutils literal"><span class="pre">get_item()</span></code> to enable retrieving data for a single cart item.</li>
<li>Added unicode support for product names.</li>
<li>Added support for disabling product name strictness via the <code class="docutils literal"><span class="pre">$product_name_safe</span></code> property.</li>
<li>Changed <code class="docutils literal"><span class="pre">insert()</span></code> method to auto-increment quantity for an item when inserted twice instead of resetting it.</li>
<li>Changed <code class="docutils literal"><span class="pre">update()</span></code> method to support updating all properties attached to an item and not to require ‘qty’.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> changes include:<ul>
<li>The <code class="docutils literal"><span class="pre">initialize()</span></code> method now only sets existing class properties.</li>
<li>Added support for 3-length hex color values for <em>wm_font_color</em> and <em>wm_shadow_color</em> properties, as well as validation for them.</li>
<li>Class properties <em>wm_font_color</em>, <em>wm_shadow_color</em> and <em>wm_use_drop_shadow</em> are now protected, to avoid breaking the <code class="docutils literal"><span class="pre">text_watermark()</span></code> method if they are set manually after initialization.</li>
<li>If property <em>maintain_ratio</em> is set to TRUE, <code class="docutils literal"><span class="pre">image_reproportion()</span></code> now doesn’t need both width and height to be specified.</li>
<li>Property <em>maintain_ratio</em> is now taken into account when resizing images using ImageMagick library.</li>
<li>Added support for maintaining transparency for PNG images when watermarking.</li>
<li>Added a <strong>file_permissions</strong> setting.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> changes include:<ul>
<li>Added method <code class="docutils literal"><span class="pre">error_array()</span></code> to return all error messages as an array.</li>
<li>Added method <code class="docutils literal"><span class="pre">set_data()</span></code> to set an alternative data array to be validated instead of the default <code class="docutils literal"><span class="pre">$_POST</span></code>.</li>
<li>Added method <code class="docutils literal"><span class="pre">reset_validation()</span></code> which resets internal validation variables in case of multiple validation routines.</li>
<li>Added support for setting error delimiters in the config file via <code class="docutils literal"><span class="pre">$config['error_prefix']</span></code> and <code class="docutils literal"><span class="pre">$config['error_suffix']</span></code>.</li>
<li>Internal method <code class="docutils literal"><span class="pre">_execute()</span></code> now considers input data to be invalid if a specified rule is not found.</li>
<li>Removed method <code class="docutils literal"><span class="pre">is_numeric()</span></code> as it exists as a native PHP function and <code class="docutils literal"><span class="pre">_execute()</span></code> will find and use that (the <strong>is_numeric</strong> rule itself is deprecated since 1.6.1).</li>
<li>Native PHP functions used as rules can now accept an additional parameter, other than the data itself.</li>
<li>Updated method <code class="docutils literal"><span class="pre">set_rules()</span></code> to accept an array of rules as well as a string.</li>
<li>Fields that have empty rules set no longer run through validation (and therefore are not considered erroneous).</li>
<li>Added rule <strong>differs</strong> to check if the value of a field differs from the value of another field.</li>
<li>Added rule <strong>valid_url</strong>.</li>
<li>Added rule <strong>in_list</strong> to check if the value of a field is within a given list.</li>
<li>Added support for named parameters in error messages.</li>
<li><a class="reference internal" href="libraries/language.html"><span class="doc">Language</span></a> line keys must now be prefixed with <strong>form_validation_</strong>.</li>
<li>Added rule <strong>alpha_numeric_spaces</strong>.</li>
<li>Added support for custom error messages per field rule.</li>
<li>Added support for callable rules when they are passed as an array.</li>
<li>Added support for non-ASCII domains in <strong>valid_email</strong> rule, depending on the Intl extension.</li>
<li>Changed the debug message about an error message not being set to include the rule name it is about.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/caching.html"><span class="doc">Caching Library</span></a> changes include:<ul>
<li>Added Wincache driver.</li>
<li>Added Redis driver.</li>
<li>Added a <em>key_prefix</em> option for cache IDs.</li>
<li>Updated driver <code class="docutils literal"><span class="pre">is_supported()</span></code> methods to log at the “debug” level.</li>
<li>Added option to store raw values instead of CI-formatted ones (APC, Memcache).</li>
<li>Added atomic increment/decrement feature via <code class="docutils literal"><span class="pre">increment()</span></code>, <code class="docutils literal"><span class="pre">decrement()</span></code>.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/email.html"><span class="doc">E-mail Library</span></a> changes include:<ul>
<li>Added a custom filename parameter to <code class="docutils literal"><span class="pre">attach()</span></code> as <code class="docutils literal"><span class="pre">$this-&gt;email-&gt;attach($filename,</span> <span class="pre">$disposition,</span> <span class="pre">$newname)</span></code>.</li>
<li>Added possibility to send attachment as buffer string in <code class="docutils literal"><span class="pre">attach()</span></code> as <code class="docutils literal"><span class="pre">$this-&gt;email-&gt;attach($buffer,</span> <span class="pre">$disposition,</span> <span class="pre">$newname,</span> <span class="pre">$mime)</span></code>.</li>
<li>Added possibility to attach remote files by passing a URL.</li>
<li>Added method <code class="docutils literal"><span class="pre">attachment_cid()</span></code> to enable embedding inline attachments into HTML.</li>
<li>Added dsn (delivery status notification) option.</li>
<li>Renamed method <code class="docutils literal"><span class="pre">_set_header()</span></code> to <code class="docutils literal"><span class="pre">set_header()</span></code> and made it public to enable adding custom headers.</li>
<li>Successfully sent emails will automatically clear the parameters.</li>
<li>Added a <em>return_path</em> parameter to the <code class="docutils literal"><span class="pre">from()</span></code> method.</li>
<li>Removed the second parameter (character limit) from internal method <code class="docutils literal"><span class="pre">_prep_quoted_printable()</span></code> as it is never used.</li>
<li>Internal method <code class="docutils literal"><span class="pre">_prep_quoted_printable()</span></code> will now utilize the native <code class="docutils literal"><span class="pre">quoted_printable_encode()</span></code>, <code class="docutils literal"><span class="pre">imap_8bit()</span></code> functions (if available) when CRLF is set to “rn”.</li>
<li>Default charset now relies on the global <code class="docutils literal"><span class="pre">$config['charset']</span></code> setting.</li>
<li>Removed unused protected method <code class="docutils literal"><span class="pre">_get_ip()</span></code> (<a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a>’s <code class="docutils literal"><span class="pre">ip_address()</span></code> should be used anyway).</li>
<li>Internal method <code class="docutils literal"><span class="pre">_prep_q_encoding()</span></code> now utilizes PHP’s <em>mbstring</em> and <em>iconv</em> extensions (when available) and no longer has a second (<code class="docutils literal"><span class="pre">$from</span></code>) argument.</li>
<li>Added an optional parameter to <code class="docutils literal"><span class="pre">print_debugger()</span></code> to allow specifying which parts of the message should be printed (‘headers’, ‘subject’, ‘body’).</li>
<li>Added SMTP keepalive option to avoid opening the connection for each <code class="docutils literal"><span class="pre">send()</span></code> call. Accessible as <code class="docutils literal"><span class="pre">$smtp_keepalive</span></code>.</li>
<li>Public method <code class="docutils literal"><span class="pre">set_header()</span></code> now filters the input by removing all “\r” and “\n” characters.</li>
<li>Added support for non-ASCII domains in <code class="docutils literal"><span class="pre">valid_email()</span></code>, depending on the Intl extension.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> changes include:<ul>
<li>Deprecated usage of the “anchor_class” setting (use the new “attributes” setting instead).</li>
<li>Added method chaining support to <code class="docutils literal"><span class="pre">initialize()</span></code> method.</li>
<li>Added support for the anchor “rel” attribute.</li>
<li>Added support for setting custom attributes.</li>
<li>Added support for language translations of the <em>first_link</em>, <em>next_link</em>, <em>prev_link</em> and <em>last_link</em> values.</li>
<li>Added support for <code class="docutils literal"><span class="pre">$config['num_links']</span> <span class="pre">=</span> <span class="pre">0</span></code> configuration.</li>
<li>Added <code class="docutils literal"><span class="pre">$config['reuse_query_string']</span></code> to allow automatic repopulation of query string arguments, combined with normal URI segments.</li>
<li>Added <code class="docutils literal"><span class="pre">$config['use_global_url_suffix']</span></code> to allow overriding the library ‘suffix’ value with that of the global <code class="docutils literal"><span class="pre">$config['url_suffix']</span></code> setting.</li>
<li>Removed the default <code class="docutils literal"><span class="pre">&amp;nbsp;</span></code> from a number of the configuration variables.</li>
</ul>
</li>
<li><a class="reference internal" href="general/profiling.html"><span class="doc">Profiler Library</span></a> changes include:<ul>
<li>Database object names are now being displayed.</li>
<li>The sum of all queries running times in seconds is now being displayed.</li>
<li>Added support for displaying the HTTP DNT (“Do Not Track”) header.</li>
<li>Added support for displaying <code class="docutils literal"><span class="pre">$_FILES</span></code>.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> changes include:<ul>
<li>Added support for timestamp-based migrations (enabled by default).</li>
<li>Added <code class="docutils literal"><span class="pre">$config['migration_type']</span></code> to allow switching between <em>sequential</em> and <em>timestamp</em> migrations.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> changes include:<ul>
<li>Added the ability to use a proxy.</li>
<li>Added Basic HTTP authentication support.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> changes include:<ul>
<li>Added check to detect if robots are pretending to be mobile clients (helps with e.g. Google indexing mobile website versions).</li>
<li>Added method <code class="docutils literal"><span class="pre">parse()</span></code> to allow parsing a custom user-agent string, different from the current visitor’s.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/table.html"><span class="doc">HTML Table Library</span></a> changes include:<ul>
<li>Added method chaining support.</li>
<li>Added support for setting table class defaults in a config file.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Library</span></a> changes include:<ul>
<li>Method <code class="docutils literal"><span class="pre">read_file()</span></code> can now also alter the original file path/name while adding files to an archive.</li>
<li>Added support for changing the compression level.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/trackback.html"><span class="doc">Trackback Library</span></a> method <code class="docutils literal"><span class="pre">receive()</span></code> will now utilize <code class="docutils literal"><span class="pre">iconv()</span></code> if it is available but <code class="docutils literal"><span class="pre">mb_convert_encoding()</span></code> is not.</li>
</ul>
</li>
<li>Core<ul>
<li><a class="reference internal" href="general/routing.html"><span class="doc">Routing</span></a> changes include:<ul>
<li>Added support for multiple levels of controller directories.</li>
<li>Added support for per-directory <em>default_controller</em> and <em>404_override</em> classes.</li>
<li>Added possibility to route requests using HTTP verbs.</li>
<li>Added possibility to route requests using callbacks.</li>
<li>Added a new reserved route (<em>translate_uri_dashes</em>) to allow usage of dashes in the controller and method URI segments.</li>
<li>Deprecated methods <code class="docutils literal"><span class="pre">fetch_directory()</span></code>, <code class="docutils literal"><span class="pre">fetch_class()</span></code> and <code class="docutils literal"><span class="pre">fetch_method()</span></code> in favor of their respective public properties.</li>
<li>Removed method <code class="docutils literal"><span class="pre">_set_overrides()</span></code> and moved its logic to the class constructor.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/uri.html"><span class="doc">URI Library</span></a> changes include:<ul>
<li>Added conditional PCRE UTF-8 support to the “invalid URI characters” check and removed the <code class="docutils literal"><span class="pre">preg_quote()</span></code> call from it to allow more flexibility.</li>
<li>Renamed method <code class="docutils literal"><span class="pre">_filter_uri()</span></code> to <code class="docutils literal"><span class="pre">filter_uri()</span></code>.</li>
<li>Changed method <code class="docutils literal"><span class="pre">filter_uri()</span></code> to accept by reference and removed its return value.</li>
<li>Changed private methods to protected so that MY_URI can override them.</li>
<li>Renamed internal method <code class="docutils literal"><span class="pre">_parse_cli_args()</span></code> to <code class="docutils literal"><span class="pre">_parse_argv()</span></code>.</li>
<li>Renamed internal method <code class="docutils literal"><span class="pre">_detect_uri()</span></code> to <code class="docutils literal"><span class="pre">_parse_request_uri()</span></code>.</li>
<li>Changed <code class="docutils literal"><span class="pre">_parse_request_uri()</span></code> to accept absolute URIs for compatibility with HTTP/1.1 as per <cite>RFC2616 &lt;http://www.ietf.org/rfc/rfc2616.txt&gt;</cite>.</li>
<li>Added protected method <code class="docutils literal"><span class="pre">_parse_query_string()</span></code> to URI paths in the the <strong>QUERY_STRING</strong> value, like <code class="docutils literal"><span class="pre">_parse_request_uri()</span></code> does.</li>
<li>Changed URI string detection logic to always default to <strong>REQUEST_URI</strong> unless configured otherwise or under CLI.</li>
<li>Removed methods <code class="docutils literal"><span class="pre">_remove_url_suffix()</span></code>, <code class="docutils literal"><span class="pre">_explode_segments()</span></code> and moved their logic into <code class="docutils literal"><span class="pre">_set_uri_string()</span></code>.</li>
<li>Removed method <code class="docutils literal"><span class="pre">_fetch_uri_string()</span></code> and moved its logic into the class constructor.</li>
<li>Removed method <code class="docutils literal"><span class="pre">_reindex_segments()</span></code>.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> changes include:<ul>
<li>Added method chaining support.</li>
<li>Added method <code class="docutils literal"><span class="pre">get_vars()</span></code> to the Loader to retrieve all variables loaded with <code class="docutils literal"><span class="pre">$this-&gt;load-&gt;vars()</span></code>.</li>
<li><code class="docutils literal"><span class="pre">_ci_autoloader()</span></code> is now a protected method.</li>
<li>Added autoloading of drivers with <code class="docutils literal"><span class="pre">$autoload['drivers']</span></code>.</li>
<li><code class="docutils literal"><span class="pre">$config['rewrite_short_tags']</span></code> now has no effect when using PHP 5.4 as <code class="docutils literal"><span class="pre">&lt;?=</span></code> will always be available.</li>
<li>Changed method <code class="docutils literal"><span class="pre">config()</span></code> to return whatever <code class="docutils literal"><span class="pre">CI_Config::load()</span></code> returns instead of always being void.</li>
<li>Added support for library and model aliasing on autoload.</li>
<li>Changed method <code class="docutils literal"><span class="pre">is_loaded()</span></code> to ask for the (case sensitive) library name instead of its instance name.</li>
<li>Removed <code class="docutils literal"><span class="pre">$_base_classes</span></code> property and unified all class data in <code class="docutils literal"><span class="pre">$_ci_classes</span></code> instead.</li>
<li>Added method <code class="docutils literal"><span class="pre">clear_vars()</span></code> to allow clearing the cached variables for views.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> changes include:<ul>
<li>Deprecated the <code class="docutils literal"><span class="pre">$config['global_xss_filtering']</span></code> setting.</li>
<li>Added <code class="docutils literal"><span class="pre">method()</span></code> to retrieve <code class="docutils literal"><span class="pre">$_SERVER['REQUEST_METHOD']</span></code>.</li>
<li>Added support for arrays and network addresses (e.g. ***********/24) for use with the <em>proxy_ips</em> setting.</li>
<li>Added method <code class="docutils literal"><span class="pre">input_stream()</span></code> to aid in using <strong>php://input</strong> stream data such as one passed via PUT, DELETE and PATCH requests.</li>
<li>Changed method <code class="docutils literal"><span class="pre">valid_ip()</span></code> to use PHP’s native <code class="docutils literal"><span class="pre">filter_var()</span></code> function.</li>
<li>Changed internal method <code class="docutils literal"><span class="pre">_sanitize_globals()</span></code> to skip enforcing reversal of <em>register_globals</em> in PHP 5.4+, where this functionality no longer exists.</li>
<li>Changed methods <code class="docutils literal"><span class="pre">get()</span></code>, <code class="docutils literal"><span class="pre">post()</span></code>, <code class="docutils literal"><span class="pre">get_post()</span></code>, <code class="docutils literal"><span class="pre">cookie()</span></code>, <code class="docutils literal"><span class="pre">server()</span></code>, <code class="docutils literal"><span class="pre">user_agent()</span></code> to return NULL instead of FALSE when no value is found.</li>
<li>Changed default value of the <code class="docutils literal"><span class="pre">$xss_clean</span></code> parameter to NULL for all methods that utilize it, the default value is now determined by the <code class="docutils literal"><span class="pre">$config['global_xss_filtering']</span></code> setting.</li>
<li>Added method <code class="docutils literal"><span class="pre">post_get()</span></code> and changed <code class="docutils literal"><span class="pre">get_post()</span></code> to search in GET data first. Both methods’ names now properly match their GET/POST data search priorities.</li>
<li>Changed method <code class="docutils literal"><span class="pre">_fetch_from_array()</span></code> to parse array notation in field name.</li>
<li>Changed method <code class="docutils literal"><span class="pre">_fetch_from_array()</span></code> to allow retrieving multiple fields at once.</li>
<li>Added an option for <code class="docutils literal"><span class="pre">_clean_input_keys()</span></code> to return FALSE instead of terminating the whole script.</li>
<li>Deprecated the <code class="docutils literal"><span class="pre">is_cli_request()</span></code> method, it is now an alias for the new <a class="reference internal" href="general/common_functions.html#is_cli" title="is_cli"><code class="xref php php-func docutils literal"><span class="pre">is_cli()</span></code></a> common function.</li>
<li>Added an <code class="docutils literal"><span class="pre">$xss_clean</span></code> parameter to method <code class="docutils literal"><span class="pre">user_agent()</span></code> and removed the <code class="docutils literal"><span class="pre">$user_agent</span></code> property.</li>
<li>Added property <code class="docutils literal"><span class="pre">$raw_input_stream</span></code> to access <strong>php://input</strong> data.</li>
</ul>
</li>
<li><a class="reference internal" href="general/common_functions.html"><span class="doc">Common functions</span></a> changes include:<ul>
<li>Added function <a class="reference internal" href="general/common_functions.html#get_mimes" title="get_mimes"><code class="xref php php-func docutils literal"><span class="pre">get_mimes()</span></code></a> to return the <em>application/config/mimes.php</em> array.</li>
<li>Added support for HTTP code 303 (“See Other”) in <a class="reference internal" href="general/common_functions.html#set_status_header" title="set_status_header"><code class="xref php php-func docutils literal"><span class="pre">set_status_header()</span></code></a>.</li>
<li>Removed redundant conditional to determine HTTP server protocol in <a class="reference internal" href="general/common_functions.html#set_status_header" title="set_status_header"><code class="xref php php-func docutils literal"><span class="pre">set_status_header()</span></code></a>.</li>
<li>Renamed <code class="docutils literal"><span class="pre">_exception_handler()</span></code> to <code class="docutils literal"><span class="pre">_error_handler()</span></code> and replaced it with a real exception handler.</li>
<li>Changed <code class="docutils literal"><span class="pre">_error_handler()</span></code> to respect php.ini <em>display_errors</em> setting.</li>
<li>Added function <a class="reference internal" href="general/common_functions.html#is_https" title="is_https"><code class="xref php php-func docutils literal"><span class="pre">is_https()</span></code></a> to check if a secure connection is used.</li>
<li>Added function <a class="reference internal" href="general/common_functions.html#is_cli" title="is_cli"><code class="xref php php-func docutils literal"><span class="pre">is_cli()</span></code></a> to replace the <code class="docutils literal"><span class="pre">CI_Input::is_cli_request()</span></code> method.</li>
<li>Added function <a class="reference internal" href="general/common_functions.html#function_usable" title="function_usable"><code class="xref php php-func docutils literal"><span class="pre">function_usable()</span></code></a> to work around a bug in <cite>Suhosin &lt;http://www.hardened-php.net/suhosin/&gt;</cite>.</li>
<li>Removed the third (<cite>$php_error</cite>) argument from function <a class="reference internal" href="general/errors.html#log_message" title="log_message"><code class="xref php php-func docutils literal"><span class="pre">log_message()</span></code></a>.</li>
<li>Changed internal function <code class="docutils literal"><span class="pre">load_class()</span></code> to accept a constructor parameter instead of (previously unused) class name prefix.</li>
<li>Removed default parameter value of <a class="reference internal" href="general/common_functions.html#is_php" title="is_php"><code class="xref php php-func docutils literal"><span class="pre">is_php()</span></code></a>.</li>
<li>Added a second argument <code class="docutils literal"><span class="pre">$double_encode</span></code> to <a class="reference internal" href="general/common_functions.html#html_escape" title="html_escape"><code class="xref php php-func docutils literal"><span class="pre">html_escape()</span></code></a>.</li>
<li>Changed function <a class="reference internal" href="general/common_functions.html#config_item" title="config_item"><code class="xref php php-func docutils literal"><span class="pre">config_item()</span></code></a> to return NULL instead of FALSE when no value is found.</li>
<li>Changed function <a class="reference internal" href="general/common_functions.html#set_status_header" title="set_status_header"><code class="xref php php-func docutils literal"><span class="pre">set_status_header()</span></code></a> to return immediately when run under CLI.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a> changes include:<ul>
<li>Added a second argument to method <code class="docutils literal"><span class="pre">set_content_type()</span></code> that allows setting the document charset as well.</li>
<li>Added methods <code class="docutils literal"><span class="pre">get_content_type()</span></code> and <code class="docutils literal"><span class="pre">get_header()</span></code>.</li>
<li>Added method <code class="docutils literal"><span class="pre">delete_cache()</span></code>.</li>
<li>Added configuration option <code class="docutils literal"><span class="pre">$config['cache_query_string']</span></code> to enable taking the query string into account when caching.</li>
<li>Changed caching behavior to compress the output before storing it, if <code class="docutils literal"><span class="pre">$config['compress_output']</span></code> is enabled.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> changes include:<ul>
<li>Changed <code class="docutils literal"><span class="pre">site_url()</span></code> method  to accept an array as well.</li>
<li>Removed internal method <code class="docutils literal"><span class="pre">_assign_to_config()</span></code> and moved its implementation to <em>CodeIgniter.php</em> instead.</li>
<li><code class="docutils literal"><span class="pre">item()</span></code> now returns NULL instead of FALSE when the required config item doesn’t exist.</li>
<li>Added an optional second parameter to both <code class="docutils literal"><span class="pre">base_url()</span></code> and <code class="docutils literal"><span class="pre">site_url()</span></code> that allows enforcing of a protocol different than the one in the <em>base_url</em> configuration setting.</li>
<li>Added HTTP “Host” header character validation to prevent cache poisoning attacks when <code class="docutils literal"><span class="pre">base_url</span></code> auto-detection is used.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> changes include:<ul>
<li>Added <code class="docutils literal"><span class="pre">$config['csrf_regeneration']</span></code>, which makes CSRF token regeneration optional.</li>
<li>Added <code class="docutils literal"><span class="pre">$config['csrf_exclude_uris']</span></code>, allowing for exclusion of URIs from the CSRF protection (regular expressions are supported).</li>
<li>Added method <code class="docutils literal"><span class="pre">strip_image_tags()</span></code>.</li>
<li>Added method <code class="docutils literal"><span class="pre">get_random_bytes()</span></code> and switched CSRF &amp; XSS token generation to use it.</li>
<li>Modified method <code class="docutils literal"><span class="pre">sanitize_filename()</span></code> to read a public <code class="docutils literal"><span class="pre">$filename_bad_chars</span></code> property for getting the invalid characters list.</li>
<li>Return status code of 403 instead of a 500 if CSRF protection is enabled but a token is missing from a request.</li>
</ul>
</li>
<li><a class="reference internal" href="libraries/language.html"><span class="doc">Language Library</span></a> changes include:<ul>
<li>Changed method <code class="docutils literal"><span class="pre">load()</span></code> to filter the language name with <code class="docutils literal"><span class="pre">ctype_alpha()</span></code>.</li>
<li>Changed method <code class="docutils literal"><span class="pre">load()</span></code> to also accept an array of language files.</li>
<li>Added an optional second parameter to method <code class="docutils literal"><span class="pre">line()</span></code> to disable error logging for line keys that were not found.</li>
<li>Language files are now loaded in a cascading style with the one in <strong>system/</strong> always loaded and overridden afterwards, if another one is found.</li>
</ul>
</li>
<li><a class="reference internal" href="general/hooks.html"><span class="doc">Hooks Library</span></a> changes include:<ul>
<li>Added support for closure hooks (or anything that <code class="docutils literal"><span class="pre">is_callable()</span></code> returns TRUE for).</li>
<li>Renamed method <code class="docutils literal"><span class="pre">_call_hook()</span></code> to <code class="docutils literal"><span class="pre">call_hook()</span></code>.</li>
<li>Class instances are now stored in order to maintain their state.</li>
</ul>
</li>
<li>UTF-8 Library changes include:<ul>
<li><code class="docutils literal"><span class="pre">UTF8_ENABLED</span></code> now requires only one of <a class="reference external" href="http://php.net/mbstring">Multibyte String</a> or <a class="reference external" href="http://php.net/iconv">iconv</a> to be available instead of both.</li>
<li>Changed method <code class="docutils literal"><span class="pre">clean_string()</span></code> to utilize <code class="docutils literal"><span class="pre">mb_convert_encoding()</span></code> if it is available.</li>
<li>Renamed method <code class="docutils literal"><span class="pre">_is_ascii()</span></code> to <code class="docutils literal"><span class="pre">is_ascii()</span></code> and made it public.</li>
</ul>
</li>
<li>Log Library changes include:<ul>
<li>Added a <code class="docutils literal"><span class="pre">$config['log_file_permissions']</span></code> setting.</li>
<li>Changed the library constructor to try to create the <strong>log_path</strong> directory if it doesn’t exist.</li>
<li>Added support for microseconds (“u” date format character) in <code class="docutils literal"><span class="pre">$config['log_date_format']</span></code>.</li>
</ul>
</li>
<li>Added <a class="reference internal" href="general/compatibility_functions.html"><span class="doc">compatibility layers</span></a> for:<ul>
<li><a class="reference external" href="http://php.net/mbstring">Multibyte String</a> (limited support).</li>
<li><a class="reference external" href="http://php.net/hash">Hash</a> (<code class="docutils literal"><span class="pre">hash_equals()</span></code>, <code class="docutils literal"><span class="pre">hash_pbkdf2()</span></code>).</li>
<li><a class="reference external" href="http://php.net/password">Password Hashing</a>.</li>
<li><cite>Standard Functions ``array_column()`</cite>, <code class="docutils literal"><span class="pre">array_replace()</span></code>, <code class="docutils literal"><span class="pre">array_replace_recursive()</span></code>, <code class="docutils literal"><span class="pre">hex2bin()</span></code>, <code class="docutils literal"><span class="pre">quoted_printable_encode()</span></code>.</li>
</ul>
</li>
<li>Removed <code class="docutils literal"><span class="pre">CI_CORE</span></code> boolean constant from <em>CodeIgniter.php</em> (no longer Reactor and Core versions).</li>
<li>Added support for HTTP-Only cookies with new config option <em>cookie_httponly</em> (default FALSE).</li>
<li><code class="docutils literal"><span class="pre">$config['time_reference']</span></code> now supports all timezone strings supported by PHP.</li>
<li>Fatal PHP errors are now also passed to <code class="docutils literal"><span class="pre">_error_handler()</span></code>, so they can be logged.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-3-0">
<h3>Bug fixes for 3.0<a class="headerlink" href="#bug-fixes-for-3-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug where <code class="docutils literal"><span class="pre">unlink()</span></code> raised an error if cache file did not exist when you try to delete it.</li>
<li>Fixed a bug (#181) - a typo in the form validation language file.</li>
<li>Fixed a bug (#159, #163) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> nested transactions didn’t work properly due to <code class="docutils literal"><span class="pre">$_trans_depth</span></code> not being incremented.</li>
<li>Fixed a bug (#737, #75) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination</span></a> anchor class was not set properly when using initialize method.</li>
<li>Fixed a bug (#419) - <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> <a class="reference internal" href="helpers/url_helper.html#auto_link" title="auto_link"><code class="xref php php-func docutils literal"><span class="pre">auto_link()</span></code></a> didn’t recognize URLs that come after a word boundary.</li>
<li>Fixed a bug (#724) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>is_unique</strong> didn’t check if a database connection exists.</li>
<li>Fixed a bug (#647) - <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Library</span></a> internal method <code class="docutils literal"><span class="pre">_get_mod_time()</span></code> didn’t suppress possible “stat failed” errors generated by <code class="docutils literal"><span class="pre">filemtime()</span></code>.</li>
<li>Fixed a bug (#157, #174) - <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <code class="docutils literal"><span class="pre">clear()</span></code> didn’t completely clear properties.</li>
<li>Fixed a bug where <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> with PostgreSQL database could lead to fetching the whole table.</li>
<li>Fixed a bug (#795) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> <a class="reference internal" href="helpers/form_helper.html#form_open" title="form_open"><code class="xref php php-func docutils literal"><span class="pre">form_open()</span></code></a> didn’t add the default form <em>method</em> and <em>accept-charset</em> when an empty array is passed to it.</li>
<li>Fixed a bug (#797) - <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> <a class="reference internal" href="helpers/date_helper.html#timespan" title="timespan"><code class="xref php php-func docutils literal"><span class="pre">timespan()</span></code></a> was using incorrect seconds for year and month.</li>
<li>Fixed a bug in <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart Library</span></a> method <code class="docutils literal"><span class="pre">contents()</span></code> where if called without a TRUE (or equal) parameter, it would fail due to a typo.</li>
<li>Fixed a bug (#406) - SQLSRV DB driver not returning resource on <code class="docutils literal"><span class="pre">db_pconnect()</span></code>.</li>
<li>Fixed a bug in <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <code class="docutils literal"><span class="pre">gd_loaded()</span></code> where it was possible for the script execution to end or a PHP E_WARNING message to be emitted.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination library</span></a> where when use_page_numbers=TRUE previous link and page 1 link did not have the same url.</li>
<li>Fixed a bug (#561) - errors in <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> were not properly escaped.</li>
<li>Fixed a bug (#904) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">initialize()</span></code> caused a PHP Fatal error to be triggered if error level E_STRICT is used.</li>
<li>Fixed a hosting edge case where an empty <code class="docutils literal"><span class="pre">$_SERVER['HTTPS']</span></code> variable would evaluate to ‘on’.</li>
<li>Fixed a bug (#154) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> method <code class="docutils literal"><span class="pre">sess_update()</span></code> caused the session to be destroyed on pages where multiple AJAX requests were executed at once.</li>
<li>Fixed a possible bug in <a class="reference internal" href="libraries/input.html"><span class="doc">Input Libary</span></a> method <code class="docutils literal"><span class="pre">is_ajax_request()</span></code> where some clients might not send the X-Requested-With HTTP header value exactly as ‘XmlHttpRequest’.</li>
<li>Fixed a bug (#1039) - <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a> internal method <code class="docutils literal"><span class="pre">_backup()</span></code> method failed for the ‘mysql’ driver due to a table name not being escaped.</li>
<li>Fixed a bug (#1070) - <code class="docutils literal"><span class="pre">CI_DB_driver::initialize()</span></code> didn’t set a character set if a database is not selected.</li>
<li>Fixed a bug (#177) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">set_value()</span></code> didn’t set the default value if POST data is NULL.</li>
<li>Fixed a bug (#68, #414) - :Oracle’s <code class="docutils literal"><span class="pre">escape_str()</span></code> didn’t properly escape LIKE wild characters.</li>
<li>Fixed a bug (#81) - ODBC’s <code class="docutils literal"><span class="pre">list_fields()</span></code> and <code class="docutils literal"><span class="pre">field_data()</span></code> methods skipped the first column due to <code class="docutils literal"><span class="pre">odbc_field_*()</span></code> functions’ index starting at 1 instead of 0.</li>
<li>Fixed a bug (#129) - ODBC’s <code class="docutils literal"><span class="pre">num_rows()</span></code> method returned -1 in some cases, due to not all subdrivers supporting the <code class="docutils literal"><span class="pre">odbc_num_rows()</span></code> function.</li>
<li>Fixed a bug (#153) - E_NOTICE being generated by <code class="docutils literal"><span class="pre">getimagesize()</span></code> in the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a>.</li>
<li>Fixed a bug (#611) - SQLSRV’s error handling methods used to issue warnings when there’s no actual error.</li>
<li>Fixed a bug (#1036) - <code class="docutils literal"><span class="pre">is_write_type()</span></code> method in the <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> didn’t return TRUE for RENAME queries.</li>
<li>Fixed a bug in PDO’s <code class="docutils literal"><span class="pre">_version()</span></code> method where it used to return the client version as opposed to the server one.</li>
<li>Fixed a bug in PDO’s <code class="docutils literal"><span class="pre">insert_id()</span></code> method where it could’ve failed if it’s used with Postgre versions prior to 8.1.</li>
<li>Fixed a bug in CUBRID’s <code class="docutils literal"><span class="pre">affected_rows()</span></code> method where a connection resource was passed to <code class="docutils literal"><span class="pre">cubrid_affected_rows()</span></code> instead of a result.</li>
<li>Fixed a bug (#638) - <code class="docutils literal"><span class="pre">db_set_charset()</span></code> ignored its arguments and always used the configured charset instead.</li>
<li>Fixed a bug (#413) - Oracle’s error handling methods used to only return connection-related errors.</li>
<li>Fixed a bug (#1101) - <a class="reference internal" href="database/results.html"><span class="doc">Database Result</span></a> method <code class="docutils literal"><span class="pre">field_data()</span></code> for ‘mysql’, ‘mysqli’ drivers was implemented as if it was handling a DESCRIBE result instead of the actual result set.</li>
<li>Fixed a bug in Oracle’s <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">_create_table()</span></code> where it failed with AUTO_INCREMENT as it’s not supported.</li>
<li>Fixed a bug (#1080) - when using the SMTP protocol, <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> method <code class="docutils literal"><span class="pre">send()</span></code> was returning TRUE even if the connection/authentication against the server failed.</li>
<li>Fixed a bug (#306) - ODBC’s <code class="docutils literal"><span class="pre">insert_id()</span></code> method was calling non-existent function <code class="docutils literal"><span class="pre">odbc_insert_id()</span></code>, which resulted in a fatal error.</li>
<li>Fixed a bug in Oracle’s <a class="reference internal" href="database/results.html"><span class="doc">Database Result</span></a> implementation where the cursor ID passed to it was always NULL.</li>
<li>Fixed a bug (#64) - Regular expression in <em>DB_query_builder.php</em> failed to handle queries containing SQL bracket delimiters in the JOIN condition.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> where a PHP E_NOTICE error was triggered by <code class="docutils literal"><span class="pre">_unserialize()</span></code> due to results from databases such as MSSQL and Oracle being space-padded on the right.</li>
<li>Fixed a bug (#501) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">set_rules()</span></code> depended on <code class="docutils literal"><span class="pre">count($_POST)</span></code> instead of actually checking if the request method ‘POST’ before aborting.</li>
<li>Fixed a bug (#136) - PostgreSQL and MySQL’s <code class="docutils literal"><span class="pre">escape_str()</span></code> method didn’t properly escape LIKE wild characters.</li>
<li>Fixed a bug in <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">library()</span></code> where some PHP versions wouldn’t execute the class constructor.</li>
<li>Fixed a bug (#88) - An unexisting property was used for configuration of the Memcache cache driver.</li>
<li>Fixed a bug (#14) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_database()</span></code> didn’t utilize the configured database character set.</li>
<li>Fixed a bug (#23, #1238) - <a class="reference internal" href="database/caching.html"><span class="doc">Database Caching</span></a> method <code class="docutils literal"><span class="pre">delete_all()</span></code> used to delete .htaccess and index.html files, which is a potential security risk.</li>
<li>Fixed a bug in <a class="reference internal" href="libraries/trackback.html"><span class="doc">Trackback Library</span></a> method <code class="docutils literal"><span class="pre">validate_url()</span></code> where it didn’t actually do anything, due to input not being passed by reference.</li>
<li>Fixed a bug (#11, #183, #863) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">_execute()</span></code> silently continued to the next rule, if a rule method/function is not found.</li>
<li>Fixed a bug (#122) - routed URI string was being reported incorrectly in sub-directories.</li>
<li>Fixed a bug (#1241) - <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Library</span></a> method <code class="docutils literal"><span class="pre">read_dir()</span></code> wasn’t compatible with Windows.</li>
<li>Fixed a bug (#306) - ODBC driver didn’t have an <code class="docutils literal"><span class="pre">_insert_batch()</span></code> method, which resulted in fatal error being triggered when <code class="docutils literal"><span class="pre">insert_batch()</span></code> is used with it.</li>
<li>Fixed a bug in MSSQL and SQLSrv’s <code class="docutils literal"><span class="pre">_truncate()</span></code> where the TABLE keyword was missing.</li>
<li>Fixed a bug in PDO’s <code class="docutils literal"><span class="pre">trans_commit()</span></code> method where it failed due to an erroneous property name.</li>
<li>Fixed a bug (#798) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">update()</span></code> used to ignore LIKE conditions that were set with <code class="docutils literal"><span class="pre">like()</span></code>.</li>
<li>Fixed a bug in Oracle’s and MSSQL’s <code class="docutils literal"><span class="pre">delete()</span></code> methods where an erroneous SQL statement was generated when used with <code class="docutils literal"><span class="pre">limit()</span></code>.</li>
<li>Fixed a bug in SQLSRV’s <code class="docutils literal"><span class="pre">delete()</span></code> method where <code class="docutils literal"><span class="pre">like()</span></code> and <code class="docutils literal"><span class="pre">limit()</span></code> conditions were ignored.</li>
<li>Fixed a bug (#1265) - Database connections were always closed, regardless of the ‘pconnect’ option value.</li>
<li>Fixed a bug (#128) - <a class="reference internal" href="libraries/language.html"><span class="doc">Language Library</span></a> did not correctly keep track of loaded language files.</li>
<li>Fixed a bug (#1349) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> method <code class="docutils literal"><span class="pre">get_extension()</span></code> returned the original filename when it didn’t have an actual extension.</li>
<li>Fixed a bug (#1273) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">set_update_batch()</span></code> generated an E_NOTICE message.</li>
<li>Fixed a bug (#44, #110) - <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> method <code class="docutils literal"><span class="pre">clean_file_name()</span></code> didn’t clear ‘!’ and ‘#’ characters.</li>
<li>Fixed a bug (#121) - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">row()</span></code> returned an array when there’s no actual result to be returned.</li>
<li>Fixed a bug (#319) - SQLSRV’s <code class="docutils literal"><span class="pre">affected_rows()</span></code> method failed due to a scrollable cursor being created for write-type queries.</li>
<li>Fixed a bug (#356) - <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> driver ‘postgre’ didn’t have an <code class="docutils literal"><span class="pre">_update_batch()</span></code> method, which resulted in fatal error being triggered when <code class="docutils literal"><span class="pre">update_batch()</span></code> is used with it.</li>
<li>Fixed a bug (#784, #862) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> failed on SQLSRV/MSSQL when used with ‘IF NOT EXISTS’.</li>
<li>Fixed a bug (#1419) - <a class="reference internal" href="general/creating_drivers.html"><span class="doc">Driver Library</span></a> had a static variable that was causing an error.</li>
<li>Fixed a bug (#1411) - the <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> used its own short list of MIMEs instead the one from <em>config/mimes.php</em>.</li>
<li>Fixed a bug where php.ini setting <em>magic_quotes_runtime</em> wasn’t turned off for PHP 5.3 (where it is indeed deprecated, but not non-existent).</li>
<li>Fixed a bug (#666) - <a class="reference internal" href="libraries/output.html"><span class="doc">Output Library</span></a> method <code class="docutils literal"><span class="pre">set_content_type()</span></code> didn’t set the document charset.</li>
<li>Fixed a bug (#784, #861) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> method <code class="docutils literal"><span class="pre">create_table()</span></code> used to accept constraints for MSSQL/SQLSRV integer-type columns.</li>
<li>Fixed a bug (#706) - SQLSRV/MSSSQL <a class="reference internal" href="database/index.html"><span class="doc">Database</span></a> drivers didn’t escape field names.</li>
<li>Fixed a bug (#1452) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> didn’t properly detect identifiers with spaces in their names.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> ignored its extra arguments when the value passed to it is an array.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> internal method <code class="docutils literal"><span class="pre">_has_operator()</span></code> didn’t detect BETWEEN.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">join()</span></code> failed with identifiers containing dashes.</li>
<li>Fixed a bug (#1264) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> and <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities</span></a> didn’t update/reset the databases and tables list cache when a table or a database is created, dropped or renamed.</li>
<li>Fixed a bug (#7) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">join()</span></code> only escaped one set of conditions.</li>
<li>Fixed a bug (#1321) - <code class="docutils literal"><span class="pre">CI_Exceptions</span></code> couldn’t find the <em>errors/</em> directory in some cases.</li>
<li>Fixed a bug (#1202) - <a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a> <code class="docutils literal"><span class="pre">encode_from_legacy()</span></code> didn’t set back the encrypt mode on failure.</li>
<li>Fixed a bug (#145) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> method <code class="docutils literal"><span class="pre">compile_binds()</span></code> failed when the bind marker was present in a literal string within the query.</li>
<li>Fixed a bug in <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">protect_identifiers()</span></code> where if passed along with the field names, operators got escaped as well.</li>
<li>Fixed a bug (#10) - <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Library</span></a> internal method <code class="docutils literal"><span class="pre">_detect_uri()</span></code> failed with paths containing a colon.</li>
<li>Fixed a bug (#1387) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">from()</span></code> didn’t escape table aliases.</li>
<li>Fixed a bug (#520) - <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> function :php:func:<code class="docutils literal"><span class="pre">nice_date()</span></code> failed when the optional second parameter is not passed.</li>
<li>Fixed a bug (#318) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiling Library</span></a> setting <em>query_toggle_count</em> was not settable as described in the manual.</li>
<li>Fixed a bug (#938) - <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> method <code class="docutils literal"><span class="pre">site_url()</span></code> added a question mark to the URL string when query strings are enabled even if it already existed.</li>
<li>Fixed a bug (#999) - <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> method <code class="docutils literal"><span class="pre">site_url()</span></code> always appended <code class="docutils literal"><span class="pre">$config['url_suffix']</span></code> to the end of the URL string, regardless of whether a query string exists in it.</li>
<li>Fixed a bug where <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> function <a class="reference internal" href="helpers/url_helper.html#anchor_popup" title="anchor_popup"><code class="xref php php-func docutils literal"><span class="pre">anchor_popup()</span></code></a> ignored the attributes argument if it is not an array.</li>
<li>Fixed a bug (#1328) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t properly check the type of the form fields before processing them.</li>
<li>Fixed a bug (#79) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t properly validate array fields that use associative keys or have custom indexes.</li>
<li>Fixed a bug (#427) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> method <code class="docutils literal"><span class="pre">strip_image_tags()</span></code> was an alias to a non-existent method.</li>
<li>Fixed a bug (#1545) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">limit()</span></code> wasn’t executed properly under Oracle.</li>
<li>Fixed a bug (#1551) - <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a> function <a class="reference internal" href="helpers/date_helper.html#standard_date" title="standard_date"><code class="xref php php-func docutils literal"><span class="pre">standard_date()</span></code></a> didn’t properly format <em>W3C</em> and <em>ATOM</em> standard dates.</li>
<li>Fixed a bug where <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">join()</span></code> escaped literal values as if they were fields.</li>
<li>Fixed a bug (#135) - PHP Error logging was impossible without the errors being displayed.</li>
<li>Fixed a bug (#1613) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> functions <a class="reference internal" href="helpers/form_helper.html#form_multiselect" title="form_multiselect"><code class="xref php php-func docutils literal"><span class="pre">form_multiselect()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#form_dropdown" title="form_dropdown"><code class="xref php php-func docutils literal"><span class="pre">form_dropdown()</span></code></a> didn’t properly handle empty array option groups.</li>
<li>Fixed a bug (#1605) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> produced incorrect <em>previous</em> and <em>next</em> link values.</li>
<li>Fixed a bug in SQLSRV’s <code class="docutils literal"><span class="pre">affected_rows()</span></code> method where an erroneous function name was used.</li>
<li>Fixed a bug (#1000) - Change syntax of <code class="docutils literal"><span class="pre">$view_file</span></code> to <code class="docutils literal"><span class="pre">$_ci_view_file</span></code> to prevent being overwritten by application.</li>
<li>Fixed a bug (#1757) - <a class="reference internal" href="helpers/directory_helper.html"><span class="doc">Directory Helper</span></a> function <a class="reference internal" href="helpers/directory_helper.html#directory_map" title="directory_map"><code class="xref php php-func docutils literal"><span class="pre">directory_map()</span></code></a> was skipping files and directories named ‘0’.</li>
<li>Fixed a bug (#1789) - <a class="reference internal" href="database/index.html"><span class="doc">Database Library</span></a> method <code class="docutils literal"><span class="pre">escape_str()</span></code> escaped quote characters in LIKE conditions twice under MySQL.</li>
<li>Fixed a bug (#395) - <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a> method <code class="docutils literal"><span class="pre">result()</span></code> didn’t properly check array result columns when called from <code class="docutils literal"><span class="pre">report()</span></code>.</li>
<li>Fixed a bug (#1692) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> method <code class="docutils literal"><span class="pre">display_error()</span></code> didn’t properly trace the possible error source on Windows systems.</li>
<li>Fixed a bug (#1745) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> method <code class="docutils literal"><span class="pre">is_write_type()</span></code> didn’t return TRUE for LOAD queries.</li>
<li>Fixed a bug (#1765) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> didn’t properly detect connection errors for the ‘mysqli’ driver.</li>
<li>Fixed a bug (#1257) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> used to (unnecessarily) group FROM clause contents, which breaks certain queries and is invalid for some databases.</li>
<li>Fixed a bug (#1709) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email</span></a> headers were broken when using long email subjects and rn as CRLF.</li>
<li>Fixed a bug where <code class="docutils literal"><span class="pre">MB_ENABLED</span></code> constant was only declared if <code class="docutils literal"><span class="pre">UTF8_ENABLED</span></code> was set to TRUE.</li>
<li>Fixed a bug where the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> accepted cookies with <em>last_activity</em> values being in the future.</li>
<li>Fixed a bug (#1897) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> triggered PHP E_WARNING errors when <em>mail</em> protocol used and <code class="docutils literal"><span class="pre">to()</span></code> is never called.</li>
<li>Fixed a bug (#1409) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t properly handle multibyte characters when applying Q-encoding to headers.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> ignored its <em>wordwrap</em> setting while handling alternative messages.</li>
<li>Fixed a bug (#1476, #1909) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> didn’t take into account actual routing when determining the current page.</li>
<li>Fixed a bug (#1766) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t always take into account the <em>dbprefix</em> setting.</li>
<li>Fixed a bug (#779) - <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Class</span></a> didn’t always trim slashes from the <em>uri_string</em> as shown in the documentation.</li>
<li>Fixed a bug (#134) - <a class="reference internal" href="database/caching.html"><span class="doc">Database Caching</span></a> method <code class="docutils literal"><span class="pre">delete_cache()</span></code> didn’t work in some cases due to <em>cachedir</em> not being initialized properly.</li>
<li>Fixed a bug (#191) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> ignored attempts for (re)loading databases to <code class="docutils literal"><span class="pre">get_instance()-&gt;db</span></code> even when the old database connection is dead.</li>
<li>Fixed a bug (#1255) - <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> method <code class="docutils literal"><span class="pre">is_referral()</span></code> only checked if <code class="docutils literal"><span class="pre">$_SERVER['HTTP_REFERER']</span></code> exists.</li>
<li>Fixed a bug (#1146) - <a class="reference internal" href="helpers/download_helper.html"><span class="doc">Download Helper</span></a> function <a class="reference internal" href="helpers/download_helper.html#force_download" title="force_download"><code class="xref php php-func docutils literal"><span class="pre">force_download()</span></code></a> incorrectly sent <em>Cache-Control</em> directives <em>pre-check</em> and <em>post-check</em> to Internet Explorer.</li>
<li>Fixed a bug (#1811) - <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Library</span></a> didn’t properly cache segments for <code class="docutils literal"><span class="pre">uri_to_assoc()</span></code> and <code class="docutils literal"><span class="pre">ruri_to_assoc()</span></code>.</li>
<li>Fixed a bug (#1506) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helpers</span></a> set empty <em>name</em> attributes.</li>
<li>Fixed a bug (#59) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> method <code class="docutils literal"><span class="pre">count_all_results()</span></code> ignored the DISTINCT clause.</li>
<li>Fixed a bug (#1624) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>matches</strong> didn’t property handle array field names.</li>
<li>Fixed a bug (#1630) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> function <a class="reference internal" href="helpers/form_helper.html#set_value" title="set_value"><code class="xref php php-func docutils literal"><span class="pre">set_value()</span></code></a> didn’t escape HTML entities.</li>
<li>Fixed a bug (#142) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> function <a class="reference internal" href="helpers/form_helper.html#form_dropdown" title="form_dropdown"><code class="xref php php-func docutils literal"><span class="pre">form_dropdown()</span></code></a> didn’t escape HTML entities in option values.</li>
<li>Fixed a bug (#50) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> unnecessarily stripped slashed from serialized data, making it impossible to read objects in a namespace.</li>
<li>Fixed a bug (#658) - <a class="reference internal" href="general/routing.html"><span class="doc">Routing</span></a> wildcard <strong>:any</strong> didn’t work as advertised and matched multiple URI segments instead of all characters within a single segment.</li>
<li>Fixed a bug (#1938) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> removed multiple spaces inside a pre-formatted plain text message.</li>
<li>Fixed a bug (#122) - <a class="reference internal" href="libraries/uri.html"><span class="doc">URI Library</span></a> method <code class="docutils literal"><span class="pre">ruri_string()</span></code> didn’t include a directory if one is used.</li>
<li>Fixed a bug - <a class="reference internal" href="general/routing.html"><span class="doc">Routing Library</span></a> didn’t properly handle <em>default_controller</em> in a subdirectory when a method is also specified.</li>
<li>Fixed a bug (#953) - <a class="reference internal" href="general/hooks.html"><span class="doc">post_controller_constructor hook</span></a> wasn’t called with a <em>404_override</em>.</li>
<li>Fixed a bug (#1220) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler Library</span></a> didn’t display information for database objects that are instantiated inside models.</li>
<li>Fixed a bug (#1978) - <a class="reference internal" href="helpers/directory_helper.html"><span class="doc">Directory Helper</span></a> function <a class="reference internal" href="helpers/directory_helper.html#directory_map" title="directory_map"><code class="xref php php-func docutils literal"><span class="pre">directory_map()</span></code></a>’s return array didn’t make a distinction between directories and file indexes when a directory with a numeric name is present.</li>
<li>Fixed a bug (#777) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> didn’t look for helper extensions in added package paths.</li>
<li>Fixed a bug (#18) - <a class="reference internal" href="libraries/caching.html"><span class="doc">APC Cache</span></a> driver didn’t (un)serialize data, resulting in failure to store objects.</li>
<li>Fixed a bug (#188) - <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a> filled up logs with error messages for non-existing language keys.</li>
<li>Fixed a bug (#113) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t properly handle empty fields that were specified as an array.</li>
<li>Fixed a bug (#2061) - <a class="reference internal" href="general/routing.html"><span class="doc">Routing Class</span></a> didn’t properly sanitize directory, controller and function triggers with <strong>enable_query_strings</strong> set to TRUE.</li>
<li>Fixed a bug - SQLSRV didn’t support <code class="docutils literal"><span class="pre">escape_like_str()</span></code> or escaping an array of values.</li>
<li>Fixed a bug - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">list_fields()</span></code> didn’t reset its field pointer for the ‘mysql’, ‘mysqli’ and ‘mssql’ drivers.</li>
<li>Fixed a bug (#2211) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> extensions couldn’t execute <code class="docutils literal"><span class="pre">CI_Migration::__construct()</span></code>.</li>
<li>Fixed a bug (#2255) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> didn’t apply <em>smtp_timeout</em> to socket reads and writes.</li>
<li>Fixed a bug (#2239) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> improperly handled the Subject when used with <em>bcc_batch_mode</em> resulting in E_WARNING messages and an empty Subject.</li>
<li>Fixed a bug (#2234) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t reset JOIN cache for write-type queries.</li>
<li>Fixed a bug (#2298) - <a class="reference internal" href="database/results.html"><span class="doc">Database Results</span></a> method <code class="docutils literal"><span class="pre">next_row()</span></code> kept returning the last row, allowing for infinite loops.</li>
<li>Fixed a bug (#2236, #2639) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> functions <a class="reference internal" href="helpers/form_helper.html#set_value" title="set_value"><code class="xref php php-func docutils literal"><span class="pre">set_value()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#set_select" title="set_select"><code class="xref php php-func docutils literal"><span class="pre">set_select()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#set_radio" title="set_radio"><code class="xref php php-func docutils literal"><span class="pre">set_radio()</span></code></a>, <a class="reference internal" href="helpers/form_helper.html#set_checkbox" title="set_checkbox"><code class="xref php php-func docutils literal"><span class="pre">set_checkbox()</span></code></a> didn’t parse array notation for keys if the rule was not present in the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a>.</li>
<li>Fixed a bug (#2353) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> erroneously prefixed literal strings with <strong>dbprefix</strong>.</li>
<li>Fixed a bug (#78) - <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart Library</span></a> didn’t allow non-English letters in product names.</li>
<li>Fixed a bug (#77) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> didn’t properly handle the transaction “test mode” flag.</li>
<li>Fixed a bug (#2380) - <a class="reference internal" href="general/routing.html"><span class="doc">URI Routing</span></a> method <code class="docutils literal"><span class="pre">fetch_method()</span></code> returned ‘index’ if the requested method name matches its controller name.</li>
<li>Fixed a bug (#2388) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> used to ignore attachment errors, resulting in broken emails being sent.</li>
<li>Fixed a bug (#2498) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> rule <strong>valid_base64</strong> only checked characters instead of actual validity.</li>
<li>Fixed a bug (#2425) - OCI8 <a class="reference internal" href="database/index.html"><span class="doc">database</span></a> driver method <code class="docutils literal"><span class="pre">stored_procedure()</span></code> didn’t log an error unless <strong>db_debug</strong> was set to TRUE.</li>
<li>Fixed a bug (#2490) - <a class="reference internal" href="database/queries.html"><span class="doc">Database Class</span></a> method <code class="docutils literal"><span class="pre">query()</span></code> returning boolean instead of a result object for PostgreSQL-specific <em>INSERT INTO … RETURNING</em> statements.</li>
<li>Fixed a bug (#249) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> didn’t properly handle Memcache(d) configurations with missing options.</li>
<li>Fixed a bug (#180) - <a class="reference internal" href="general/common_functions.html#config_item" title="config_item"><code class="xref php php-func docutils literal"><span class="pre">config_item()</span></code></a> didn’t take into account run-time configuration changes.</li>
<li>Fixed a bug (#2551) - <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader Library</span></a> method <code class="docutils literal"><span class="pre">library()</span></code> didn’t properly check if a class that is being loaded already exists.</li>
<li>Fixed a bug (#2560) - <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a> function <a class="reference internal" href="helpers/form_helper.html#form_open" title="form_open"><code class="xref php php-func docutils literal"><span class="pre">form_open()</span></code></a> set the ‘method=”post”’ attribute only if the passed attributes equaled an empty string.</li>
<li>Fixed a bug (#2585) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> methods <code class="docutils literal"><span class="pre">min()</span></code>, <code class="docutils literal"><span class="pre">max()</span></code>, <code class="docutils literal"><span class="pre">avg()</span></code>, <code class="docutils literal"><span class="pre">sum()</span></code> didn’t escape field names.</li>
<li>Fixed a bug (#2590) - <a class="reference internal" href="general/common_functions.html"><span class="doc">Common function</span></a> <a class="reference internal" href="general/errors.html#log_message" title="log_message"><code class="xref php php-func docutils literal"><span class="pre">log_message()</span></code></a> didn’t actually cache the <code class="docutils literal"><span class="pre">CI_Log</span></code> class instance.</li>
<li>Fixed a bug (#2609) - <a class="reference internal" href="general/common_functions.html"><span class="doc">Common function</span></a> <code class="xref php php-func docutils literal"><span class="pre">get_config()</span></code> optional argument was only effective on first function call. Also, it can now add items, in addition to updating existing items.</li>
<li>Fixed a bug in the ‘postgre’ <a class="reference internal" href="database/index.html"><span class="doc">database</span></a> driver where the connection ID wasn’t passed to <code class="docutils literal"><span class="pre">pg_escape_string()</span></code>.</li>
<li>Fixed a bug (#33) - Script execution was terminated when an invalid cookie key was encountered.</li>
<li>Fixed a bug (#2691) - nested <a class="reference internal" href="database/index.html"><span class="doc">database</span></a> transactions could end in a deadlock when an error is encountered with <em>db_debug</em> set to TRUE.</li>
<li>Fixed a bug (#2515) - <code class="docutils literal"><span class="pre">_exception_handler()</span></code> used to send the 200 “OK” HTTP status code and didn’t stop script exection even on fatal errors.</li>
<li>Fixed a bug - Redis <a class="reference internal" href="libraries/caching.html"><span class="doc">Caching</span></a> driver didn’t handle connection failures properly.</li>
<li>Fixed a bug (#2756) - <a class="reference internal" href="database/index.html"><span class="doc">Database Class</span></a> executed the MySQL-specific <cite>SET SESSION sql_mode</cite> query for all drivers when the ‘stricton’ option is set.</li>
<li>Fixed a bug (#2579) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> “no escape” functionality didn’t work properly with query cache.</li>
<li>Fixed a bug (#2237) - <a class="reference internal" href="libraries/parser.html"><span class="doc">Parser Library</span></a> failed if the same tag pair is used more than once within a template.</li>
<li>Fixed a bug (#2143) - <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a> didn’t check for rule groups named in a <em>controller/method</em> manner when trying to load from a config file.</li>
<li>Fixed a bug (#2762) - <a class="reference internal" href="general/hooks.html"><span class="doc">Hooks Class</span></a> didn’t properly check if the called class/function exists.</li>
<li>Fixed a bug (#148) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> internal method <code class="docutils literal"><span class="pre">_clean_input_data()</span></code> assumed that it data is URL-encoded, stripping certain character sequences from it.</li>
<li>Fixed a bug (#346) - with <code class="docutils literal"><span class="pre">$config['global_xss_filtering']</span></code> turned on, the <code class="docutils literal"><span class="pre">$_GET</span></code>, <code class="docutils literal"><span class="pre">$_POST</span></code>, <code class="docutils literal"><span class="pre">$_COOKIE</span></code> and <code class="docutils literal"><span class="pre">$_SERVER</span></code> superglobals were overwritten during initialization time, resulting in XSS filtering being either performed twice or there was no possible way to get the original data, even though options for this do exist.</li>
<li>Fixed an edge case (#555) - <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> reported an incorrect version Opera 10+ due to a non-standard user-agent string.</li>
<li>Fixed a bug (#133) - <a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text Helper</span></a> <a class="reference internal" href="helpers/text_helper.html#ascii_to_entities" title="ascii_to_entities"><code class="xref php php-func docutils literal"><span class="pre">ascii_to_entities()</span></code></a> stripped the last character if it happens to be in the extended ASCII group.</li>
<li>Fixed a bug (#2822) - <code class="docutils literal"><span class="pre">fwrite()</span></code> was used incorrectly throughout the whole framework, allowing incomplete writes when writing to a network stream and possibly a few other edge cases.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> methods <code class="docutils literal"><span class="pre">accept_charset()</span></code> and <code class="docutils literal"><span class="pre">accept_lang()</span></code> didn’t properly parse HTTP headers that contain spaces.</li>
<li>Fixed a bug where <em>default_controller</em> was called instad of triggering a 404 error if the current route is in a controller directory.</li>
<li>Fixed a bug (#2737) - <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XML-RPC Library</span></a> used objects as array keys, which triggered E_NOTICE messages.</li>
<li>Fixed a bug (#2771) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> didn’t take into account HTML5 entities.</li>
<li>Fixed a bug (#2856) - ODBC method <code class="docutils literal"><span class="pre">affected_rows()</span></code> passed an incorrect value to <code class="docutils literal"><span class="pre">odbc_num_rows()</span></code>.</li>
<li>Fixed a bug (#43) <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> method <code class="docutils literal"><span class="pre">text_watermark()</span></code> didn’t properly determine watermark placement.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/table.html"><span class="doc">HTML Table Library</span></a> ignored its <em>auto_heading</em> setting if headings were not already set.</li>
<li>Fixed a bug (#2364) - <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination Library</span></a> appended the query string (if used) multiple times when there are successive calls to <code class="docutils literal"><span class="pre">create_links()</span></code> with no <code class="docutils literal"><span class="pre">initialize()</span></code> in between them.</li>
<li>Partially fixed a bug (#261) - UTF-8 class method <code class="docutils literal"><span class="pre">clean_string()</span></code> generating log messages and/or not producing the desired result due to an upstream bug in iconv.</li>
<li>Fixed a bug where <code class="docutils literal"><span class="pre">CI_Xmlrpcs::parseRequest()</span></code> could fail if <code class="docutils literal"><span class="pre">$HTTP_RAW_POST_DATA</span></code> is not populated.</li>
<li>Fixed a bug in <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Library</span></a> internal method <code class="docutils literal"><span class="pre">_get_mod_time()</span></code> where it was not parsing result returned by <code class="docutils literal"><span class="pre">filemtime()</span></code>.</li>
<li>Fixed a bug (#3161) - <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache Library</span></a> methods <cite>increment()</cite>, <cite>decrement()</cite> didn’t auto-create non-existent items when using redis and/or file storage.</li>
<li>Fixed a bug (#3189) - <a class="reference internal" href="libraries/parser.html"><span class="doc">Parser Library</span></a> used double replacement on <code class="docutils literal"><span class="pre">key-&gt;value</span></code> pairs, exposing a potential template injection vulnerability.</li>
<li>Fixed a bug (#3573) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> violated <a class="reference external" href="https://tools.ietf.org/rfc/rfc5321.txt">RFC5321</a> by sending ‘localhost.localdomain’ as a hostname.</li>
<li>Fixed a bug (#3572) - <code class="docutils literal"><span class="pre">CI_Security::_remove_evil_attributes()</span></code> failed for large-sized inputs due to <em>pcre.backtrack_limit</em> and didn’t properly match HTML tags.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-2-3">
<h2>Version 2.2.3<a class="headerlink" href="#version-2-2-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: July 14, 2015</p>
<ul class="simple">
<li>Security<ul>
<li>Removed a fallback to <code class="docutils literal"><span class="pre">mysql_escape_string()</span></code> in the ‘mysql’ database driver (<code class="docutils literal"><span class="pre">escape_str()</span></code> method) when there’s no active database connection.</li>
</ul>
</li>
</ul>
</div>
<div class="section" id="version-2-2-2">
<h2>Version 2.2.2<a class="headerlink" href="#version-2-2-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 15, 2015</p>
<ul class="simple">
<li>General Changes<ul>
<li>Added HTTP “Host” header character validation to prevent cache poisoning attacks when <em>base_url</em> auto-detection is used.</li>
<li>Added <em>FSCommand</em> and <em>seekSegmentTime</em> to the “evil attributes” list in <code class="docutils literal"><span class="pre">CI_Security::xss_clean()</span></code>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-2-2">
<h3>Bug fixes for 2.2.2<a class="headerlink" href="#bug-fixes-for-2-2-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#3665) - <code class="docutils literal"><span class="pre">CI_Security::entity_decode()</span></code> triggered warnings under some circumstances.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-2-1">
<h2>Version 2.2.1<a class="headerlink" href="#version-2-2-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: January 22, 2015</p>
<ul class="simple">
<li>General Changes<ul>
<li>Improved security in <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
<li>Updated timezones in <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date Helper</span></a>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-2-1">
<h3>Bug fixes for 2.2.1<a class="headerlink" href="#bug-fixes-for-2-2-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#3094) - Internal method <code class="docutils literal"><span class="pre">CI_Input::_clean_input_data()</span></code> breaks encrypted session cookies.</li>
<li>Fixed a bug (#2268) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> didn’t properly match JavaScript events.</li>
<li>Fixed a bug (#3309) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> used an overly-invasive pattern to strip JS event handlers.</li>
<li>Fixed a bug (#2771) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">xss_clean()</span></code> didn’t take into account HTML5 entities.</li>
<li>Fixed a bug (#73) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">sanitize_filename()</span></code> could be tricked by an XSS attack.</li>
<li>Fixed a bug (#2681) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> method <code class="docutils literal"><span class="pre">entity_decode()</span></code> used the <code class="docutils literal"><span class="pre">PREG_REPLACE_EVAL</span></code> flag, which is deprecated since PHP 5.5.</li>
<li>Fixed a bug (#3302) - Internal function <code class="docutils literal"><span class="pre">get_config()</span></code> triggered an E_NOTICE message on PHP 5.6.</li>
<li>Fixed a bug (#2508) - <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> didn’t properly detect if the current request is via HTTPS.</li>
<li>Fixed a bug (#3314) - SQLSRV <a class="reference internal" href="database/index.html"><span class="doc">Database driver</span></a>’s method <code class="docutils literal"><span class="pre">count_all()</span></code> didn’t escape the supplied table name.</li>
<li>Fixed a bug (#3404) - MySQLi <a class="reference internal" href="database/index.html"><span class="doc">Database driver</span></a>’s method <code class="docutils literal"><span class="pre">escape_str()</span></code> had a wrong fallback to <code class="docutils literal"><span class="pre">mysql_escape_string()</span></code> when there was no active connection.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> where session ID regeneration occurred during AJAX requests.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-2-0">
<h2>Version 2.2.0<a class="headerlink" href="#version-2-2-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: June 2, 2014</p>
<ul class="simple">
<li>General Changes<ul>
<li>Security: <a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Library</span></a> method <code class="docutils literal"><span class="pre">xor_encode()</span></code> has been removed. The Encrypt Class now requires the Mcrypt extension to be installed.</li>
<li>Security: The <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> now uses HMAC authentication instead of a simple MD5 checksum.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-2-0">
<h3>Bug fixes for 2.2.0<a class="headerlink" href="#bug-fixes-for-2-2-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed an edge case (#2583) in the <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> where <cite>Suhosin &lt;http://www.hardened-php.net/suhosin/&gt;</cite> blocked messages sent via <code class="docutils literal"><span class="pre">mail()</span></code> due to trailing newspaces in headers.</li>
<li>Fixed a bug (#696) - make <code class="docutils literal"><span class="pre">oci_execute()</span></code> calls inside <code class="docutils literal"><span class="pre">num_rows()</span></code> non-committing, since they are only there to reset which row is next in line for oci_fetch calls and thus don’t need to be committed.</li>
<li>Fixed a bug (#2689) - <a class="reference internal" href="database/forge.html"><span class="doc">Database Force</span></a> methods <code class="docutils literal"><span class="pre">create_table()</span></code>, <code class="docutils literal"><span class="pre">drop_table()</span></code> and <code class="docutils literal"><span class="pre">rename_table()</span></code> produced broken SQL for tge ‘sqlsrv’ driver.</li>
<li>Fixed a bug (#2427) - PDO <a class="reference internal" href="database/index.html"><span class="doc">Database driver</span></a> didn’t properly check for query failures.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> where authentication was not performed for encrypted cookies.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-1-4">
<h2>Version 2.1.4<a class="headerlink" href="#version-2-1-4" title="Permalink to this headline">¶</a></h2>
<p>Release Date: July 8, 2013</p>
<ul class="simple">
<li>General Changes<ul>
<li>Improved security in <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-1-4">
<h3>Bug fixes for 2.1.4<a class="headerlink" href="#bug-fixes-for-2-1-4" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#1936) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> method <code class="docutils literal"><span class="pre">latest()</span></code> had a typo when retrieving language values.</li>
<li>Fixed a bug (#2021) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> configuration file was mistakenly using Windows style line feeds.</li>
<li>Fixed a bug (#1273) - <code class="docutils literal"><span class="pre">E_NOTICE</span></code> being generated by <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a>’s <code class="docutils literal"><span class="pre">set_update_batch()</span></code> method.</li>
<li>Fixed a bug (#2337) - <a class="reference internal" href="libraries/email.html"><span class="doc">Email Library</span></a> method <code class="docutils literal"><span class="pre">print_debugger()</span></code> didn’t apply <code class="docutils literal"><span class="pre">htmlspecialchars()</span></code> to headers.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-1-3">
<h2>Version 2.1.3<a class="headerlink" href="#version-2-1-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 8, 2012</p>
<ul class="simple">
<li>Core<ul>
<li><a class="reference internal" href="general/common_functions.html"><span class="doc">Common function</span></a> <code class="docutils literal"><span class="pre">is_loaded()</span></code> now returns a reference.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-1-3">
<h3>Bug fixes for 2.1.3<a class="headerlink" href="#bug-fixes-for-2-1-3" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#1543) - File-based <a class="reference internal" href="libraries/caching.html"><span class="doc">Caching</span></a> method <code class="docutils literal"><span class="pre">get_metadata()</span></code> used a non-existent array key to look for the TTL value.</li>
<li>Fixed a bug (#1314) - <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session Library</span></a> method <code class="docutils literal"><span class="pre">sess_destroy()</span></code> didn’t destroy the userdata array.</li>
<li>Fixed a bug (#804) - <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler library</span></a> was trying to handle objects as strings in some cases, resulting in <em>E_WARNING</em> messages being issued by <code class="docutils literal"><span class="pre">htmlspecialchars()</span></code>.</li>
<li>Fixed a bug (#1699) - <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration Library</span></a> ignored the <code class="docutils literal"><span class="pre">$config['migration_path']</span></code> setting.</li>
<li>Fixed a bug (#227) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> allowed unconditional spoofing of HTTP clients’ IP addresses through the <em>HTTP_CLIENT_IP</em> header.</li>
<li>Fixed a bug (#907) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> ignored <em>HTTP_X_CLUSTER_CLIENT_IP</em> and <em>HTTP_X_CLIENT_IP</em> headers when checking for proxies.</li>
<li>Fixed a bug (#940) - <code class="docutils literal"><span class="pre">csrf_verify()</span></code> used to set the CSRF cookie while processing a POST request with no actual POST data, which resulted in validating a request that should be considered invalid.</li>
<li>Fixed a bug (#499) - <a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> where a CSRF cookie was created even if <code class="docutils literal"><span class="pre">$config['csrf_protection']</span></code> is set to FALSE.</li>
<li>Fixed a bug (#1715) - <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a> triggered <code class="docutils literal"><span class="pre">csrf_verify()</span></code> on CLI requests.</li>
<li>Fixed a bug (#751) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t properly handle cached field escaping overrides.</li>
<li>Fixed a bug (#2004) - <a class="reference internal" href="database/query_builder.html"><span class="doc">Query Builder</span></a> didn’t properly merge cached calls with non-cache ones.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-1-2">
<h2>Version 2.1.2<a class="headerlink" href="#version-2-1-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: June 29, 2012</p>
<ul class="simple">
<li>General Changes<ul>
<li>Improved security in <code class="docutils literal"><span class="pre">xss_clean()</span></code>.</li>
</ul>
</li>
</ul>
</div>
<div class="section" id="version-2-1-1">
<h2>Version 2.1.1<a class="headerlink" href="#version-2-1-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: June 12, 2012</p>
<ul class="simple">
<li>General Changes<ul>
<li>Fixed support for docx, xlsx files in mimes.php.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Further improved MIME type detection in the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a>.</li>
<li>Added support for IPv6 to the <a class="reference internal" href="libraries/input.html"><span class="doc">Input Library</span></a>.</li>
<li>Added support for the IP format parameter to the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation Library</span></a>.</li>
</ul>
</li>
<li>Helpers<ul>
<li><code class="docutils literal"><span class="pre">url_title()</span></code> performance and output improved. You can now use any string as the word delimiter, but ‘dash’ and ‘underscore’ are still supported.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-1-1">
<h3>Bug fixes for 2.1.1<a class="headerlink" href="#bug-fixes-for-2-1-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#697) - A wrong array key was used in the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> to check for mime-types.</li>
<li>Fixed a bug - <code class="docutils literal"><span class="pre">form_open()</span></code> compared $action against <code class="docutils literal"><span class="pre">site_url()</span></code> instead of <code class="docutils literal"><span class="pre">base_url()</span></code>.</li>
<li>Fixed a bug - <code class="docutils literal"><span class="pre">CI_Upload::_file_mime_type()</span></code> could’ve failed if <code class="docutils literal"><span class="pre">mime_content_type()</span></code> is used for the detection and returns FALSE.</li>
<li>Fixed a bug (#538) - Windows paths were ignored when using the <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Library</span></a> to create a new file.</li>
<li>Fixed a bug - When database caching was enabled, $this-&gt;db-&gt;query() checked the cache before binding variables which resulted in cached queries never being found.</li>
<li>Fixed a bug - CSRF cookie value was allowed to be any (non-empty) string before being written to the output, making code injection a risk.</li>
<li>Fixed a bug (#726) - PDO put a ‘dbname’ argument in its connection string regardless of the database platform in use, which made it impossible to use SQLite.</li>
<li>Fixed a bug - <code class="docutils literal"><span class="pre">CI_DB_pdo_driver::num_rows()</span></code> was not returning properly value with SELECT queries, cause it was relying on <code class="docutils literal"><span class="pre">PDOStatement::rowCount()</span></code>.</li>
<li>Fixed a bug (#1059) - <code class="docutils literal"><span class="pre">CI_Image_lib::clear()</span></code> was not correctly clearing all necessary object properties, namely width and height.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-1-0">
<h2>Version 2.1.0<a class="headerlink" href="#version-2-1-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: November 14, 2011</p>
<ul class="simple">
<li>General Changes<ul>
<li>Callback validation rules can now accept parameters like any other
validation rule.</li>
<li>Added html_escape() to <a class="reference internal" href="general/common_functions.html"><span class="doc">Common
functions</span></a> to escape HTML output
for preventing XSS.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added increment_string() to <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String
Helper</span></a> to turn “foo” into “foo-1”
or “foo-1” into “foo-2”.</li>
<li>Altered form helper - made action on form_open_multipart helper
function call optional. Fixes (#65)</li>
<li>url_title() will now trim extra dashes from beginning and end.</li>
<li>Improved speed of <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a>’s random_string() method</li>
</ul>
</li>
<li>Database<ul>
<li>Added a <a class="reference external" href="http://www.cubrid.org/">CUBRID</a> driver to the <a class="reference internal" href="database/index.html"><span class="doc">Database
Driver</span></a>. Thanks to the CUBRID team for
supplying this patch.</li>
<li>Added a PDO driver to the <a class="reference internal" href="database/index.html"><span class="doc">Database Driver</span></a>.</li>
<li>Typecast limit and offset in the <a class="reference internal" href="database/queries.html"><span class="doc">Database
Driver</span></a> to integers to avoid possible
injection.</li>
<li>Added additional option ‘none’ for the optional third argument for
$this-&gt;db-&gt;like() in the <a class="reference internal" href="database/query_builder.html"><span class="doc">Database
Driver</span></a>.</li>
<li>Added $this-&gt;db-&gt;insert_batch() support to the OCI8 (Oracle) driver.</li>
<li>Added failover if the main connections in the config should fail</li>
</ul>
</li>
<li>Libraries<ul>
<li>Changed <code class="docutils literal"><span class="pre">$this-&gt;cart-&gt;insert()</span></code> in the <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart
Library</span></a> to return the Row ID if a single
item was inserted successfully.</li>
<li>Added support to set an optional parameter in your callback rules
of validation using the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation
Library</span></a>.</li>
<li>Added a <a class="reference internal" href="libraries/migration.html"><span class="doc">Migration library</span></a> to assist with applying
incremental updates to your database schema.</li>
<li>Driver children can be located in any package path.</li>
<li>Added max_filename_increment config setting for Upload library.</li>
<li>Added <code class="docutils literal"><span class="pre">is_unique</span></code> to the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation library</span></a>.</li>
<li>Added $config[‘use_page_numbers’] to the <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination library</span></a>, which enables real page numbers in the URI.</li>
<li>Added TLS and SSL Encryption for SMTP.</li>
</ul>
</li>
<li>Core<ul>
<li>Changed private functions in CI_URI to protected so MY_URI can
override them.</li>
<li>Removed CI_CORE boolean constant from CodeIgniter.php (no longer Reactor and Core versions).</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-1-0">
<h3>Bug fixes for 2.1.0<a class="headerlink" href="#bug-fixes-for-2-1-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed #378 Robots identified as regular browsers by the User Agent
class.</li>
<li>If a config class was loaded first then a library with the same name
is loaded, the config would be ignored.</li>
<li>Fixed a bug (Reactor #19) where 1) the 404_override route was being
ignored in some cases, and 2) auto-loaded libraries were not
available to the 404_override controller when a controller existed
but the requested method did not.</li>
<li>Fixed a bug (Reactor #89) where MySQL export would fail if the table
had hyphens or other non alphanumeric/underscore characters.</li>
<li>Fixed a bug (#105) that stopped query errors from being logged unless database debugging was enabled</li>
<li>Fixed a bug (#160) - Removed unneeded array copy in the file cache
driver.</li>
<li>Fixed a bug (#150) - field_data() now correctly returns column
length.</li>
<li>Fixed a bug (#8) - load_class() now looks for core classes in
APPPATH first, allowing them to be replaced.</li>
<li>Fixed a bug (#24) - ODBC database driver called incorrect parent in __construct().</li>
<li>Fixed a bug (#85) - OCI8 (Oracle) database escape_str() function did not escape correct.</li>
<li>Fixed a bug (#344) - Using schema found in <a class="reference internal" href="libraries/sessions.html"><span class="doc">Saving Session Data to a Database</span></a>, system would throw error “user_data does not have a default value” when deleting then creating a session.</li>
<li>Fixed a bug (#112) - OCI8 (Oracle) driver didn’t pass the configured database character set when connecting.</li>
<li>Fixed a bug (#182) - OCI8 (Oracle) driver used to re-execute the statement whenever num_rows() is called.</li>
<li>Fixed a bug (#82) - WHERE clause field names in the DB update_string() method were not escaped, resulting in failed queries in some cases.</li>
<li>Fixed a bug (#89) - Fix a variable type mismatch in DB display_error() where an array is expected, but a string could be set instead.</li>
<li>Fixed a bug (#467) - Suppress warnings generated from get_magic_quotes_gpc() (deprecated in PHP 5.4)</li>
<li>Fixed a bug (#484) - First time _csrf_set_hash() is called, hash is never set to the cookie (in Security.php).</li>
<li>Fixed a bug (#60) - Added _file_mime_type() method to the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Library</span></a> in order to fix a possible MIME-type injection.</li>
<li>Fixed a bug (#537) - Support for all wav type in browser.</li>
<li>Fixed a bug (#576) - Using ini_get() function to detect if apc is enabled or not.</li>
<li>Fixed invalid date time format in <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date helper</span></a> and <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XMLRPC library</span></a>.</li>
<li>Fixed a bug (#200) - MySQL queries would be malformed after calling db-&gt;count_all() then db-&gt;get().</li>
</ul>
</div>
</div>
<div class="section" id="version-2-0-3">
<h2>Version 2.0.3<a class="headerlink" href="#version-2-0-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: August 20, 2011</p>
<ul>
<li><p class="first">Security</p>
<ul>
<li><p class="first">An improvement was made to the MySQL and MySQLi drivers to prevent
exposing a potential vector for SQL injection on sites using
multi-byte character sets in the database client connection.
An incompatibility in PHP versions &lt; 5.2.3 and MySQL &lt; 5.0.7 with
<em>mysql_set_charset()</em> creates a situation where using multi-byte
character sets on these environments may potentially expose a SQL
injection attack vector. Latin-1, UTF-8, and other “low ASCII”
character sets are unaffected on all environments.</p>
<p>If you are running or considering running a multi-byte character
set for your database connection, please pay close attention to
the server environment you are deploying on to ensure you are not
vulnerable.</p>
</li>
</ul>
</li>
<li><p class="first">General Changes</p>
<ul class="simple">
<li>Fixed a bug where there was a misspelling within a code comment in
the index.php file.</li>
<li>Added Session Class userdata to the output profiler. Additionally,
added a show/hide toggle on HTTP Headers, Session Data and Config
Variables.</li>
<li>Removed internal usage of the EXT constant.</li>
<li>Visual updates to the welcome_message view file and default error
templates. Thanks to <a class="reference external" href="https://bitbucket.org/danijelb">danijelb</a>
for the pull request.</li>
<li>Added insert_batch() function to the PostgreSQL database driver.
Thanks to epallerols for the patch.</li>
<li>Added “application/x-csv” to mimes.php.</li>
<li>Fixed a bug where <a class="reference internal" href="libraries/email.html"><span class="doc">Email library</span></a>
attachments with a “.” in the name would using invalid MIME-types.</li>
</ul>
</li>
<li><p class="first">Helpers</p>
<ul class="simple">
<li>Added an optional third parameter to heading() which allows adding
html attributes to the rendered heading tag.</li>
<li>form_open() now only adds a hidden (Cross-site Reference Forgery)
protection field when the form’s action is internal and is set to
the post method. (Reactor #165)</li>
<li>Re-worked plural() and singular() functions in the <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector
helper</span></a> to support considerably
more words.</li>
</ul>
</li>
<li><p class="first">Libraries</p>
<ul class="simple">
<li>Altered Session to use a longer match against the user_agent
string. See upgrade notes if using database sessions.</li>
<li>Added $this-&gt;db-&gt;set_dbprefix() to the <a class="reference internal" href="database/queries.html"><span class="doc">Database
Driver</span></a>.</li>
<li>Changed $this-&gt;cart-&gt;insert() in the <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart
Library</span></a> to return the Row ID if a single
item was inserted successfully.</li>
<li>Added $this-&gt;load-&gt;get_var() to the <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader
library</span></a> to retrieve global vars set with
$this-&gt;load-&gt;view() and $this-&gt;load-&gt;vars().</li>
<li>Changed $this-&gt;db-&gt;having() to insert quotes using escape() rather
than escape_str().</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-0-3">
<h3>Bug fixes for 2.0.3<a class="headerlink" href="#bug-fixes-for-2-0-3" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Added ENVIRONMENT to reserved constants. (Reactor #196)</li>
<li>Changed server check to ensure SCRIPT_NAME is defined. (Reactor #57)</li>
<li>Removed APPPATH.’third_party’ from the packages autoloader to negate
needless file stats if no packages exist or if the developer does not
load any other packages by default.</li>
<li>Fixed a bug (Reactor #231) where Sessions Library database table
example SQL did not contain an index on last_activity. See <a class="reference internal" href="installation/upgrade_203.html"><span class="doc">Upgrade
Notes</span></a>.</li>
<li>Fixed a bug (Reactor #229) where the Sessions Library example SQL in
the documentation contained incorrect SQL.</li>
<li>Fixed a bug (Core #340) where when passing in the second parameter to
$this-&gt;db-&gt;select(), column names in subsequent queries would not be
properly escaped.</li>
<li>Fixed issue #199 - Attributes passed as string does not include a
space between it and the opening tag.</li>
<li>Fixed a bug where the method $this-&gt;cart-&gt;total_items() from <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart
Library</span></a> now returns the sum of the quantity
of all items in the cart instead of your total count.</li>
<li>Fixed a bug where not setting ‘null’ when adding fields in db_forge
for mysql and mysqli drivers would default to NULL instead of NOT
NULL as the docs suggest.</li>
<li>Fixed a bug where using $this-&gt;db-&gt;select_max(),
$this-&gt;db-&gt;select_min(), etc could throw notices. Thanks to w43l for
the patch.</li>
<li>Replace checks for STDIN with php_sapi_name() == ‘cli’ which on the
whole is more reliable. This should get parameters in crontab
working.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-0-2">
<h2>Version 2.0.2<a class="headerlink" href="#version-2-0-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 7, 2011
Hg Tag: v2.0.2</p>
<ul class="simple">
<li>General changes<ul>
<li>The <a class="reference internal" href="libraries/security.html"><span class="doc">Security library</span></a> was moved to
the core and is now loaded automatically. Please remove your
loading calls.</li>
<li>The CI_SHA class is now deprecated. All supported versions of PHP
provide a sha1() function.</li>
<li>constants.php will now be loaded from the environment folder if
available.</li>
<li>Added language key error logging</li>
<li>Made Environment Support optional. Comment out or delete the
constant to stop environment checks.</li>
<li>Added Environment Support for Hooks.</li>
<li>Added CI_ Prefix to the <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache driver</span></a>.</li>
<li>Added <a class="reference internal" href="general/cli.html"><span class="doc">CLI usage</span></a> documentation.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Removed the previously deprecated dohash() from the <a class="reference internal" href="helpers/security_helper.html"><span class="doc">Security
helper</span></a>; use do_hash() instead.</li>
<li>Changed the ‘plural’ function so that it doesn’t ruin the
captalization of your string. It also take into consideration
acronyms which are all caps.</li>
</ul>
</li>
<li>Database<ul>
<li>$this-&gt;db-&gt;count_all_results() will now return an integer
instead of a string.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-0-2">
<h3>Bug fixes for 2.0.2<a class="headerlink" href="#bug-fixes-for-2-0-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (Reactor #145) where the Output Library had
parse_exec_vars set to protected.</li>
<li>Fixed a bug (Reactor #80) where is_really_writable would create an
empty file when on Windows or with safe_mode enabled.</li>
<li>Fixed various bugs with User Guide.</li>
<li>Added is_cli_request() method to documentation for <a class="reference internal" href="libraries/input.html"><span class="doc">Input
class</span></a>.</li>
<li>Added form_validation_lang entries for decimal, less_than and
greater_than.</li>
<li>Fixed issue #153 Escape Str Bug in MSSQL driver.</li>
<li>Fixed issue #172 Google Chrome 11 posts incorrectly when action is empty.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-0-1">
<h2>Version 2.0.1<a class="headerlink" href="#version-2-0-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 15, 2011
Hg Tag: v2.0.1</p>
<ul class="simple">
<li>General changes<ul>
<li>Added $config[‘cookie_secure’] to the config file to allow
requiring a secure (HTTPS) in order to set cookies.</li>
<li>Added the constant CI_CORE to help differentiate between Core:
TRUE and Reactor: FALSE.</li>
<li>Added an ENVIRONMENT constant in index.php, which affects PHP
error reporting settings, and optionally, which configuration
files are loaded (see below). Read more on the <a class="reference internal" href="general/environments.html"><span class="doc">Handling
Environments</span></a> page.</li>
<li>Added support for
<a class="reference internal" href="libraries/config.html#config-environments"><span class="std std-ref">environment-specific</span></a>
configuration files.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Added decimal, less_than and greater_than rules to the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form
validation Class</span></a>.</li>
<li><a class="reference internal" href="libraries/input.html"><span class="doc">Input Class</span></a> methods post() and get()
will now return a full array if the first argument is not
provided.</li>
<li>Secure cookies can now be made with the set_cookie() helper and
<a class="reference internal" href="libraries/input.html"><span class="doc">Input Class</span></a> method.</li>
<li>Added set_content_type() to <a class="reference internal" href="libraries/output.html"><span class="doc">Output
Class</span></a> to set the output Content-Type
HTTP header based on a MIME Type or a config/mimes.php array key.</li>
<li><a class="reference internal" href="libraries/output.html"><span class="doc">Output Class</span></a> will now support method
chaining.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Changed the logic for form_open() in <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a>. If no value is passed it will
submit to the current URL.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-0-1">
<h3>Bug fixes for 2.0.1<a class="headerlink" href="#bug-fixes-for-2-0-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>CLI requests can now be run from any folder, not just when CD’ed next
to index.php.</li>
<li>Fixed issue #41: Added audio/mp3 mime type to mp3.</li>
<li>Fixed a bug (Core #329) where the file caching driver referenced the
incorrect cache directory.</li>
<li>Fixed a bug (Reactor #69) where the SHA1 library was named
incorrectly.</li>
</ul>
</div>
</div>
<div class="section" id="version-2-0-0">
<span id="changelog"></span><h2>Version 2.0.0<a class="headerlink" href="#version-2-0-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: January 28, 2011
Hg Tag: v2.0.0</p>
<ul class="simple">
<li>General changes<ul>
<li>PHP 4 support is removed. CodeIgniter now requires PHP 5.1.6.</li>
<li>Scaffolding, having been deprecated for a number of versions, has
been removed.</li>
<li>Plugins have been removed, in favor of Helpers. The CAPTCHA plugin
has been converted to a Helper and
<a class="reference internal" href="helpers/captcha_helper.html"><span class="doc">documented</span></a>. The JavaScript
calendar plugin was removed due to the ready availability of great
JavaScript calendars, particularly with jQuery.</li>
<li>Added new special Library type:
<a class="reference internal" href="general/drivers.html"><span class="doc">Drivers</span></a>.</li>
<li>Added full query-string support. See the config file for details.</li>
<li>Moved the application folder outside of the system folder.</li>
<li>Moved system/cache and system/logs directories to the application
directory.</li>
<li>Added routing overrides to the main index.php file, enabling the
normal routing to be overridden on a per “index” file basis.</li>
<li>Added the ability to set config values (or override config values)
directly from data set in the main index.php file. This allows a
single application to be used with multiple front controllers,
each having its own config values.</li>
<li>Added $config[‘directory_trigger’] to the config file so that a
controller sub-directory can be specified when running _GET
strings instead of URI segments.</li>
<li>Added ability to set “Package” paths - specific paths where the
Loader and Config classes should try to look first for a requested
file. This allows distribution of sub-applications with their own
libraries, models, config files, etc. in a single “package”
directory. See the <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader class</span></a>
documentation for more details.</li>
<li>In-development code is now hosted at BitBucket .</li>
<li>Removed the deprecated Validation Class.</li>
<li>Added CI_ Prefix to all core classes.</li>
<li>Package paths can now be set in application/config/autoload.php.</li>
<li><a class="reference internal" href="libraries/file_uploading.html"><span class="doc">Upload library</span></a> file_name can
now be set without an extension, the extension will be taken from
the uploaded file instead of the given name.</li>
<li>In <a class="reference internal" href="database/forge.html"><span class="doc">Database Forge</span></a> the name can be omitted
from $this-&gt;dbforge-&gt;modify_column()’s 2nd param if you aren’t
changing the name.</li>
<li>$config[‘base_url’] is now empty by default and will guess what
it should be.</li>
<li>Enabled full Command Line Interface compatibility with
config[‘uri_protocol’] = ‘CLI’;.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Added a <a class="reference internal" href="libraries/caching.html"><span class="doc">Cache driver</span></a> with APC,
memcached, and file-based support.</li>
<li>Added $prefix, $suffix and $first_url properties to <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination
library</span></a>.</li>
<li>Added the ability to suppress first, previous, next, last, and
page links by setting their values to FALSE in the <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination
library</span></a>.</li>
<li>Added <a class="reference internal" href="libraries/security.html"><span class="doc">Security library</span></a>, which now
contains the xss_clean function, filename_security function and
other security related functions.</li>
<li>Added CSRF (Cross-site Reference Forgery) protection to the
<a class="reference internal" href="libraries/security.html"><span class="doc">Security library</span></a>.</li>
<li>Added $parse_exec_vars property to Output library.</li>
<li>Added ability to enable / disable individual sections of the
<a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a></li>
<li>Added a wildcard option $config[‘allowed_types’] = ‘*’ to the
<a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Uploading Class</span></a>.</li>
<li>Added an ‘object’ config variable to the XML-RPC Server library so
that one can specify the object to look for requested methods,
instead of assuming it is in the $CI superobject.</li>
<li>Added “is_object” into the list of unit tests capable of being
run.</li>
<li>Table library will generate an empty cell with a blank string, or
NULL value.</li>
<li>Added ability to set tag attributes for individual cells in the
Table library</li>
<li>Added a parse_string() method to the <a class="reference internal" href="libraries/parser.html"><span class="doc">Parser
Class</span></a>.</li>
<li>Added HTTP headers and Config information to the
<a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a> output.</li>
<li>Added Chrome and Flock to the list of detectable browsers by
browser() in the <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Class</span></a>.</li>
<li>The <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Test Class</span></a> now has an
optional “notes” field available to it, and allows for discrete
display of test result items using
$this-&gt;unit-&gt;set_test_items().</li>
<li>Added a $xss_clean class variable to the XMLRPC library, enabling
control over the use of the Security library’s xss_clean()
method.</li>
<li>Added a download() method to the <a class="reference internal" href="libraries/ftp.html"><span class="doc">FTP
library</span></a></li>
<li>Changed do_xss_clean() to return FALSE if the uploaded file
fails XSS checks.</li>
<li>Added stripslashes() and trim()ing of double quotes from $_FILES
type value to standardize input in Upload library.</li>
<li>Added a second parameter (boolean) to
$this-&gt;zip-&gt;read_dir(‘/path/to/directory’, FALSE) to remove the
preceding trail of empty folders when creating a Zip archive. This
example would contain a zip with “directory” and all of its
contents.</li>
<li>Added ability in the Image Library to handle PNG transparency for
resize operations when using the GD lib.</li>
<li>Modified the Session class to prevent use if no encryption key is
set in the config file.</li>
<li>Added a new config item to the Session class
sess_expire_on_close to allow sessions to auto-expire when the
browser window is closed.</li>
<li>Improved performance of the Encryption library on servers where
Mcrypt is available.</li>
<li>Changed the default encryption mode in the Encryption library to
CBC.</li>
<li>Added an encode_from_legacy() method to provide a way to
transition encrypted data from CodeIgniter 1.x to CodeIgniter 2.x.
Please see the <a class="reference internal" href="installation/upgrade_200.html"><span class="doc">upgrade
instructions</span></a> for details.</li>
<li>Altered Form_Validation library to allow for method chaining on
set_rules(), set_message() and set_error_delimiters()
functions.</li>
<li>Altered Email Library to allow for method chaining.</li>
<li>Added request_headers(), get_request_header() and
is_ajax_request() to the input class.</li>
<li>Altered <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User agent library</span></a> so that
is_browser(), is_mobile() and is_robot() can optionally check
for a specific browser or mobile device.</li>
<li>Altered <a class="reference internal" href="libraries/input.html"><span class="doc">Input library</span></a> so that post() and
get() will return all POST and GET items (respectively) if there
are no parameters passed in.</li>
</ul>
</li>
<li>Database<ul>
<li><a class="reference internal" href="database/configuration.html"><span class="doc">database configuration</span></a>.</li>
<li>Added autoinit value to <a class="reference internal" href="database/configuration.html"><span class="doc">database
configuration</span></a>.</li>
<li>Added stricton value to <a class="reference internal" href="database/configuration.html"><span class="doc">database
configuration</span></a>.</li>
<li>Added database_exists() to the <a class="reference internal" href="database/utilities.html"><span class="doc">Database Utilities
Class</span></a>.</li>
<li>Semantic change to db-&gt;version() function to allow a list of
exceptions for databases with functions to return version string
instead of specially formed SQL queries. Currently this list only
includes Oracle and SQLite.</li>
<li>Fixed a bug where driver specific table identifier protection
could lead to malformed queries in the field_data() functions.</li>
<li>Fixed a bug where an undefined class variable was referenced in
database drivers.</li>
<li>Modified the database errors to show the filename and line number
of the problematic query.</li>
<li>Removed the following deprecated functions: orwhere, orlike,
groupby, orhaving, orderby, getwhere.</li>
<li>Removed deprecated _drop_database() and _create_database()
functions from the db utility drivers.</li>
<li>Improved dbforge create_table() function for the Postgres driver.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added convert_accented_characters() function to <a class="reference internal" href="helpers/text_helper.html"><span class="doc">text
helper</span></a>.</li>
<li>Added accept-charset to the list of inserted attributes of
form_open() in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form Helper</span></a>.</li>
<li>Deprecated the dohash() function in favour of do_hash() for
naming consistency.</li>
<li>Non-backwards compatible change made to get_dir_file_info() in
the <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File Helper</span></a>. No longer recurses
by default so as to encourage responsible use (this function can
cause server performance issues when used without caution).</li>
<li>Modified the second parameter of directory_map() in the
<a class="reference internal" href="helpers/directory_helper.html"><span class="doc">Directory Helper</span></a> to accept an
integer to specify recursion depth.</li>
<li>Modified delete_files() in the <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File
Helper</span></a> to return FALSE on failure.</li>
<li>Added an optional second parameter to byte_format() in the
<a class="reference internal" href="helpers/number_helper.html"><span class="doc">Number Helper</span></a> to allow for decimal
precision.</li>
<li>Added alpha, and sha1 string types to random_string() in the
<a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a>.</li>
<li>Modified prep_url() so as to not prepend http:// if the supplied
string already has a scheme.</li>
<li>Modified get_file_info in the file helper, changing filectime()
to filemtime() for dates.</li>
<li>Modified smiley_js() to add optional third parameter to return
only the javascript with no script tags.</li>
<li>The img() function of the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML
helper</span></a> will now generate an empty
string as an alt attribute if one is not provided.</li>
<li>If CSRF is enabled in the application config file, form_open()
will automatically insert it as a hidden field.</li>
<li>Added sanitize_filename() into the <a class="reference internal" href="helpers/security_helper.html"><span class="doc">Security
helper</span></a>.</li>
<li>Added ellipsize() to the <a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text
Helper</span></a></li>
<li>Added elements() to the <a class="reference internal" href="helpers/array_helper.html"><span class="doc">Array
Helper</span></a></li>
</ul>
</li>
<li>Other Changes<ul>
<li>Added an optional second parameter to show_404() to disable
logging.</li>
<li>Updated loader to automatically apply the sub-class prefix as an
option when loading classes. Class names can be prefixed with the
standard “CI_” or the same prefix as the subclass prefix, or no
prefix at all.</li>
<li>Increased randomness with is_really_writable() to avoid file
collisions when hundreds or thousands of requests occur at once.</li>
<li>Switched some DIR_WRITE_MODE constant uses to FILE_WRITE_MODE
where files and not directories are being operated on.</li>
<li>get_mime_by_extension() is now case insensitive.</li>
<li>Added “default” to the list <a class="reference internal" href="general/reserved_names.html"><span class="doc">Reserved
Names</span></a>.</li>
<li>Added ‘application/x-msdownload’ for .exe files and
‘application/x-gzip-compressed’ for .tgz files to
config/mimes.php.</li>
<li>Updated the output library to no longer compress output or send
content-length headers if the server runs with
zlib.output_compression enabled.</li>
<li>Eliminated a call to is_really_writable() on each request unless
it is really needed (Output caching)</li>
<li>Documented append_output() in the <a class="reference internal" href="libraries/output.html"><span class="doc">Output
Class</span></a>.</li>
<li>Documented a second argument in the decode() function for the
<a class="reference internal" href="libraries/encrypt.html"><span class="doc">Encrypt Class</span></a>.</li>
<li>Documented db-&gt;close().</li>
<li>Updated the router to support a default route with any number of
segments.</li>
<li>Moved _remove_invisible_characters() function from the
<a class="reference internal" href="libraries/security.html"><span class="doc">Security Library</span></a> to <a class="reference internal" href="general/common_functions.html"><span class="doc">common
functions.</span></a></li>
<li>Added audio/mpeg3 as a valid mime type for MP3.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-2-0-0">
<h3>Bug fixes for 2.0.0<a class="headerlink" href="#bug-fixes-for-2-0-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug where you could not change the User-Agent when sending
email.</li>
<li>Fixed a bug where the Output class would send incorrect cached output
for controllers implementing their own _output() method.</li>
<li>Fixed a bug where a failed query would not have a saved query
execution time causing errors in the Profiler</li>
<li>Fixed a bug that was writing log entries when multiple identical
helpers and plugins were loaded.</li>
<li>Fixed assorted user guide typos or examples (#10693, #8951, #7825,
#8660, #7883, #6771, #10656).</li>
<li>Fixed a language key in the profiler: “profiler_no_memory_usage”
to “profiler_no_memory”.</li>
<li>Fixed an error in the Zip library that didn’t allow downloading on
PHP 4 servers.</li>
<li>Fixed a bug in the Form Validation library where fields passed as
rule parameters were not being translated (#9132)</li>
<li>Modified inflector helper to properly pluralize words that end in
‘ch’ or ‘sh’</li>
<li>Fixed a bug in xss_clean() that was not allowing hyphens in query
strings of submitted URLs.</li>
<li>Fixed bugs in get_dir_file_info() and get_file_info() in the
File Helper with recursion, and file paths on Windows.</li>
<li>Fixed a bug where Active Record override parameter would not let you
disable Active Record if it was enabled in your database config file.</li>
<li>Fixed a bug in reduce_double_slashes() in the String Helper to
properly remove duplicate leading slashes (#7585)</li>
<li>Fixed a bug in values_parsing() of the XML-RPC library which
prevented NULL variables typed as ‘string’ from being handled
properly.</li>
<li>Fixed a bug were form_open_multipart() didn’t accept string
attribute arguments (#10930).</li>
<li>Fixed a bug (#10470) where get_mime_by_extension() was case
sensitive.</li>
<li>Fixed a bug where some error messages for the SQLite and Oracle
drivers would not display.</li>
<li>Fixed a bug where files created with the Zip Library would result in
file creation dates of 1980.</li>
<li>Fixed a bug in the Session library that would result in PHP error
when attempting to store values with objects.</li>
<li>Fixed a bug where extending the Controller class would result in a
fatal PHP error.</li>
<li>Fixed a PHP Strict Standards Error in the index.php file.</li>
<li>Fixed a bug where getimagesize() was being needlessly checked on
non-image files in is_allowed_type().</li>
<li>Fixed a bug in the Encryption library where an empty key was not
triggering an error.</li>
<li>Fixed a bug in the Email library where CC and BCC recipients were not
reset when using the clear() method (#109).</li>
<li>Fixed a bug in the URL Helper where prep_url() could cause a PHP
error on PHP versions &lt; 5.1.2.</li>
<li>Added a log message in core/output if the cache directory config
value was not found.</li>
<li>Fixed a bug where multiple libraries could not be loaded by passing
an array to load-&gt;library()</li>
<li>Fixed a bug in the html helper where too much white space was
rendered between the src and alt tags in the img() function.</li>
<li>Fixed a bug in the profilers _compile_queries() function.</li>
<li>Fixed a bug in the date helper where the DATE_ISO8601 variable was
returning an incorrectly formatted date string.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-7-2">
<h2>Version 1.7.2<a class="headerlink" href="#version-1-7-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: September 11, 2009
Hg Tag: v1.7.2</p>
<ul class="simple">
<li>Libraries<ul>
<li>Added a new <a class="reference internal" href="libraries/cart.html"><span class="doc">Cart Class</span></a>.</li>
<li>Added the ability to pass $config[‘file_name’] for the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File
Uploading Class</span></a> and rename the
uploaded file.</li>
<li>Changed order of listed user-agents so Safari would more
accurately report itself. (#6844)</li>
</ul>
</li>
<li>Database<ul>
<li>Switched from using gettype() in escape() to is_* methods, since
future PHP versions might change its output.</li>
<li>Updated all database drivers to handle arrays in escape_str()</li>
<li>Added escape_like_str() method for escaping strings to be used
in LIKE conditions</li>
<li>Updated Active Record to utilize the new LIKE escaping mechanism.</li>
<li>Added reconnect() method to DB drivers to try to keep alive /
reestablish a connection after a long idle.</li>
<li>Modified MSSQL driver to use mssql_get_last_message() for error
messages.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added form_multiselect() to the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a>.</li>
<li>Modified form_hidden() in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a> to accept multi-dimensional
arrays.</li>
<li>Modified form_prep() in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a> to keep track of prepped
fields to avoid multiple prep/mutation from subsequent calls which
can occur when using Form Validation and form helper functions to
output form fields.</li>
<li>Modified directory_map() in the <a class="reference internal" href="helpers/directory_helper.html"><span class="doc">Directory
helper</span></a> to allow the inclusion of
hidden files, and to return FALSE on failure to read directory.</li>
<li>Modified the <a class="reference internal" href="helpers/smiley_helper.html"><span class="doc">Smiley helper</span></a> to work
with multiple fields and insert the smiley at the last known
cursor position.</li>
</ul>
</li>
<li>General<ul>
<li>Compatible with PHP 5.3.0.</li>
<li>Modified <a class="reference internal" href="general/errors.html"><span class="doc">show_error()</span></a> to allow sending
of HTTP server response codes.</li>
<li>Modified <a class="reference internal" href="general/errors.html"><span class="doc">show_404()</span></a> to send 404 status
code, removing non-CGI compatible header() statement from
error_404.php template.</li>
<li>Added set_status_header() to the <a class="reference internal" href="general/common_functions.html"><span class="doc">Common
functions</span></a> to allow use when the
Output class is unavailable.</li>
<li>Added is_php() to <a class="reference internal" href="general/common_functions.html"><span class="doc">Common
functions</span></a> to facilitate PHP
version comparisons.</li>
<li>Added 2 CodeIgniter “cheatsheets” (thanks to DesignFellow.com for
this contribution).</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-1-7-2">
<h3>Bug fixes for 1.7.2<a class="headerlink" href="#bug-fixes-for-1-7-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed assorted user guide typos or examples (#6743, #7214, #7516,
#7287, #7852, #8224, #8324, #8349).</li>
<li>Fixed a bug in the Form Validation library where multiple callbacks
weren’t working (#6110)</li>
<li>doctype helper default value was missing a “1”.</li>
<li>Fixed a bug in the language class when outputting an error for an
unfound file.</li>
<li>Fixed a bug in the Calendar library where the shortname was output
for “May”.</li>
<li>Fixed a bug with ORIG_PATH_INFO that was allowing URIs of just a
slash through.</li>
<li>Fixed a fatal error in the Oracle and ODBC drivers (#6752)</li>
<li>Fixed a bug where xml_from_result() was checking for a nonexistent
method.</li>
<li>Fixed a bug where Database Forge’s add_column and modify_column
were not looping through when sent multiple fields.</li>
<li>Fixed a bug where the File Helper was using ‘/’ instead of the
DIRECTORY_SEPARATOR constant.</li>
<li>Fixed a bug to prevent PHP errors when attempting to use sendmail on
servers that have manually disabled the PHP popen() function.</li>
<li>Fixed a bug that would cause PHP errors in XML-RPC data if the PHP
data type did not match the specified XML-RPC type.</li>
<li>Fixed a bug in the XML-RPC class with parsing dateTime.iso8601 data
types.</li>
<li>Fixed a case sensitive string replacement in xss_clean()</li>
<li>Fixed a bug in form_textarea() where form data was not prepped
correctly.</li>
<li>Fixed a bug in form_prep() causing it to not preserve entities in
the user’s original input when called back into a form element</li>
<li>Fixed a bug in _protect_identifiers() where the swap prefix
($swap_pre) was not being observed.</li>
<li>Fixed a bug where the 400 status header sent with the ‘disallowed URI
characters’ was not compatible with CGI environments.</li>
<li>Fixed a bug in the typography class where heading tags could have
paragraph tags inserted when using auto_typography().</li>
</ul>
</div>
</div>
<div class="section" id="version-1-7-1">
<h2>Version 1.7.1<a class="headerlink" href="#version-1-7-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: February 10, 2009
Hg Tag: 1.7.1</p>
<ul class="simple">
<li>Libraries<ul>
<li>Fixed an arbitrary script execution security flaw (#6068) in the
Form Validation library (thanks to hkk)</li>
<li>Changed default current page indicator in the Pagination library
to use &lt;strong&gt; instead of &lt;b&gt;</li>
<li>A “HTTP/1.1 400 Bad Request” header is now sent when disallowed
characters are encountered.</li>
<li>Added &lt;big&gt;, &lt;small&gt;, &lt;q&gt;, and &lt;tt&gt; to the Typography parser’s
inline elements.</li>
<li>Added more accurate error reporting for the Email library when
using sendmail.</li>
<li>Removed a strict type check from the rotate() function of the
<a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Manipulation Class</span></a>.</li>
<li>Added enhanced error checking in file saving in the Image library
when using the GD lib.</li>
<li>Added an additional newline between multipart email headers and
the MIME message text for better compatibility with a variety of
MUAs.</li>
<li>Made modest improvements to efficiency and accuracy of
explode_name() in the Image lib.</li>
</ul>
</li>
<li>Database<ul>
<li>Added where_in to the list of expected arguments received by
delete().</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added the ability to have optgroups in form_dropdown() within the
<a class="reference internal" href="helpers/form_helper.html"><span class="doc">form helper</span></a>.</li>
<li>Added a doctype() function to the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML
helper</span></a>.</li>
<li>Added ability to force lowercase for url_title() in the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
helper</span></a>.</li>
<li>Changed the default “type” of form_button() to “button” from
“submit” in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">form helper</span></a>.</li>
<li>Changed redirect() in the URL helper to allow redirections to URLs
outside of the CI site.</li>
<li>Updated get_cookie() to try to fetch the cookie using the global
cookie prefix if the requested cookie name doesn’t exist.</li>
</ul>
</li>
<li>Other Changes<ul>
<li>Improved security in xss_clean() to help prevent attacks
targeting Internet Explorer.</li>
<li>Added ‘application/msexcel’ to config/mimes.php for .xls files.</li>
<li>Added ‘proxy_ips’ config item to whitelist reverse proxy servers
from which to trust the HTTP_X_FORWARDED_FOR header to to
determine the visitor’s IP address.</li>
<li>Improved accuracy of Upload::is_allowed_filetype() for images
(#6715)</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-1-7-1">
<h3>Bug fixes for 1.7.1<a class="headerlink" href="#bug-fixes-for-1-7-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Database<ul>
<li>Fixed a bug when doing ‘random’ on order_by() (#5706).</li>
<li>Fixed a bug where adding a primary key through Forge could fail
(#5731).</li>
<li>Fixed a bug when using DB cache on multiple databases (#5737).</li>
<li>Fixed a bug where TRUNCATE was not considered a “write” query
(#6619).</li>
<li>Fixed a bug where csv_from_result() was checking for a
nonexistent method.</li>
<li>Fixed a bug _protect_identifiers() where it was improperly
removing all pipe symbols from items</li>
</ul>
</li>
<li>Fixed assorted user guide typos or examples (#5998, #6093, #6259,
#6339, #6432, #6521).</li>
<li>Fixed a bug in the MySQLi driver when no port is specified</li>
<li>Fixed a bug (#5702), in which the field label was not being fetched
properly, when “matching” one field to another.</li>
<li>Fixed a bug in which identifers were not being escaped properly when
reserved characters were used.</li>
<li>Fixed a bug with the regular expression used to protect submitted
paragraph tags in auto typography.</li>
<li>Fixed a bug where double dashes within tag attributes were being
converted to em dash entities.</li>
<li>Fixed a bug where double spaces within tag attributes were being
converted to non-breaking space entities.</li>
<li>Fixed some accuracy issues with curly quotes in
Typography::format_characters()</li>
<li>Changed a few docblock comments to reflect actual return values.</li>
<li>Fixed a bug with high ascii characters in subject and from email
headers.</li>
<li>Fixed a bug in xss_clean() where whitespace following a validated
character entity would not be preserved.</li>
<li>Fixed a bug where HTML comments and &lt;pre&gt; tags were being parsed in
Typography::auto_typography().</li>
<li>Fixed a bug with non-breaking space cleanup in
Typography::auto_typography().</li>
<li>Fixed a bug in database escaping where a compound statement (ie:
SUM()) wasn’t handled correctly with database prefixes.</li>
<li>Fixed a bug when an opening quote is preceded by a paragraph tag and
immediately followed by another tag.</li>
<li>Fixed a bug in the Text Helper affecting some locales where
word_censor() would not work on words beginning or ending with an
accented character.</li>
<li>Fixed a bug in the Text Helper character limiter where the provided
limit intersects the last word of the string.</li>
<li>Fixed a bug (#6342) with plural() in the Inflection helper with words
ending in “y”.</li>
<li>Fixed bug (#6517) where Routed URI segments returned by
URI::rsegment() method were incorrect for the default controller.</li>
<li>Fixed a bug (#6706) in the Security Helper where xss_clean() was
using a deprecated second argument.</li>
<li>Fixed a bug in the URL helper url_title() function where trailing
periods were allowed at the end of a URL.</li>
<li>Fixed a bug (#6669) in the Email class when CRLF’s are used for the
newline character with headers when used with the “mail” protocol.</li>
<li>Fixed a bug (#6500) where URI::A_filter_uri() was exit()ing an
error instead of using show_error().</li>
<li>Fixed a bug (#6592) in the File Helper where get_dir_file_info()
where recursion was not occurring properly.</li>
<li>Tweaked Typography::auto_typography() for some edge-cases.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-7">
<h2>Version 1.7<a class="headerlink" href="#version-1-7" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 23, 2008
Hg Tag: 1.7.0</p>
<ul class="simple">
<li>Libraries<ul>
<li>Added a new <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation
Class</span></a>. It simplifies setting
rules and field names, supports arrays as field names, allows
groups of validation rules to be saved in a config file, and adds
some helper functions for use in view files. <strong>Please note that
the old Validation class is now deprecated</strong>. We will leave it in
the library folder for some time so that existing applications
that use it will not break, but you are encouraged to migrate to
the new version.</li>
<li>Updated the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Sessions class</span></a> so that
any custom data being saved gets stored to a database rather than
the session cookie (assuming you are using a database to store
session data), permitting much more data to be saved.</li>
<li>Added the ability to store libraries in subdirectories within
either the main “libraries” or the local application “libraries”
folder. Please see the <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader class</span></a> for
more info.</li>
<li>Added the ability to assign library objects to your own variable
names when you use $this-&gt;load-&gt;library(). Please see the <a class="reference internal" href="libraries/loader.html"><span class="doc">Loader
class</span></a> for more info.</li>
<li>Added controller class/method info to <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler
class</span></a> and support for multiple database
connections.</li>
<li>Improved the “auto typography” feature and moved it out of the
helper into its own <a class="reference internal" href="libraries/typography.html"><span class="doc">Typography
Class</span></a>.</li>
<li>Improved performance and accuracy of xss_clean(), including
reduction of false positives on image/file tests.</li>
<li>Improved <a class="reference internal" href="libraries/parser.html"><span class="doc">Parser class</span></a> to allow
multiple calls to the parse() function. The output of each is
appended in the output.</li>
<li>Added max_filename option to set a file name length limit in the
<a class="reference internal" href="libraries/file_uploading.html"><span class="doc">File Upload Class</span></a>.</li>
<li>Added set_status_header() function to <a class="reference internal" href="libraries/output.html"><span class="doc">Output
class</span></a>.</li>
<li>Modified <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination</span></a> class to only
output the “First” link when the link for page one would not be
shown.</li>
<li>Added support for mb_strlen in the <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form
Validation</span></a> class so that
multi-byte languages will calculate string lengths properly.</li>
</ul>
</li>
<li>Database<ul>
<li>Improved Active Record class to allow full path column and table
names: hostname.database.table.column. Also improved the alias
handling.</li>
<li>Improved how table and column names are escaped and prefixed. It
now honors full path names when adding prefixes and escaping.</li>
<li>Added Active Record caching feature to “update” and “delete”
functions.</li>
<li>Added removal of non-printing control characters in escape_str()
of DB drivers that do not have native PHP escaping mechanisms
(mssql, oci8, odbc), to avoid potential SQL errors, and possible
sources of SQL injection.</li>
<li>Added port support to MySQL, MySQLi, and MS SQL database drivers.</li>
<li>Added driver name variable in each DB driver, based on bug report
#4436.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added several new “setting” functions to the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a> that allow POST data to be
retrieved and set into forms. These are intended to be used on
their own, or with the new <a class="reference internal" href="libraries/form_validation.html"><span class="doc">Form Validation
Class</span></a>.</li>
<li>Added current_url() and uri_segments() to <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
helper</span></a>.</li>
<li>Altered auto_link() in the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
helper</span></a> so that email addresses with
“+” included will be linked.</li>
<li>Added meta() function to <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML
helper</span></a>.</li>
<li>Improved accuracy of calculations in <a class="reference internal" href="helpers/number_helper.html"><span class="doc">Number
helper</span></a>.</li>
<li>Removed added newlines (“\n”) from most form and html helper
functions.</li>
<li>Tightened up validation in the <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date
helper</span></a> function human_to_unix(),
and eliminated the POSIX regex.</li>
<li>Updated <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date helper</span></a> to match the
world’s current time zones and offsets.</li>
<li>Modified url_title() in the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
helper</span></a> to remove characters and digits
that are part of character entities, to allow dashes, underscores,
and periods regardless of the $separator, and to allow uppercase
characters.</li>
<li>Added support for arbitrary attributes in anchor_popup() of the
<a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL helper</span></a>.</li>
</ul>
</li>
<li>Other Changes<ul>
<li>Added <a class="reference internal" href="general/styleguide.html"><span class="doc">PHP Style Guide</span></a> to docs.</li>
<li>Added sanitization in xss_clean() for a deprecated HTML tag that
could be abused in user input in Internet Explorer.</li>
<li>Added a few openxml document mime types, and an additional mobile
agent to mimes.php and user_agents.php respectively.</li>
<li>Added a file lock check during caching, before trying to write to
the file.</li>
<li>Modified Cookie key cleaning to unset a few troublesome key names
that can be present in certain environments, preventing CI from
halting execution.</li>
<li>Changed the output of the profiler to use style attribute rather
than clear, and added the id “codeigniter_profiler” to the
container div.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-1-7-0">
<h3>Bug fixes for 1.7.0<a class="headerlink" href="#bug-fixes-for-1-7-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed bug in xss_clean() that could remove some desirable tag
attributes.</li>
<li>Fixed assorted user guide typos or examples (#4807, #4812, #4840,
#4862, #4864, #4899, #4930, #5006, #5071, #5158, #5229, #5254,
#5351).</li>
<li>Fixed an edit from 1.6.3 that made the $robots array in
user_agents.php go poof.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/email.html"><span class="doc">Email library</span></a> with
quoted-printable encoding improperly encoding space and tab
characters.</li>
<li>Modified XSS sanitization to no longer add semicolons after &amp;[single
letter], such as in M&amp;M’s, B&amp;B, etc.</li>
<li>Modified XSS sanitization to no longer strip XHTML image tags of
closing slashes.</li>
<li>Fixed a bug in the Session class when database sessions are used
where upon session update all userdata would be errantly written to
the session cookie.</li>
<li>Fixed a bug (#4536) in backups with the MySQL driver where some
legacy code was causing certain characters to be double escaped.</li>
<li>Fixed a routing bug (#4661) that occurred when the default route
pointed to a subfolder.</li>
<li>Fixed the spelling of “Dhaka” in the timezone_menu() function of the
<a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date helper.</span></a></li>
<li>Fixed the spelling of “raspberry” in config/smileys.php.</li>
<li>Fixed incorrect parenthesis in form_open() function (#5135).</li>
<li>Fixed a bug that was ignoring case when comparing controller methods
(#4560).</li>
<li>Fixed a bug (#4615) that was not setting SMTP authorization settings
when using the initialize function.</li>
<li>Fixed a bug in highlight_code() in the <a class="reference internal" href="helpers/text_helper.html"><span class="doc">Text
helper</span></a> that would leave a stray &lt;/span&gt;
in certain cases.</li>
<li>Fixed Oracle bug (#3306) that was preventing multiple queries in one
action.</li>
<li>Fixed ODBC bug that was ignoring connection params due to its use of
a constructor.</li>
<li>Fixed a DB driver bug with num_rows() that would cause an error with
the Oracle driver.</li>
<li>Fixed MS SQL bug (#4915). Added brackets around database name in MS
SQL driver when selecting the database, in the event that reserved
characters are used in the name.</li>
<li>Fixed a DB caching bug (4718) in which the path was incorrect when no
URI segments were present.</li>
<li>Fixed Image_lib class bug #4562. A path was not defined for NetPBM.</li>
<li>Fixed Image_lib class bug #4532. When cropping an image with
identical height/width settings on output, a copy is made.</li>
<li>Fixed DB_driver bug (4900), in which a database error was not being
logged correctly.</li>
<li>Fixed DB backup bug in which field names were not being escaped.</li>
<li>Fixed a DB Active Record caching bug in which multiple calls to
cached data were not being honored.</li>
<li>Fixed a bug in the Session class that was disallowing slashes in the
serialized array.</li>
<li>Fixed a Form Validation bug in which the “isset” error message was
being trigged by the “required” rule.</li>
<li>Fixed a spelling error in a Loader error message.</li>
<li>Fixed a bug (5050) with IP validation with empty segments.</li>
<li>Fixed a bug in which the parser was being greedy if multiple
identical sets of tags were encountered.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-6-3">
<h2>Version 1.6.3<a class="headerlink" href="#version-1-6-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: June 26, 2008
Hg Tag: v1.6.3</p>
<p>Version 1.6.3 is a security and maintenance release and is recommended
for all users.</p>
<ul class="simple">
<li>Database<ul>
<li>Modified MySQL/MySQLi Forge class to give explicit names to keys</li>
<li>Added ability to set multiple column non-primary keys to the
<a class="reference internal" href="database/forge.html"><span class="doc">Forge class</span></a></li>
<li>Added ability to set additional database config values in <a class="reference internal" href="database/connecting.html"><span class="doc">DSN
connections</span></a> via the query string.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Set the mime type check in the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">Upload
class</span></a> to reference the global
mimes variable.</li>
<li>Added support for query strings to the <a class="reference internal" href="libraries/pagination.html"><span class="doc">Pagination
class</span></a>, automatically detected or
explicitly declared.</li>
<li>Added get_post() to the <a class="reference internal" href="libraries/input.html"><span class="doc">Input class</span></a>.</li>
<li>Documented get() in the <a class="reference internal" href="libraries/input.html"><span class="doc">Input class</span></a>.</li>
<li>Added the ability to automatically output language items as form
labels in the <a class="reference internal" href="libraries/language.html"><span class="doc">Language class</span></a>.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added a <a class="reference internal" href="helpers/language_helper.html"><span class="doc">Language helper</span></a>.</li>
<li>Added a <a class="reference internal" href="helpers/number_helper.html"><span class="doc">Number helper</span></a>.</li>
<li><a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form helper</span></a> refactored to allow
form_open() and form_fieldset() to accept arrays or strings as
arguments.</li>
</ul>
</li>
<li>Other changes<ul>
<li>Improved security in xss_clean().</li>
<li>Removed an unused Router reference in _display_cache().</li>
<li>Added ability to <a class="reference internal" href="libraries/input.html"><span class="doc">use xss_clean() to test
images</span></a> for XSS, useful for upload
security.</li>
<li>Considerably expanded list of mobile user-agents in
config/user_agents.php.</li>
<li>Charset information in the userguide has been moved above title
for internationalization purposes (#4614).</li>
<li>Added “Using Associative Arrays In a Request Parameter” example to
the <a class="reference internal" href="libraries/xmlrpc.html"><span class="doc">XMLRPC userguide page</span></a>.</li>
<li>Removed maxlength and size as automatically added attributes of
form_input() in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">form helper</span></a>.</li>
<li>Documented the language file use of byte_format() in the <a class="reference internal" href="helpers/number_helper.html"><span class="doc">number
helper</span></a>.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-1-6-3">
<h3>Bug fixes for 1.6.3<a class="headerlink" href="#bug-fixes-for-1-6-3" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Added a language key for valid_emails in validation_lang.php.</li>
<li>Amended fixes for bug (#3419) with parsing DSN database connections.</li>
<li>Moved the _has_operator() function (#4535) into DB_driver from
DB_active_rec.</li>
<li>Fixed a syntax error in upload_lang.php.</li>
<li>Fixed a bug (#4542) with a regular expression in the Image library.</li>
<li>Fixed a bug (#4561) where orhaving() wasn’t properly passing values.</li>
<li>Removed some unused variables from the code (#4563).</li>
<li>Fixed a bug where having() was not adding an = into the statement
(#4568).</li>
<li>Fixed assorted user guide typos or examples (#4574, #4706).</li>
<li>Added quoted-printable headers to Email class when the multi-part
override is used.</li>
<li>Fixed a double opening &lt;p&gt; tag in the index pages of each system
directory.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-6-2">
<h2>Version 1.6.2<a class="headerlink" href="#version-1-6-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: May 13, 2008
Hg Tag: 1.6.2</p>
<ul class="simple">
<li>Active Record<ul>
<li>Added the ability to prevent escaping in having() clauses.</li>
<li>Added rename_table() into <a class="reference internal" href="database/forge.html"><span class="doc">DBForge</span></a>.</li>
<li>Fixed a bug that wasn’t allowing escaping to be turned off if the
value of a query was NULL.</li>
<li>DB Forge is now assigned to any models that exist after loading
(#3457).</li>
</ul>
</li>
<li>Database<ul>
<li>Added <a class="reference internal" href="database/transactions.html"><span class="doc">Strict Mode</span></a> to database
transactions.</li>
<li>Escape behaviour in where() clauses has changed; values in those
with the “FALSE” argument are no longer escaped (ie: quoted).</li>
</ul>
</li>
<li>Config<ul>
<li>Added ‘application/vnd.ms-powerpoint’ to list of mime types.</li>
<li>Added ‘audio/mpg’ to list of mime types.</li>
<li>Added new user-modifiable file constants.php containing file mode
and fopen constants.</li>
<li>Added the ability to set CRLF settings via config in the
<a class="reference internal" href="libraries/email.html"><span class="doc">Email</span></a> class.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Added increased security for filename handling in the Upload
library.</li>
<li>Added increased security for sessions for client-side data
tampering.</li>
<li>The MySQLi forge class is now in sync with MySQL forge.</li>
<li>Added the ability to set CRLF settings via config in the
<a class="reference internal" href="libraries/email.html"><span class="doc">Email</span></a> class.</li>
<li><a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing</span></a> results are now
colour coded, and a change was made to the default template of
results.</li>
<li>Added a valid_emails rule to the Validation class.</li>
<li>The <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip class</span></a> now exits within download().</li>
<li>The <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip class</span></a> has undergone a substantial
re-write for speed and clarity (thanks stanleyxu for the hard work
and code contribution in bug report #3425!)</li>
</ul>
</li>
<li>Helpers<ul>
<li>Added a Compatibility
Helper for using some common
PHP 5 functions safely in applications that might run on PHP 4
servers (thanks Seppo for the hard work and code contribution!)</li>
<li>Added form_button() in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">Form
helper</span></a>.</li>
<li>Changed the radio() and checkbox() functions to default to not
checked by default.</li>
<li>Added the ability to include an optional HTTP Response Code in the
redirect() function of the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
Helper</span></a>.</li>
<li>Modified img() in the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML Helper</span></a> to
remove an unneeded space (#4208).</li>
<li>Modified anchor() in the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL helper</span></a>
to no longer add a default title= attribute (#4209).</li>
<li>The <a class="reference internal" href="helpers/download_helper.html"><span class="doc">Download helper</span></a> now exits
within force_download().</li>
<li>Added get_dir_file_info(), get_file_info(), and
get_mime_by_extension() to the <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File
Helper</span></a>.</li>
<li>Added symbolic_permissions() and octal_permissions() to the
<a class="reference internal" href="helpers/file_helper.html"><span class="doc">File helper</span></a>.</li>
</ul>
</li>
<li>Plugins<ul>
<li>Modified captcha generation to first look for the function
imagecreatetruecolor, and fallback to imagecreate if it isn’t
available (#4226).</li>
</ul>
</li>
<li>Other Changes<ul>
<li>Added ability for <a class="reference internal" href="libraries/input.html"><span class="doc">xss_clean()</span></a> to accept
arrays.</li>
<li>Removed closing PHP tags from all PHP files to avoid accidental
output and potential ‘cannot modify headers’ errors.</li>
<li>Removed “scripts” from the auto-load search path. Scripts were
deprecated in Version 1.4.1 (September 21, 2006). If you still
need to use them for legacy reasons, they must now be manually
loaded in each Controller.</li>
<li>Added a <a class="reference internal" href="general/reserved_names.html"><span class="doc">Reserved Names</span></a> page to
the userguide, and migrated reserved controller names into it.</li>
<li>Added a <a class="reference internal" href="general/common_functions.html"><span class="doc">Common Functions</span></a> page
to the userguide for globally available functions.</li>
<li>Improved security and performance of xss_clean().</li>
</ul>
</li>
</ul>
<div class="section" id="bugfixes-for-1-6-2">
<h3>Bugfixes for 1.6.2<a class="headerlink" href="#bugfixes-for-1-6-2" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug where SET queries were not being handled as “write”
queries.</li>
<li>Fixed a bug (#3191) with ORIG_PATH_INFO URI parsing.</li>
<li>Fixed a bug in DB Forge, when inserting an id field (#3456).</li>
<li>Fixed a bug in the table library that could cause identically
constructed rows to be dropped (#3459).</li>
<li>Fixed DB Driver and MySQLi result driver checking for resources
instead of objects (#3461).</li>
<li>Fixed an AR_caching error where it wasn’t tracking table aliases
(#3463).</li>
<li>Fixed a bug in AR compiling, where select statements with arguments
got incorrectly escaped (#3478).</li>
<li>Fixed an incorrect documentation of $this-&gt;load-&gt;language (#3520).</li>
<li>Fixed bugs (#3523, #4350) in get_filenames() with recursion and
problems with Windows when $include_path is used.</li>
<li>Fixed a bug (#4153) in the XML-RPC class preventing dateTime.iso8601
from being used.</li>
<li>Fixed an AR bug with or_where_not_in() (#4171).</li>
<li>Fixed a bug with <a class="reference internal" href="libraries/input.html"><span class="doc">xss_clean()</span></a> that would
add semicolons to GET URI variable strings.</li>
<li>Fixed a bug (#4206) in the Directory Helper where the directory
resource was not being closed, and minor improvements.</li>
<li>Fixed a bug in the FTP library where delete_dir() was not working
recursively (#4215).</li>
<li>Fixed a Validation bug when set_rules() is used with a non-array
field name and rule (#4220).</li>
<li>Fixed a bug (#4223) where DB caching would not work for returned DB
objects or multiple DB connections.</li>
<li>Fixed a bug in the Upload library that might output the same error
twice (#4390).</li>
<li>Fixed an AR bug when joining with a table alias and table prefix
(#4400).</li>
<li>Fixed a bug in the DB class testing the $params argument.</li>
<li>Fixed a bug in the Table library where the integer 0 in cell data
would be displayed as a blank cell.</li>
<li>Fixed a bug in link_tag() of the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL
helper</span></a> where a key was passed instead of
a value.</li>
<li>Fixed a bug in DB_result::row() that prevented it from returning
individual fields with MySQL NULL values.</li>
<li>Fixed a bug where SMTP emails were not having dot transformation
performed on lines that begin with a dot.</li>
<li>Fixed a bug in display_error() in the DB driver that was
instantiating new Language and Exception objects, and not using the
error heading.</li>
<li>Fixed a bug (#4413) where a URI containing slashes only e.g.
‘http://example.com/index.php?//’ would result in PHP errors</li>
<li>Fixed an array to string conversion error in the Validation library
(#4425)</li>
<li>Fixed bug (#4451, #4299, #4339) where failed transactions will not
rollback when debug mode is enabled.</li>
<li>Fixed a bug (#4506) with overlay_watermark() in the Image library
preventing support for PNG-24s with alpha transparency</li>
<li>Fixed assorted user guide typos (#3453, #4364, #4379, #4399, #4408,
#4412, #4448, #4488).</li>
</ul>
</div>
</div>
<div class="section" id="version-1-6-1">
<h2>Version 1.6.1<a class="headerlink" href="#version-1-6-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: February 12, 2008
Hg Tag: 1.6.1</p>
<ul class="simple">
<li>Active Record<ul>
<li>Added <a class="reference internal" href="database/query_builder.html#ar-caching"><span class="std std-ref">Active Record
Caching</span></a>.</li>
<li>Made Active Record fully database-prefix aware.</li>
</ul>
</li>
<li>Database drivers<ul>
<li>Added support for setting client character set and collation for
MySQLi.</li>
</ul>
</li>
<li>Core Changes<ul>
<li>Modified xss_clean() to be more intelligent with its handling of
URL encoded strings.</li>
<li>Added $_SERVER, $_FILES, $_ENV, and $_SESSION to sanitization
of globals.</li>
<li>Added a <a class="reference internal" href="helpers/path_helper.html"><span class="doc">Path Helper</span></a>.</li>
<li>Simplified _reindex_segments() in the URI class.</li>
<li>Escaped the ‘-‘ in the default ‘permitted_uri_chars’ config
item, to prevent errors if developers just try to add additional
characters to the end of the default expression.</li>
<li>Modified method calling to controllers to show a 404 when a
private or protected method is accessed via a URL.</li>
<li>Modified framework initiated 404s to log the controller and method
for invalid requests.</li>
</ul>
</li>
<li>Helpers<ul>
<li>Modified get_filenames() in the File Helper to return FALSE if
the $source_dir is not readable.</li>
</ul>
</li>
</ul>
<div class="section" id="bugfixes-for-1-6-1">
<h3>Bugfixes for 1.6.1<a class="headerlink" href="#bugfixes-for-1-6-1" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Deprecated is_numeric as a validation rule. Use of numeric and
integer are preferred.</li>
<li>Fixed bug (#3379) in DBForge with SQLite for table creation.</li>
<li>Made Active Record fully database prefix aware (#3384).</li>
<li>Fixed a bug where DBForge was outputting invalid SQL in Postgres by
adding brackets around the tables in FROM.</li>
<li>Changed the behaviour of Active Record’s update() to make the WHERE
clause optional (#3395).</li>
<li>Fixed a bug (#3396) where certain POST variables would cause a PHP
warning.</li>
<li>Fixed a bug in query binding (#3402).</li>
<li>Changed order of SQL keywords in the Profiler $highlight array so OR
would not be highlighted before ORDER BY.</li>
<li>Fixed a bug (#3404) where the MySQLi driver was testing if
$this-&gt;conn_id was a resource instead of an object.</li>
<li>Fixed a bug (#3419) connecting to a database via a DSN string.</li>
<li>Fixed a bug (#3445) where the routed segment array was not re-indexed
to begin with 1 when the default controller is used.</li>
<li>Fixed assorted user guide typos.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-6-0">
<h2>Version 1.6.0<a class="headerlink" href="#version-1-6-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: January 30, 2008</p>
<ul class="simple">
<li>DBForge<ul>
<li>Added <a class="reference internal" href="database/forge.html"><span class="doc">DBForge</span></a> to the database tools.</li>
<li>Moved create_database() and drop_database() into
<a class="reference internal" href="database/forge.html"><span class="doc">DBForge</span></a>.</li>
<li>Added add_field(), add_key(), create_table(), drop_table(),
add_column(), drop_column(), modify_column() into
<a class="reference internal" href="database/forge.html"><span class="doc">DBForge</span></a>.</li>
</ul>
</li>
<li>Active Record<ul>
<li>Added protect_identifiers() in <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>All AR queries are backticked if appropriate to the database.</li>
<li>Added where_in(), or_where_in(), where_not_in(),
or_where_not_in(), not_like() and or_not_like() to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>Added support for limit() into update() and delete() statements in
<a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record</span></a>.</li>
<li>Added empty_table() and truncate_table() to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>Added the ability to pass an array of tables to the delete()
statement in <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record</span></a>.</li>
<li>Added count_all_results() function to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>Added select_max(), select_min(), select_avg() and
select_sum() to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record</span></a>.</li>
<li>Added the ability to use aliases with joins in <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>Added a third parameter to Active Record’s like() clause to
control where the wildcard goes.</li>
<li>Added a third parameter to set() in <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a> that withholds escaping
data.</li>
<li>Changed the behaviour of variables submitted to the where() clause
with no values to auto set “IS NULL”</li>
</ul>
</li>
<li>Other Database Related<ul>
<li>MySQL driver now requires MySQL 4.1+</li>
<li>Added $this-&gt;DB-&gt;save_queries variable to DB driver, enabling
queries to get saved or not. Previously they were always saved.</li>
<li>Added $this-&gt;db-&gt;dbprefix() to manually add database prefixes.</li>
<li>Added ‘random’ as an order_by() option , and removed “rand()” as
a listed option as it was MySQL only.</li>
<li>Added a check for NULL fields in the MySQL database backup
utility.</li>
<li>Added “constrain_by_prefix” parameter to db-&gt;list_table()
function. If set to TRUE it will limit the result to only table
names with the current prefix.</li>
<li>Deprecated from Active Record; getwhere() for get_where();
groupby() for group_by(); havingor() for having_or(); orderby()
for order_by; orwhere() for or_where(); and orlike() for
or_like().</li>
<li>Modified csv_from_result() to output CSV data more in the spirit
of basic rules of RFC 4180.</li>
<li>Added ‘char_set’ and ‘dbcollat’ database configuration settings,
to explicitly set the client communication properly.</li>
<li>Removed ‘active_r’ configuration setting and replaced with a
global $active_record setting, which is more in harmony with the
global nature of the behavior (#1834).</li>
</ul>
</li>
<li>Core changes<ul>
<li>Added ability to load multiple views, whose content will be
appended to the output in the order loaded.</li>
<li>Added the ability to <a class="reference internal" href="general/autoloader.html"><span class="doc">auto-load</span></a>
<a class="reference internal" href="general/models.html"><span class="doc">Models</span></a>.</li>
<li>Reorganized the URI and Routes classes for better clarity.</li>
<li>Added Compat.php to allow function overrides for older versions of
PHP or PHP environments missing certain extensions / libraries</li>
<li>Added memory usage, GET, URI string data, and individual query
execution time to Profiler output.</li>
<li>Deprecated Scaffolding.</li>
<li>Added is_really_writable() to Common.php to provide a
cross-platform reliable method of testing file/folder writability.</li>
</ul>
</li>
<li>Libraries<ul>
<li>Changed the load protocol of Models to allow for extension.</li>
<li>Strengthened the Encryption library to help protect against man in
the middle attacks when MCRYPT_MODE_CBC mode is used.</li>
<li>Added Flashdata variables, session_id regeneration and
configurable session update times to the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session
class.</span></a></li>
<li>Removed ‘last_visit’ from the Session class.</li>
<li>Added a language entry for valid_ip validation error.</li>
<li>Modified prep_for_form() in the Validation class to accept
arrays, adding support for POST array validation (via callbacks
only)</li>
<li>Added an “integer” rule into the Validation library.</li>
<li>Added valid_base64() to the Validation library.</li>
<li>Documented clear() in the <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image
Processing</span></a> library.</li>
<li>Changed the behaviour of custom callbacks so that they no longer
trigger the “required” rule.</li>
<li>Modified Upload class $_FILES error messages to be more precise.</li>
<li>Moved the safe mode and auth checks for the Email library into the
constructor.</li>
<li>Modified variable names in _ci_load() method of Loader class to
avoid conflicts with view variables.</li>
<li>Added a few additional mime type variations for CSV.</li>
<li>Enabled the ‘system’ methods for the XML-RPC Server library,
except for ‘system.multicall’ which is still disabled.</li>
</ul>
</li>
<li>Helpers &amp; Plugins<ul>
<li>Added link_tag() to the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML
helper.</span></a></li>
<li>Added img() to the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML helper.</span></a></li>
<li>Added ability to <a class="reference internal" href="general/helpers.html"><span class="doc">“extend” Helpers</span></a>.</li>
<li>Added an <a class="reference internal" href="helpers/email_helper.html"><span class="doc">email helper</span></a> into core
helpers.</li>
<li>Added strip_quotes() function to <a class="reference internal" href="helpers/string_helper.html"><span class="doc">string
helper</span></a>.</li>
<li>Added reduce_multiples() function to <a class="reference internal" href="helpers/string_helper.html"><span class="doc">string
helper</span></a>.</li>
<li>Added quotes_to_entities() function to <a class="reference internal" href="helpers/string_helper.html"><span class="doc">string
helper</span></a>.</li>
<li>Added form_fieldset(), form_fieldset_close(), form_label(),
and form_reset() function to <a class="reference internal" href="helpers/form_helper.html"><span class="doc">form
helper</span></a>.</li>
<li>Added support for external urls in form_open().</li>
<li>Removed support for db_backup in MySQLi due to incompatible
functions.</li>
<li>Javascript Calendar plugin now uses the months and days from the
calendar language file, instead of hard-coded values,
internationalizing it.</li>
</ul>
</li>
<li>Documentation Changes<ul>
<li>Added Writing Documentation section
for the community to use in writing their own documentation.</li>
<li>Added titles to all user manual pages.</li>
<li>Added attributes into &lt;html&gt; of userguide for valid html.</li>
<li>Added <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Encoding Class</span></a>
to the table of contents of the userguide.</li>
<li>Moved part of the userguide menu javascript to an external file.</li>
<li>Documented distinct() in <a class="reference internal" href="database/query_builder.html"><span class="doc">Active
Record</span></a>.</li>
<li>Documented the timezones() function in the <a class="reference internal" href="helpers/date_helper.html"><span class="doc">Date
Helper</span></a>.</li>
<li>Documented unset_userdata in the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session
class</span></a>.</li>
<li>Documented 2 config options to the <a class="reference internal" href="database/configuration.html"><span class="doc">Database
configuration</span></a> page.</li>
</ul>
</li>
</ul>
<div class="section" id="bug-fixes-for-version-1-6-0">
<h3>Bug fixes for Version 1.6.0<a class="headerlink" href="#bug-fixes-for-version-1-6-0" title="Permalink to this headline">¶</a></h3>
<ul class="simple">
<li>Fixed a bug (#1813) preventing using $CI-&gt;db in the same application
with returned database objects.</li>
<li>Fixed a bug (#1842) where the $this-&gt;uri-&gt;rsegments array would not
include the ‘index’ method if routed to the controller without an
implicit method.</li>
<li>Fixed a bug (#1872) where word_limiter() was not retaining
whitespace.</li>
<li>Fixed a bug (#1890) in csv_from_result() where content that
included the delimiter would break the file.</li>
<li>Fixed a bug (#2542)in the clean_email() method of the Email class to
allow for non-numeric / non-sequential array keys.</li>
<li>Fixed a bug (#2545) in _html_entity_decode_callback() when
‘global_xss_filtering’ is enabled.</li>
<li>Fixed a bug (#2668) in the <a class="reference internal" href="libraries/parser.html"><span class="doc">parser class</span></a>
where numeric data was ignored.</li>
<li>Fixed a bug (#2679) where the “previous” pagination link would get
drawn on the first page.</li>
<li>Fixed a bug (#2702) in _object_to_array that broke some types of
inserts and updates.</li>
<li>Fixed a bug (#2732) in the SQLite driver for PHP 4.</li>
<li>Fixed a bug (#2754) in Pagination to scan for non-positive
num_links.</li>
<li>Fixed a bug (#2762) in the <a class="reference internal" href="libraries/sessions.html"><span class="doc">Session
library</span></a> where user agent matching would
fail on user agents ending with a space.</li>
<li>Fixed a bug (#2784) $field_names[] vs $Ffield_names[] in postgres
and sqlite drivers.</li>
<li>Fixed a bug (#2810) in the typography helper causing extraneous
paragraph tags when string contains tags.</li>
<li>Fixed a bug (#2849) where arguments passed to a subfolder controller
method would be incorrectly shifted, dropping the 3rd segment value.</li>
<li>Fixed a bug (#2858) which referenced a wrong variable in the Image
class.</li>
<li>Fixed a bug (#2875)when loading plugin files as _plugin. and not
_pi.</li>
<li>Fixed a bug (#2912) in get_filenames() in the <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File
Helper</span></a> where the array wasn’t cleared
after each call.</li>
<li>Fixed a bug (#2974) in highlight_phrase() that caused an error with
slashes.</li>
<li>Fixed a bug (#3003) in the Encryption Library to support modes other
than MCRYPT_MODE_ECB</li>
<li>Fixed a bug (#3015) in the <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent
library</span></a> where more than 2 languages
where not reported with languages().</li>
<li>Fixed a bug (#3017) in the <a class="reference internal" href="libraries/email.html"><span class="doc">Email</span></a> library
where some timezones were calculated incorrectly.</li>
<li>Fixed a bug (#3024) in which master_dim wasn’t getting reset by
clear() in the Image library.</li>
<li>Fixed a bug (#3156) in Text Helper highlight_code() causing PHP tags
to be handled incorrectly.</li>
<li>Fixed a bug (#3166) that prevented num_rows from working in Oracle.</li>
<li>Fixed a bug (#3175) preventing certain libraries from working
properly when autoloaded in PHP 4.</li>
<li>Fixed a bug (#3267) in the Typography Helper where unordered list was
listed “un.</li>
<li>Fixed a bug (#3268) where the Router could leave ‘/’ as the path.</li>
<li>Fixed a bug (#3279) where the Email class was sending the wrong
Content-Transfer-Encoding for some character sets.</li>
<li>Fixed a bug (#3284) where the rsegment array would not be set
properly if the requested URI contained more segments than the routed
URI.</li>
<li>Removed extraneous load of $CFG in _display_cache() of the Output
class (#3285).</li>
<li>Removed an extraneous call to loading models (#3286).</li>
<li>Fixed a bug (#3310) with sanitization of globals in the Input class
that could unset CI’s global variables.</li>
<li>Fixed a bug (#3314) which would cause the top level path to be
deleted in delete_files() of the File helper.</li>
<li>Fixed a bug (#3328) where the smiley helper might return an undefined
variable.</li>
<li>Fixed a bug (#3330) in the FTP class where a comparison wasn’t
getting made.</li>
<li>Removed an unused parameter from Profiler (#3332).</li>
<li>Fixed a bug in database driver where num_rows property wasn’t
getting updated.</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">upload
library</span></a> when allowed_files
wasn’t defined.</li>
<li>Fixed a bug in word_wrap() of the Text Helper that incorrectly
referenced an object.</li>
<li>Fixed a bug in Validation where valid_ip() wasn’t called properly.</li>
<li>Fixed a bug in Validation where individual error messages for
checkboxes wasn’t supported.</li>
<li>Fixed a bug in captcha calling an invalid PHP function.</li>
<li>Fixed a bug in the cookie helper “set_cookie” function. It was not
honoring the config settings.</li>
<li>Fixed a bug that was making validation callbacks required even when
not set as such.</li>
<li>Fixed a bug in the XML-RPC library so if a type is specified, a more
intelligent decision is made as to the default type.</li>
<li>Fixed an example of comma-separated emails in the email library
documentation.</li>
<li>Fixed an example in the Calendar library for Showing Next/Previous
Month Links.</li>
<li>Fixed a typo in the database language file.</li>
<li>Fixed a typo in the image language file “suppor” to “support”.</li>
<li>Fixed an example for XML RPC.</li>
<li>Fixed an example of accept_charset() in the <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent
Library</span></a>.</li>
<li>Fixed a typo in the docblock comments that had CodeIgniter spelled
CodeIgnitor.</li>
<li>Fixed a typo in the <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String Helper</span></a>
(uniquid changed to uniqid).</li>
<li>Fixed typos in the email Language class
(email_attachment_unredable, email_filed_smtp_login), and FTP
Class (ftp_unable_to_remame).</li>
<li>Added a stripslashes() into the Upload Library.</li>
<li>Fixed a series of grammatical and spelling errors in the language
files.</li>
<li>Fixed assorted user guide typos.</li>
</ul>
</div>
</div>
<div class="section" id="version-1-5-4">
<h2>Version 1.5.4<a class="headerlink" href="#version-1-5-4" title="Permalink to this headline">¶</a></h2>
<p>Release Date: July 12, 2007</p>
<ul class="simple">
<li>Added <a class="reference internal" href="libraries/language.html"><span class="doc">custom Language files</span></a> to the
<a class="reference internal" href="general/autoloader.html"><span class="doc">autoload</span></a> options.</li>
<li>Added stripslashes() to the _clean_input_data() function in the
<a class="reference internal" href="libraries/input.html"><span class="doc">Input class</span></a> when magic quotes is on so
that data will always be un-slashed within the framework.</li>
<li>Added array to string into the <a class="reference internal" href="general/profiling.html"><span class="doc">profiler</span></a>.</li>
<li>Added some additional mime types in application/config/mimes.php.</li>
<li>Added filename_security() method to <a class="reference internal" href="libraries/input.html"><span class="doc">Input
library</span></a>.</li>
<li>Added some additional arguments to the <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflection
helper</span></a> singular() to compensate
for words ending in “s”. Also added a force parameter to pluralize().</li>
<li>Added $config[‘charset’] to the config file. Default value is
‘UTF-8’, used in some string handling functions.</li>
<li>Fixed MSSQL insert_id().</li>
<li>Fixed a logic error in the DB trans_status() function. It was
incorrectly returning TRUE on failure and FALSE on success.</li>
<li>Fixed a bug that was allowing multiple load attempts on extended
classes.</li>
<li>Fixed a bug in the bootstrap file that was incorrectly attempting to
discern the full server path even when it was explicity set by the
user.</li>
<li>Fixed a bug in the escape_str() function in the MySQL driver.</li>
<li>Fixed a typo in the <a class="reference internal" href="libraries/calendar.html"><span class="doc">Calendar library</span></a></li>
<li>Fixed a typo in rpcs.php library</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip library</span></a>, providing
PC Zip file compatibility with Mac OS X</li>
<li>Fixed a bug in router that was ignoring the scaffolding route for
optimization</li>
<li>Fixed an IP validation bug.</li>
<li>Fixed a bug in display of POST keys in the
<a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a> output</li>
<li>Fixed a bug in display of queries with characters that would be
interpreted as HTML in the <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler</span></a>
output</li>
<li>Fixed a bug in display of Email class print debugger with characters
that would be interpreted as HTML in the debugging output</li>
<li>Fixed a bug in the Content-Transfer-Encoding of HTML emails with the
quoted-printable MIME type</li>
<li>Fixed a bug where one could unset certain PHP superglobals by setting
them via GET or POST data</li>
<li>Fixed an undefined function error in the insert_id() function of the
PostgreSQL driver</li>
<li>Fixed various doc typos.</li>
<li>Documented two functions from the <a class="reference internal" href="helpers/string_helper.html"><span class="doc">String
helper</span></a> that were missing from the
user guide: trim_slashes() and reduce_double_slashes().</li>
<li>Docs now validate to XHTML 1 transitional</li>
<li>Updated the XSS Filtering to take into account the IE expression()
ability and improved certain deletions to prevent possible exploits</li>
<li>Modified the Router so that when Query Strings are Enabled, the
controller trigger and function trigger values are sanitized for
filename include security.</li>
<li>Modified the is_image() method in the Upload library to take into
account Windows IE 6/7 eccentricities when dealing with MIMEs</li>
<li>Modified XSS Cleaning routine to be more performance friendly and
compatible with PHP 5.2’s new PCRE backtrack and recursion limits.</li>
<li>Modified the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helper</span></a> to type cast
the $title as a string in case a numeric value is supplied</li>
<li>Modified Form Helper form_dropdown() to type cast the keys and
values of the options array as strings, allowing numeric values to be
properly set as ‘selected’</li>
<li>Deprecated the use if is_numeric() in various places since it allows
periods. Due to compatibility problems with ctype_digit(), making it
unreliable in some installations, the following regular expression
was used instead: preg_match(“/[^0-9]/”, $n)</li>
<li>Deprecated: APPVER has been deprecated and replaced with CI_VERSION
for clarity.</li>
</ul>
</div>
<div class="section" id="version-1-5-3">
<h2>Version 1.5.3<a class="headerlink" href="#version-1-5-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 15, 2007</p>
<ul class="simple">
<li>Added array to string into the profiler</li>
<li>Code Igniter references updated to CodeIgniter</li>
<li>pMachine references updated to EllisLab</li>
<li>Fixed a bug in the repeater function of <a class="reference internal" href="helpers/string_helper.html"><span class="doc">string
helper</span></a>.</li>
<li>Fixed a bug in ODBC driver</li>
<li>Fixed a bug in result_array() that was returning an empty array when
no result is produced.</li>
<li>Fixed a bug in the redirect function of the <a class="reference internal" href="helpers/url_helper.html"><span class="doc">url
helper</span></a>.</li>
<li>Fixed an undefined variable in Loader</li>
<li>Fixed a version bug in the Postgres driver</li>
<li>Fixed a bug in the textarea function of the form helper for use with
strings</li>
<li>Fixed doc typos.</li>
</ul>
</div>
<div class="section" id="version-1-5-2">
<h2>Version 1.5.2<a class="headerlink" href="#version-1-5-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: February 13, 2007</p>
<ul class="simple">
<li>Added subversion information
to the <a class="reference internal" href="installation/downloads.html"><span class="doc">downloads</span></a> page.</li>
<li>Added support for captions in the <a class="reference internal" href="libraries/table.html"><span class="doc">Table
Library</span></a></li>
<li>Fixed a bug in the
<a class="reference internal" href="helpers/download_helper.html"><span class="doc">download_helper</span></a> that was causing
Internet Explorer to load rather than download</li>
<li>Fixed a bug in the Active Record Join function that was not taking
table prefixes into consideration.</li>
<li>Removed unescaped variables in error messages of Input and Router
classes</li>
<li>Fixed a bug in the Loader that was causing errors on Libraries loaded
twice. A debug message is now silently made in the log.</li>
<li>Fixed a bug in the <a class="reference internal" href="helpers/form_helper.html"><span class="doc">form helper</span></a> that
gave textarea a value attribute</li>
<li>Fixed a bug in the <a class="reference internal" href="libraries/image_lib.html"><span class="doc">Image Library</span></a> that
was ignoring resizing the same size image</li>
<li>Fixed some doc typos.</li>
</ul>
</div>
<div class="section" id="version-1-5-1">
<h2>Version 1.5.1<a class="headerlink" href="#version-1-5-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: November 23, 2006</p>
<ul class="simple">
<li>Added support for submitting arrays of libraries in the
$this-&gt;load-&gt;library function.</li>
<li>Added support for naming custom library files in lower or uppercase.</li>
<li>Fixed a bug related to output buffering.</li>
<li>Fixed a bug in the active record class that was not resetting query
data after a completed query.</li>
<li>Fixed a bug that was suppressing errors in controllers.</li>
<li>Fixed a problem that can cause a loop to occur when the config file
is missing.</li>
<li>Fixed a bug that occurred when multiple models were loaded with the
third parameter set to TRUE.</li>
<li>Fixed an oversight that was not unsetting globals properly in the
input sanitize function.</li>
<li>Fixed some bugs in the Oracle DB driver.</li>
<li>Fixed an incorrectly named variable in the MySQLi result driver.</li>
<li>Fixed some doc typos.</li>
</ul>
</div>
<div class="section" id="version-1-5-0-1">
<h2>Version *******<a class="headerlink" href="#version-1-5-0-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 31, 2006</p>
<ul class="simple">
<li>Fixed a problem in which duplicate attempts to load helpers and
classes were not being stopped.</li>
<li>Fixed a bug in the word_wrap() helper function.</li>
<li>Fixed an invalid color Hex number in the Profiler class.</li>
<li>Fixed a corrupted image in the user guide.</li>
</ul>
</div>
<div class="section" id="version-1-5-0">
<h2>Version 1.5.0<a class="headerlink" href="#version-1-5-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: October 30, 2006</p>
<ul class="simple">
<li>Added <a class="reference internal" href="database/utilities.html"><span class="doc">DB utility class</span></a>, permitting DB
backups, CVS or XML files from DB results, and various other
functions.</li>
<li>Added <a class="reference internal" href="database/caching.html"><span class="doc">Database Caching Class</span></a>.</li>
<li>Added <a class="reference internal" href="database/transactions.html"><span class="doc">transaction support</span></a> to the
database classes.</li>
<li>Added <a class="reference internal" href="general/profiling.html"><span class="doc">Profiler Class</span></a> which generates a
report of Benchmark execution times, queries, and POST data at the
bottom of your pages.</li>
<li>Added <a class="reference internal" href="libraries/user_agent.html"><span class="doc">User Agent Library</span></a> which
allows browsers, robots, and mobile devises to be identified.</li>
<li>Added <a class="reference internal" href="libraries/table.html"><span class="doc">HTML Table Class</span></a> , enabling tables
to be generated from arrays or database results.</li>
<li>Added <a class="reference internal" href="libraries/zip.html"><span class="doc">Zip Encoding Library</span></a>.</li>
<li>Added <a class="reference internal" href="libraries/ftp.html"><span class="doc">FTP Library</span></a>.</li>
<li>Added the ability to <a class="reference internal" href="general/creating_libraries.html"><span class="doc">extend
libraries</span></a> and <a class="reference internal" href="general/core_classes.html"><span class="doc">extend core
classes</span></a>, in addition to being able to
replace them.</li>
<li>Added support for storing <a class="reference internal" href="general/models.html"><span class="doc">models within
sub-folders</span></a>.</li>
<li>Added <a class="reference internal" href="helpers/download_helper.html"><span class="doc">Download Helper</span></a>.</li>
<li>Added <a class="reference internal" href="database/queries.html"><span class="doc">simple_query()</span></a> function to the
database classes</li>
<li>Added <a class="reference internal" href="helpers/date_helper.html"><span class="doc">standard_date()</span></a> function to
the Date Helper.</li>
<li>Added <a class="reference internal" href="database/results.html"><span class="doc">$query-&gt;free_result()</span></a> to database
class.</li>
<li>Added <a class="reference internal" href="database/metadata.html"><span class="doc">$query-&gt;list_fields()</span></a> function to
database class</li>
<li>Added <a class="reference internal" href="database/helpers.html"><span class="doc">$this-&gt;db-&gt;platform()</span></a> function</li>
<li>Added new <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File Helper</span></a>:
get_filenames()</li>
<li>Added new helper: <a class="reference internal" href="helpers/smiley_helper.html"><span class="doc">Smiley Helper</span></a></li>
<li>Added support for &lt;ul&gt; and &lt;ol&gt; lists in the <a class="reference internal" href="helpers/html_helper.html"><span class="doc">HTML
Helper</span></a></li>
<li>Added the ability to rewrite <a class="reference internal" href="general/alternative_php.html"><span class="doc">short
tags</span></a> on-the-fly, converting them
to standard PHP statements, for those servers that do not support
short tags. This allows the cleaner syntax to be used regardless of
whether it’s supported by the server.</li>
<li>Added the ability to <a class="reference internal" href="general/managing_apps.html"><span class="doc">rename or relocate the “application”
folder</span></a>.</li>
<li>Added more thorough initialization in the upload class so that all
class variables are reset.</li>
<li>Added “is_numeric” to validation, which uses the native PHP
is_numeric function.</li>
<li>Improved the URI handler to make it more reliable when the
$config[‘uri_protocol’] item is set to AUTO.</li>
<li>Moved most of the functions in the Controller class into the Loader
class, allowing fewer reserved function names for controllers when
running under PHP 5.</li>
<li>Updated the DB Result class to return an empty array when
$query-&gt;result() doesn’t produce a result.</li>
<li>Updated the input-&gt;cookie() and input-&gt;post() functions in <a class="reference internal" href="libraries/input.html"><span class="doc">Input
Class</span></a> to permit arrays contained cookies
that are arrays to be run through the XSS filter.</li>
<li>Documented three functions from the Validation
class that were missing from the user
guide: set_select(), set_radio(), and set_checkbox().</li>
<li>Fixed a bug in the Email class related to SMTP Helo data.</li>
<li>Fixed a bug in the word wrapping helper and function in the email
class.</li>
<li>Fixed a bug in the validation class.</li>
<li>Fixed a bug in the typography helper that was incorrectly wrapping
block level elements in paragraph tags.</li>
<li>Fixed a problem in the form_prep() function that was double encoding
entities.</li>
<li>Fixed a bug that affects some versions of PHP when output buffering
is nested.</li>
<li>Fixed a bug that caused CI to stop working when the PHP magic
__get() or __set() functions were used within models or
controllers.</li>
<li>Fixed a pagination bug that was permitting negative values in the
URL.</li>
<li>Fixed an oversight in which the Loader class was not allowed to be
extended.</li>
<li>Changed _get_config() to get_config() since the function is not a
private one.</li>
<li><strong>Deprecated “init” folder</strong>. Initialization happens automatically
now. <a class="reference internal" href="general/creating_libraries.html"><span class="doc">Please see documentation</span></a>.</li>
<li><strong>Deprecated</strong> $this-&gt;db-&gt;field_names() USE
$this-&gt;db-&gt;list_fields()</li>
<li><strong>Deprecated</strong> the $config[‘log_errors’] item from the config.php
file. Instead, $config[‘log_threshold’] can be set to “0” to turn it
off.</li>
</ul>
</div>
<div class="section" id="version-1-4-1">
<h2>Version 1.4.1<a class="headerlink" href="#version-1-4-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: September 21, 2006</p>
<ul class="simple">
<li>Added a new feature that passes URI segments directly to your
function calls as parameters. See the
<a class="reference internal" href="general/controllers.html"><span class="doc">Controllers</span></a> page for more info.</li>
<li>Added support for a function named _output(), which when used in
your controllers will received the final rendered output from the
output class. More info in the <a class="reference internal" href="general/controllers.html"><span class="doc">Controllers</span></a>
page.</li>
<li>Added several new functions in the <a class="reference internal" href="libraries/uri.html"><span class="doc">URI
Class</span></a> to let you retrieve and manipulate URI
segments that have been re-routed using the <a class="reference internal" href="general/routing.html"><span class="doc">URI
Routing</span></a> feature. Previously, the URI class did not
permit you to access any re-routed URI segments, but now it does.</li>
<li>Added <a class="reference internal" href="libraries/output.html"><span class="doc">$this-&gt;output-&gt;set_header()</span></a>
function, which allows you to set server headers.</li>
<li>Updated plugins, helpers, and language classes to allow your
application folder to contain its own plugins, helpers, and language
folders. Previously they were always treated as global for your
entire installation. If your application folder contains any of these
resources they will be used <em>instead</em> the global ones.</li>
<li>Added <a class="reference internal" href="helpers/inflector_helper.html"><span class="doc">Inflector helper</span></a>.</li>
<li>Added element() function in the <a class="reference internal" href="helpers/array_helper.html"><span class="doc">array
helper</span></a>.</li>
<li>Added RAND() to active record orderby() function.</li>
<li>Added delete_cookie() and get_cookie() to <a class="reference internal" href="helpers/cookie_helper.html"><span class="doc">Cookie
helper</span></a>, even though the input class
has a cookie fetching function.</li>
<li>Added Oracle database driver (still undergoing testing so it might
have some bugs).</li>
<li>Added the ability to combine pseudo-variables and php variables in
the template parser class.</li>
<li>Added output compression option to the config file.</li>
<li>Removed the is_numeric test from the db-&gt;escape() function.</li>
<li>Fixed a MySQLi bug that was causing error messages not to contain
proper error data.</li>
<li>Fixed a bug in the email class which was causing it to ignore
explicitly set alternative headers.</li>
<li>Fixed a bug that was causing a PHP error when the Exceptions class
was called within the get_config() function since it was causing
problems.</li>
<li>Fixed an oversight in the cookie helper in which the config file
cookie settings were not being honored.</li>
<li>Fixed an oversight in the upload class. An item mentioned in the 1.4
changelog was missing.</li>
<li>Added some code to allow email attachments to be reset when sending
batches of email.</li>
<li>Deprecated the application/scripts folder. It will continue to work
for legacy users, but it is recommended that you create your own
<a class="reference internal" href="general/libraries.html"><span class="doc">libraries</span></a> or
<a class="reference internal" href="general/models.html"><span class="doc">models</span></a> instead. It was originally added
before CI had user libraries or models, but it’s not needed anymore.</li>
<li>Deprecated the $autoload[‘core’] item from the autoload.php file.
Instead, please now use: $autoload[‘libraries’]</li>
<li>Deprecated the following database functions:
$this-&gt;db-&gt;smart_escape_str() and $this-&gt;db-&gt;fields().</li>
</ul>
</div>
<div class="section" id="version-1-4-0">
<h2>Version 1.4.0<a class="headerlink" href="#version-1-4-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: September 17, 2006</p>
<ul class="simple">
<li>Added <a class="reference internal" href="general/hooks.html"><span class="doc">Hooks</span></a> feature, enabling you to tap
into and modify the inner workings of the framework without hacking
the core files.</li>
<li>Added the ability to organize controller files <a class="reference internal" href="general/controllers.html"><span class="doc">into
sub-folders</span></a>. Kudos to Marco for
suggesting this (and the next two) feature.</li>
<li>Added regular expressions support for <a class="reference internal" href="general/routing.html"><span class="doc">routing
rules</span></a>.</li>
<li>Added the ability to <a class="reference internal" href="general/controllers.html"><span class="doc">remap function
calls</span></a> within your controllers.</li>
<li>Added the ability to <a class="reference internal" href="general/core_classes.html"><span class="doc">replace core system
classes</span></a> with your own classes.</li>
<li>Added support for % character in URL.</li>
<li>Added the ability to supply full URLs using the
<a class="reference internal" href="helpers/url_helper.html"><span class="doc">anchor()</span></a> helper function.</li>
<li>Added mode parameter to <a class="reference internal" href="helpers/file_helper.html"><span class="doc">file_write()</span></a>
helper.</li>
<li>Added support for changing the port number in the <a class="reference internal" href="database/configuration.html"><span class="doc">Postgres
driver</span></a>.</li>
<li>Moved the list of “allowed URI characters” out of the Router class
and into the config file.</li>
<li>Moved the MIME type array out of the Upload class and into its own
file in the application/config/ folder.</li>
<li>Updated the Upload class to allow the upload field name to be set
when calling <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">do_upload()</span></a>.</li>
<li>Updated the <a class="reference internal" href="libraries/config.html"><span class="doc">Config Library</span></a> to be able to
load config files silently, and to be able to assign config files to
their own index (to avoid collisions if you use multiple config
files).</li>
<li>Updated the URI Protocol code to allow more options so that URLs will
work more reliably in different environments.</li>
<li>Updated the form_open() helper to allow the GET method to be used.</li>
<li>Updated the MySQLi execute() function with some code to help prevent
lost connection errors.</li>
<li>Updated the SQLite Driver to check for object support before
attempting to return results as objects. If unsupported it returns an
array.</li>
<li>Updated the Models loader function to allow multiple loads of the
same model.</li>
<li>Updated the MS SQL driver so that single quotes are escaped.</li>
<li>Updated the Postgres and ODBC drivers for better compatibility.</li>
<li>Removed a strtolower() call that was changing URL segments to lower
case.</li>
<li>Removed some references that were interfering with PHP 4.4.1
compatibility.</li>
<li>Removed backticks from Postgres class since these are not needed.</li>
<li>Renamed display() to _display() in the Output class to make it clear
that it’s a private function.</li>
<li>Deprecated the hash() function due to a naming conflict with a native
PHP function with the same name. Please use dohash() instead.</li>
<li>Fixed an bug that was preventing the input class from unsetting GET
variables.</li>
<li>Fixed a router bug that was making it too greedy when matching end
segments.</li>
<li>Fixed a bug that was preventing multiple discrete database calls.</li>
<li>Fixed a bug in which loading a language file was producing a “file
contains no data” message.</li>
<li>Fixed a session bug caused by the XSS Filtering feature inadvertently
changing the case of certain words.</li>
<li>Fixed some missing prefixes when using the database prefix feature.</li>
<li>Fixed a typo in the Calendar class (cal_november).</li>
<li>Fixed a bug in the form_checkbox() helper.</li>
<li>Fixed a bug that was allowing the second segment of the URI to be
identical to the class name.</li>
<li>Fixed an evaluation bug in the database initialization function.</li>
<li>Fixed a minor bug in one of the error messages in the language class.</li>
<li>Fixed a bug in the date helper timespan function.</li>
<li>Fixed an undefined variable in the DB Driver class.</li>
<li>Fixed a bug in which dollar signs used as binding replacement values
in the DB class would be treated as RegEx back-references.</li>
<li>Fixed a bug in the set_hash() function which was preventing MD5 from
being used.</li>
<li>Fixed a couple bugs in the Unit Testing class.</li>
<li>Fixed an incorrectly named variable in the Validation class.</li>
<li>Fixed an incorrectly named variable in the URI class.</li>
<li>Fixed a bug in the config class that was preventing the base URL from
being called properly.</li>
<li>Fixed a bug in the validation class that was not permitting callbacks
if the form field was empty.</li>
<li>Fixed a problem that was preventing scaffolding from working properly
with MySQLi.</li>
<li>Fixed some MS SQL bugs.</li>
<li>Fixed some doc typos.</li>
</ul>
</div>
<div class="section" id="version-1-3-3">
<h2>Version 1.3.3<a class="headerlink" href="#version-1-3-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: June 1, 2006</p>
<ul class="simple">
<li>Models do <strong>not</strong> connect automatically to the database as of this
version. <a class="reference internal" href="general/models.html"><span class="doc">More info here</span></a>.</li>
<li>Updated the Sessions class to utilize the active record class when
running session related queries. Previously the queries assumed MySQL
syntax.</li>
<li>Updated alternator() function to re-initialize when called with no
arguments, allowing multiple calls.</li>
<li>Fixed a bug in the active record “having” function.</li>
<li>Fixed a problem in the validation class which was making checkboxes
be ignored when required.</li>
<li>Fixed a bug in the word_limiter() helper function. It was cutting
off the fist word.</li>
<li>Fixed a bug in the xss_clean function due to a PHP bug that affects
some versions of html_entity_decode.</li>
<li>Fixed a validation bug that was preventing rules from being set twice
in one controller.</li>
<li>Fixed a calendar bug that was not letting it use dynamically loaded
languages.</li>
<li>Fixed a bug in the active record class when using WHERE clauses with
LIKE</li>
<li>Fixed a bug in the hash() security helper.</li>
<li>Fixed some typos.</li>
</ul>
</div>
<div class="section" id="version-1-3-2">
<h2>Version 1.3.2<a class="headerlink" href="#version-1-3-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 17, 2006</p>
<ul class="simple">
<li>Changed the behavior of the validation class such that if a
“required” rule is NOT explicitly stated for a field then all other
tests get ignored.</li>
<li>Fixed a bug in the Controller class that was causing it to look in
the local “init” folder instead of the main system one.</li>
<li>Fixed a bug in the init_pagination file. The $config item was not
being set correctly.</li>
<li>Fixed a bug in the auto typography helper that was causing
inconsistent behavior.</li>
<li>Fixed a couple bugs in the Model class.</li>
<li>Fixed some documentation typos and errata.</li>
</ul>
</div>
<div class="section" id="version-1-3-1">
<h2>Version 1.3.1<a class="headerlink" href="#version-1-3-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 11, 2006</p>
<ul class="simple">
<li>Added a <a class="reference internal" href="libraries/unit_testing.html"><span class="doc">Unit Testing Library</span></a>.</li>
<li>Added the ability to pass objects to the <strong>insert()</strong> and
<strong>update()</strong> database functions. This feature enables you to (among
other things) use your <a class="reference internal" href="general/models.html"><span class="doc">Model class</span></a>
variables to run queries with. See the Models page for details.</li>
<li>Added the ability to pass objects to the <a class="reference internal" href="general/views.html"><span class="doc">view loading
function</span></a>: $this-&gt;load-&gt;view(‘my_view’,
$object);</li>
<li>Added getwhere function to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record
class</span></a>.</li>
<li>Added count_all function to <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record
class</span></a>.</li>
<li>Added language file for scaffolding and fixed a scaffolding bug that
occurs when there are no rows in the specified table.</li>
<li>Added <a class="reference internal" href="database/queries.html"><span class="doc">$this-&gt;db-&gt;last_query()</span></a>, which
allows you to view your last query that was run.</li>
<li>Added a new mime type to the upload class for better compatibility.</li>
<li>Changed how cache files are read to prevent PHP errors if the cache
file contains an XML tag, which PHP wants to interpret as a short
tag.</li>
<li>Fixed a bug in a couple of the active record functions (where and
orderby).</li>
<li>Fixed a bug in the image library when realpath() returns false.</li>
<li>Fixed a bug in the Models that was preventing libraries from being
used within them.</li>
<li>Fixed a bug in the “exact_length” function of the validation class.</li>
<li>Fixed some typos in the user guide</li>
</ul>
</div>
<div class="section" id="version-1-3">
<h2>Version 1.3<a class="headerlink" href="#version-1-3" title="Permalink to this headline">¶</a></h2>
<p>Release Date: April 3, 2006</p>
<ul class="simple">
<li>Added support for <a class="reference internal" href="general/models.html"><span class="doc">Models</span></a>.</li>
<li>Redesigned the database libraries to support additional RDBMs
(Postgres, MySQLi, etc.).</li>
<li>Redesigned the <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record class</span></a>
to enable more varied types of queries with simpler syntax, and
advanced features like JOINs.</li>
<li>Added a feature to the database class that lets you run <a class="reference internal" href="database/call_function.html"><span class="doc">custom
function calls</span></a>.</li>
<li>Added support for <a class="reference internal" href="general/controllers.html"><span class="doc">private functions</span></a> in your
controllers. Any controller function name that starts with an
underscore will not be served by a URI request.</li>
<li>Added the ability to pass your own initialization parameters to your
<a class="reference internal" href="general/creating_libraries.html"><span class="doc">custom core libraries</span></a> when using
$this-&gt;load-&gt;library()</li>
<li>Added support for running standard <a class="reference internal" href="general/urls.html"><span class="doc">query string URLs</span></a>.
These can be optionally enabled in your config file.</li>
<li>Added the ability to <a class="reference internal" href="general/urls.html"><span class="doc">specify a “suffix”</span></a>, which will be
appended to your URLs. For example, you could add .html to your URLs,
making them appear static. This feature is enabled in your config
file.</li>
<li>Added a new error template for use with native PHP errors.</li>
<li>Added “alternator” function in the <a class="reference internal" href="helpers/string_helper.html"><span class="doc">string
helpers</span></a>.</li>
<li>Removed slashing from the input class. After much debate we decided
to kill this feature.</li>
<li>Change the commenting style in the scripts to the PEAR standard so
that IDEs and tools like phpDocumenter can harvest the comments.</li>
<li>Added better class and function name-spacing to avoid collisions with
user developed classes. All CodeIgniter classes are now prefixed with
CI_ and all controller methods are prefixed with _ci to avoid
controller collisions. A list of reserved function names can be
<a class="reference internal" href="general/controllers.html"><span class="doc">found here</span></a>.</li>
<li>Redesigned how the “CI” super object is referenced, depending on
whether PHP 4 or 5 is being run, since PHP 5 allows a more graceful
way to manage objects that utilizes a bit less resources.</li>
<li>Deprecated: $this-&gt;db-&gt;use_table() has been deprecated. Please read
the <a class="reference internal" href="database/query_builder.html"><span class="doc">Active Record</span></a> page for
information.</li>
<li>Deprecated: $this-&gt;db-&gt;smart_escape_str() has been deprecated.
Please use this instead: $this-&gt;db-&gt;escape()</li>
<li>Fixed a bug in the exception handler which was preventing some PHP
errors from showing up.</li>
<li>Fixed a typo in the URI class. $this-&gt;total_segment() should be
plural: $this-&gt;total_segments()</li>
<li>Fixed some typos in the default calendar template</li>
<li>Fixed some typos in the user guide</li>
</ul>
</div>
<div class="section" id="version-1-2">
<h2>Version 1.2<a class="headerlink" href="#version-1-2" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 21, 2006</p>
<ul class="simple">
<li>Redesigned some internal aspects of the framework to resolve scoping
problems that surfaced during the beta tests. The problem was most
notable when instantiating classes in your constructors, particularly
if those classes in turn did work in their constructors.</li>
<li>Added a global function named
<a class="reference internal" href="general/ancillary_classes.html"><span class="doc">get_instance()</span></a> allowing the main
CodeIgniter object to be accessible throughout your own classes.</li>
<li>Added new <a class="reference internal" href="helpers/file_helper.html"><span class="doc">File Helper</span></a>:
delete_files()</li>
<li>Added new <a class="reference internal" href="helpers/url_helper.html"><span class="doc">URL Helpers</span></a>: base_url(),
index_page()</li>
<li>Added the ability to create your own <a class="reference internal" href="general/creating_libraries.html"><span class="doc">core
libraries</span></a> and store them in your local
application directory.</li>
<li>Added an overwrite option to the <a class="reference internal" href="libraries/file_uploading.html"><span class="doc">Upload
class</span></a>, enabling files to be
overwritten rather than having the file name appended.</li>
<li>Added Javascript Calendar plugin.</li>
<li>Added search feature to user guide. Note: This is done using Google,
which at the time of this writing has not crawled all the pages of
the docs.</li>
<li>Updated the parser class so that it allows tag pars within other tag
pairs.</li>
<li>Fixed a bug in the DB “where” function.</li>
<li>Fixed a bug that was preventing custom config files to be
auto-loaded.</li>
<li>Fixed a bug in the mysql class bind feature that prevented question
marks in the replacement data.</li>
<li>Fixed some bugs in the xss_clean function</li>
</ul>
</div>
<div class="section" id="version-beta-1-1">
<h2>Version Beta 1.1<a class="headerlink" href="#version-beta-1-1" title="Permalink to this headline">¶</a></h2>
<p>Release Date: March 10, 2006</p>
<ul class="simple">
<li>Added a <a class="reference internal" href="libraries/calendar.html"><span class="doc">Calendaring class</span></a>.</li>
<li>Added support for running <a class="reference internal" href="general/managing_apps.html"><span class="doc">multiple
applications</span></a> that share a common CodeIgniter
backend.</li>
<li>Moved the “uri protocol” variable from the index.php file into the
config.php file</li>
<li>Fixed a problem that was preventing certain function calls from
working within constructors.</li>
<li>Fixed a problem that was preventing the $this-&gt;load-&gt;library function
from working in constructors.</li>
<li>Fixed a bug that occurred when the session class was loaded using the
auto-load routine.</li>
<li>Fixed a bug that can happen with PHP versions that do not support the
E_STRICT constant</li>
<li>Fixed a data type error in the form_radio function (form helper)</li>
<li>Fixed a bug that was preventing the xss_clean function from being
called from the validation class.</li>
<li>Fixed the cookie related config names, which were incorrectly
specified as $conf rather than $config</li>
<li>Fixed a pagination problem in the scaffolding.</li>
<li>Fixed a bug in the mysql class “where” function.</li>
<li>Fixed a regex problem in some code that trimmed duplicate slashes.</li>
<li>Fixed a bug in the br() function in the HTML helper</li>
<li>Fixed a syntax mistake in the form_dropdown function in the Form
Helper.</li>
<li>Removed the “style” attributes form the form helpers.</li>
<li>Updated the documentation. Added “next/previous” links to each page
and fixed various typos.</li>
</ul>
</div>
<div class="section" id="version-beta-1-0">
<h2>Version Beta 1.0<a class="headerlink" href="#version-beta-1-0" title="Permalink to this headline">¶</a></h2>
<p>Release Date: February 28, 2006</p>
<p>First publicly released version.</p>
</div>
</div>


          </div>
          <footer>
  
    <div class="rst-footer-buttons" role="navigation" aria-label="footer navigation">
      
        <a href="license.html" class="btn btn-neutral float-right" title="The MIT License (MIT)">Next <span class="fa fa-arrow-circle-right"></span></a>
      
      
        <a href="helpers/xml_helper.html" class="btn btn-neutral" title="XML Helper"><span class="fa fa-arrow-circle-left"></span> Previous</a>
      
    </div>
  

  <hr/>

  <div role="contentinfo">
    <p>
        &copy; Copyright 2014 - 2019, British Columbia Institute of Technology.
      Last updated on Sep 19, 2019.
    </p>
  </div>

  Built with <a href="http://sphinx-doc.org/">Sphinx</a> using a <a href="https://github.com/snide/sphinx_rtd_theme">theme</a> provided by <a href="https://readthedocs.org">Read the Docs</a>.
  
</footer>
        </div>
      </div>

    </section>

  </div>
  


  

    <script type="text/javascript">
        var DOCUMENTATION_OPTIONS = {
            URL_ROOT:'./',
            VERSION:'3.1.11',
            COLLAPSE_INDEX:false,
            FILE_SUFFIX:'.html',
            HAS_SOURCE:  false
        };
    </script>
      <script type="text/javascript" src="_static/jquery.js"></script>
      <script type="text/javascript" src="_static/underscore.js"></script>
      <script type="text/javascript" src="_static/doctools.js"></script>

  

  
  
    <script type="text/javascript" src="_static/js/theme.js"></script>
  

  
  
  <script type="text/javascript">
      jQuery(function () {
          SphinxRtdTheme.StickyNav.enable();
      });
  </script>
   

</body>
</html>