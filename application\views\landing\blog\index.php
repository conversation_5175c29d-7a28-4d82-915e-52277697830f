<?php $this->load->view('landing/header'); ?>

<!-- Blog Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1>Our Blog</h1>
                <p class="lead">Stay updated with the latest tips, tutorials, and insights about email hosting, web development, and business growth.</p>
                
                <!-- Search Form -->
                <div class="blog-search mt-4">
                    <form action="<?php echo base_url('blog/search'); ?>" method="get" class="search-form">
                        <div class="input-group">
                            <input type="text" name="q" class="form-control" placeholder="Search blog posts..." value="<?php echo isset($search_query) ? $search_query : ''; ?>">
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Blog Content Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (!empty($posts)): ?>
                    <div class="row g-4">
                        <?php foreach ($posts as $post): ?>
                            <div class="col-md-6">
                                <article class="blog-card">
                                    <?php if ($post->featured_image): ?>
                                        <div class="blog-card-image">
                                            <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" alt="<?php echo htmlspecialchars($post->title); ?>" class="img-fluid">
                                            <div class="blog-card-overlay">
                                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="btn btn-light btn-sm">
                                                    <i class="fas fa-arrow-right me-1"></i>Read More
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-content">
                                        <div class="blog-card-meta">
                                            <?php if ($post->category_name): ?>
                                                <a href="<?php echo base_url('blog/category/' . $post->category_slug); ?>" class="category-link">
                                                    <?php echo htmlspecialchars($post->category_name); ?>
                                                </a>
                                            <?php endif; ?>
                                            <span class="date">
                                                <i class="fas fa-calendar-alt me-1"></i>
                                                <?php echo date('M j, Y', strtotime($post->published_at)); ?>
                                            </span>
                                        </div>
                                        
                                        <h3 class="blog-card-title">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>">
                                                <?php echo htmlspecialchars($post->title); ?>
                                            </a>
                                        </h3>
                                        
                                        <p class="blog-card-excerpt">
                                            <?php echo $post->excerpt ? htmlspecialchars($post->excerpt) : word_limiter(strip_tags($post->content), 20); ?>
                                        </p>
                                        
                                        <div class="blog-card-footer">
                                            <div class="author-info">
                                                <i class="fas fa-user me-1"></i>
                                                <?php echo htmlspecialchars($post->author_name); ?>
                                            </div>
                                            <div class="post-stats">
                                                <span class="views">
                                                    <i class="fas fa-eye me-1"></i>
                                                    <?php echo number_format($post->views); ?>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Pagination -->
                    <?php if ($pagination): ?>
                        <div class="mt-5">
                            <?php echo $pagination; ?>
                        </div>
                    <?php endif; ?>
                    
                <?php else: ?>
                    <div class="text-center py-5">
                        <div class="feature-icon mx-auto mb-4" style="width: 100px; height: 100px;">
                            <i class="fas fa-blog fa-3x"></i>
                        </div>
                        <h3>No Blog Posts Yet</h3>
                        <p class="text-muted">We're working on creating amazing content for you. Check back soon!</p>
                    </div>
                <?php endif; ?>
            </div>
            
            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php $this->load->view('landing/blog/sidebar'); ?>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h3>Stay Updated</h3>
                <p class="mb-0">Subscribe to our newsletter and get the latest blog posts delivered to your inbox.</p>
            </div>
            <div class="col-lg-4">
                <form class="newsletter-form">
                    <div class="input-group">
                        <input type="email" class="form-control" placeholder="Enter your email">
                        <button type="submit" class="btn btn-light">
                            <i class="fas fa-paper-plane me-1"></i>Subscribe
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</section>

<style>
/* Blog specific styles */
.blog-search .input-group {
    max-width: 500px;
    margin: 0 auto;
}

.blog-card {
    background: white;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
}

.blog-card-image {
    position: relative;
    overflow: hidden;
    height: 200px;
}

.blog-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-card-image img {
    transform: scale(1.05);
}

.blog-card-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.blog-card:hover .blog-card-overlay {
    opacity: 1;
}

.blog-card-content {
    padding: 1.5rem;
    flex: 1;
    display: flex;
    flex-direction: column;
}

.blog-card-meta {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.category-link {
    background: var(--primary-color);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 1rem;
    text-decoration: none;
    font-weight: 500;
}

.category-link:hover {
    background: var(--primary-dark);
    color: white;
}

.date {
    color: var(--text-light);
}

.blog-card-title {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.blog-card-title a {
    color: var(--text-dark);
    text-decoration: none;
}

.blog-card-title a:hover {
    color: var(--primary-color);
}

.blog-card-excerpt {
    color: var(--text-light);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex: 1;
}

.blog-card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 1rem;
    border-top: 1px solid var(--border-light);
    font-size: 0.875rem;
    color: var(--text-light);
}

.newsletter-form .input-group {
    border-radius: 0.5rem;
    overflow: hidden;
}

.newsletter-form .form-control {
    border: none;
    padding: 0.75rem 1rem;
}

.newsletter-form .btn {
    border: none;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
}
</style>

<?php $this->load->view('landing/footer'); ?>
