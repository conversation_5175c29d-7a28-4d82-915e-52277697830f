<?php $this->load->view('landing/header'); ?>

<!-- Blog Category -->
<section class="blog-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo base_url('blog'); ?>">Blog</a></li>
                        <li class="breadcrumb-item active"><?php echo htmlspecialchars($category->name); ?></li>
                    </ol>
                </nav>
                <h1 class="hero-title"><?php echo htmlspecialchars($category->name); ?></h1>
                <?php if ($category->description): ?>
                    <p class="hero-subtitle"><?php echo htmlspecialchars($category->description); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<section class="blog-content">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (!empty($posts)): ?>
                    <div class="row">
                        <?php foreach ($posts as $post): ?>
                            <div class="col-md-6 mb-4">
                                <article class="blog-card">
                                    <?php if ($post->featured_image): ?>
                                        <div class="blog-card-image">
                                            <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" 
                                                 alt="<?php echo htmlspecialchars($post->title); ?>" 
                                                 class="img-fluid">
                                            <div class="blog-card-overlay">
                                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more-btn">
                                                    <i class="fas fa-arrow-right"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-content">
                                        <div class="blog-meta">
                                            <span class="blog-date">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('M j, Y', strtotime($post->published_at)); ?>
                                            </span>
                                            <span class="blog-author">
                                                <i class="fas fa-user"></i>
                                                <?php echo htmlspecialchars($post->author_name); ?>
                                            </span>
                                        </div>
                                        
                                        <h3 class="blog-card-title">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>">
                                                <?php echo htmlspecialchars($post->title); ?>
                                            </a>
                                        </h3>
                                        
                                        <p class="blog-card-excerpt">
                                            <?php 
                                            $excerpt = $post->excerpt ?: strip_tags($post->content);
                                            echo htmlspecialchars(word_limiter($excerpt, 20));
                                            ?>
                                        </p>
                                        
                                        <div class="blog-card-footer">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more">
                                                Read More <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                            <div class="blog-stats">
                                                <span><i class="fas fa-eye"></i> <?php echo number_format($post->views); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- Pagination -->
                    <?php if (!empty($pagination)): ?>
                        <div class="blog-pagination">
                            <?php echo $pagination; ?>
                        </div>
                    <?php endif; ?>
                <?php else: ?>
                    <div class="no-posts">
                        <div class="text-center py-5">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <h4>No Posts in This Category</h4>
                            <p class="text-muted mb-4">
                                There are no published posts in the "<?php echo htmlspecialchars($category->name); ?>" category yet.
                            </p>
                            <a href="<?php echo base_url('blog'); ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to All Posts
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="blog-sidebar">
                    <!-- Search Widget -->
                    <div class="sidebar-widget">
                        <h5 class="widget-title">Search Posts</h5>
                        <form action="<?php echo base_url('blog/search'); ?>" method="get" class="search-form">
                            <div class="input-group">
                                <input type="text" name="q" class="form-control" 
                                       placeholder="Search posts..." required>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Categories Widget -->
                    <?php if (!empty($categories)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="category-list">
                                <?php foreach ($categories as $cat): ?>
                                    <li class="<?php echo ($cat->id == $category->id) ? 'active' : ''; ?>">
                                        <a href="<?php echo base_url('blog/category/' . $cat->slug); ?>">
                                            <?php echo htmlspecialchars($cat->name); ?>
                                            <span class="post-count">(<?php echo $cat->post_count; ?>)</span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Recent Posts Widget -->
                    <?php if (!empty($recent_posts)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Recent Posts</h5>
                            <div class="recent-posts">
                                <?php foreach ($recent_posts as $recent_post): ?>
                                    <div class="recent-post-item">
                                        <?php if ($recent_post->featured_image): ?>
                                            <div class="recent-post-image">
                                                <img src="<?php echo base_url('uploads/blog/thumbs/' . $recent_post->featured_image); ?>" 
                                                     alt="<?php echo htmlspecialchars($recent_post->title); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="recent-post-content">
                                            <h6>
                                                <a href="<?php echo base_url('blog/post/' . $recent_post->slug); ?>">
                                                    <?php echo htmlspecialchars($recent_post->title); ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($recent_post->published_at)); ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Popular Tags Widget -->
                    <?php if (!empty($popular_tags)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Popular Tags</h5>
                            <div class="tag-cloud">
                                <?php foreach ($popular_tags as $tag): ?>
                                    <a href="<?php echo base_url('blog/tag/' . $tag->slug); ?>" class="tag-item">
                                        <?php echo htmlspecialchars($tag->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Newsletter Widget -->
                    <div class="sidebar-widget newsletter-widget">
                        <h5 class="widget-title">Stay Updated</h5>
                        <p>Subscribe to our newsletter for the latest updates and tips.</p>
                        <form class="newsletter-form">
                            <div class="mb-3">
                                <input type="email" class="form-control" placeholder="Your email address" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-envelope me-2"></i>Subscribe
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.no-posts {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.category-list li.active a {
    color: var(--primary-color);
    font-weight: 600;
}

.category-list li.active {
    background: rgba(99, 102, 241, 0.1);
    border-radius: 0.5rem;
}

.newsletter-widget {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-radius: 1rem;
    padding: 2rem;
}

.newsletter-widget .widget-title {
    color: white;
}

.newsletter-widget p {
    color: rgba(255,255,255,0.9);
}

.newsletter-widget .form-control {
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
}

.newsletter-widget .form-control::placeholder {
    color: rgba(255,255,255,0.7);
}

.newsletter-widget .btn-primary {
    background: white;
    color: var(--primary-color);
    border: none;
}

.newsletter-widget .btn-primary:hover {
    background: rgba(255,255,255,0.9);
}
</style>

<?php $this->load->view('landing/footer'); ?>
