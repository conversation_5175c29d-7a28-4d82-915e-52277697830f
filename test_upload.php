<?php
// Test script to check upload directory permissions
// Run this once to verify everything is set up correctly

echo "<h2>Blog Upload Directory Test</h2>";

$upload_dir = './uploads/blog/';
$thumbs_dir = './uploads/blog/thumbs/';

echo "<h3>Directory Checks:</h3>";

// Check if main upload directory exists
if (is_dir($upload_dir)) {
    echo "✅ Main upload directory exists: " . $upload_dir . "<br>";
} else {
    echo "❌ Main upload directory does not exist: " . $upload_dir . "<br>";
    if (mkdir($upload_dir, 0755, true)) {
        echo "✅ Created main upload directory<br>";
    } else {
        echo "❌ Failed to create main upload directory<br>";
    }
}

// Check if thumbs directory exists
if (is_dir($thumbs_dir)) {
    echo "✅ Thumbs directory exists: " . $thumbs_dir . "<br>";
} else {
    echo "❌ Thumbs directory does not exist: " . $thumbs_dir . "<br>";
    if (mkdir($thumbs_dir, 0755, true)) {
        echo "✅ Created thumbs directory<br>";
    } else {
        echo "❌ Failed to create thumbs directory<br>";
    }
}

// Check permissions
if (is_writable($upload_dir)) {
    echo "✅ Main upload directory is writable<br>";
} else {
    echo "❌ Main upload directory is not writable<br>";
    echo "Try running: chmod 755 " . $upload_dir . "<br>";
}

if (is_writable($thumbs_dir)) {
    echo "✅ Thumbs directory is writable<br>";
} else {
    echo "❌ Thumbs directory is not writable<br>";
    echo "Try running: chmod 755 " . $thumbs_dir . "<br>";
}

echo "<h3>PHP Configuration:</h3>";

// Check PHP upload settings
echo "Max file size: " . ini_get('upload_max_filesize') . "<br>";
echo "Max post size: " . ini_get('post_max_size') . "<br>";
echo "File uploads enabled: " . (ini_get('file_uploads') ? 'Yes' : 'No') . "<br>";

// Check GD library for image processing
if (extension_loaded('gd')) {
    echo "✅ GD library is loaded (needed for thumbnails)<br>";
    $gd_info = gd_info();
    echo "GD Version: " . $gd_info['GD Version'] . "<br>";
} else {
    echo "❌ GD library is not loaded (thumbnails won't work)<br>";
}

echo "<h3>Test File Creation:</h3>";

// Test creating a file
$test_file = $upload_dir . 'test.txt';
if (file_put_contents($test_file, 'test content')) {
    echo "✅ Can create files in upload directory<br>";
    unlink($test_file); // Clean up
} else {
    echo "❌ Cannot create files in upload directory<br>";
}

echo "<br><strong>If all checks pass, image upload should work!</strong><br>";
echo "<br><a href='Blog_management/create'>Go to Create Blog Post</a>";
?>
