<!-- Content wrapper start -->

<?php

$expires = 0;
$soon = 0;
$still = 0;
foreach ($product_life_data as $product_life)
{

    $dateOne = new DateTime($product_life->expire_date);
    $dateTwo = new DateTime(date('Y-m-d'));

    $diff = $dateTwo->diff($dateOne)->format("%a");

    if($product_life->expire_date < date('Y-m-d')){


        $expires ++;

    }elseif ($diff < 31 && $diff > 0){

        $soon ++;

    }else{
        $still ++;
    }

}

?>
<div class="content-wrapper">
	<?php
    $settings = get_by_id('cpanel_settings','id','1');
    $domains = get_all('company_domains');
    $company= get_all('company');
	if($this->session->userdata('role')=='1'){
$jan = count_by_year(DATE('Y'),1);
$feb = count_by_year(DATE('Y'),2);
$mar = count_by_year(DATE('Y'),2);
$apr = count_by_year(DATE('Y'),4);
$may = count_by_year(DATE('Y'),5);
$june = count_by_year(DATE('Y'),6);
$july = count_by_year(DATE('Y'),7);
$aug = count_by_year(DATE('Y'),8);
$sept = count_by_year(DATE('Y'),9);
$oct = count_by_year(DATE('Y'),10);
$nov = count_by_year(DATE('Y'),11);
$dec = count_by_year(DATE('Y'),12);

		?>
		WELCOME SUPER-ADMIN
        <hr>
        <div class="col-lg-12 ">
            <div class="row ">
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-cherry">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large"><i class="fas fa-shopping-cart"></i></div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0">Expiring</h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0">
                                        <?php
                                        echo  $soon;
                                        ?>
                                    </h2>
                                </div>
                                <div class="col-4 text-right">
                                    <span>Hosting & domains  <i class="fa fa-arrow-up"></i></span>
                                </div>
                            </div>
                            <div class="progress mt-1 " data-height="8" style="height: 8px;">
                                <div class="progress-bar l-bg-cyan" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-blue-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large"><i class="fas fa-users"></i></div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0">Customers</h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0">
                                        <?php
                                        $cc = 0;
                                        foreach ($company as $co){
                                            $cc ++;
                                        }
                                        echo $cc;
                                        ?>
                                    </h2>
                                </div>
                                <div class="col-4 text-right">
                                    <span>All clients <i class="fa fa-arrow-up"></i></span>
                                </div>
                            </div>
                            <div class="progress mt-1 " data-height="8" style="height: 8px;">
                                <div class="progress-bar l-bg-green" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-green-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large"><i class="fas fa-ticket-alt"></i></div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0">Domains</h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0">
                                        <?php
                                        $dc = 0;
                                        foreach ($domains as $d){
                                            $dc ++;
                                        }
                                        echo $dc;
                                        ?>
                                    </h2>
                                </div>
                                <div class="col-4 text-right">
                                    <span>All domains <i class="fa fa-arrow-up"></i></span>
                                </div>
                            </div>
                            <div class="progress mt-1 " data-height="8" style="height: 8px;">
                                <div class="progress-bar l-bg-orange" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-lg-6">
                    <div class="card l-bg-orange-dark">
                        <div class="card-statistic-3 p-4">
                            <div class="card-icon card-icon-large"><i class="fas fa-dollar-sign"></i></div>
                            <div class="mb-4">
                                <h5 class="card-title mb-0">Total Yearly Revenue </h5>
                            </div>
                            <div class="row align-items-center mb-2 d-flex">
                                <div class="col-8">
                                    <h2 class="d-flex align-items-center mb-0">

                                        <?php

                                        $t = sum_year_rev(DATE('Y'));
                                        echo $settings->currency.number_format($t->total_sum,2);
                                        ?>

                                    </h2>
                                </div>
                                <div class="col-4 text-right">
                                    <span>Yearly revenue  <i class="fa fa-arrow-up"></i></span>
                                </div>
                            </div>
                            <div class="progress mt-1 " data-height="8" style="height: 8px;">
                                <div class="progress-bar l-bg-cyan" role="progressbar" data-width="25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100" style="width: 25%;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-8"> <div id="myChart" style="width:100%; max-width:1000px; height:600px;"></div></div>
            <div class="col-lg-4"><div id="expiring" style="width:100%; max-width:500px; height:600px;"></div></div>
        </div>


        <script>
            google.charts.load('current', {'packages':['corechart']});
            google.charts.setOnLoadCallback(drawChartt);

            function drawChartt() {
                var data = google.visualization.arrayToDataTable([
                    ['Category', 'Qty'],
                    ['Expiring',<?php  echo $soon?>],
                    ['Active',<?php  echo $still?>],
                    ['Expired',<?php  echo $expires?>]

                ]);

                var options = {
                    title:'Cpanel domains hosting product tracker'
                };

                var chart = new google.visualization.PieChart(document.getElementById('expiring'));
                chart.draw(data, options);
            }
        </script>
        <script>
            google.charts.load('current',{packages:['corechart']});
            google.charts.setOnLoadCallback(drawChart);



            function drawChart() {
// Set Data
                var data = google.visualization.arrayToDataTable([
                    ['Month', 'qty'],
                    ['Jan',<?php echo $jan ?>],['Feb',<?php echo $feb ?>],['Mar',<?php echo $mar ?>],['April',<?php echo $apr ?>],['May',<?php echo $may ?>],
                    ['June',<?php echo $june ?>],['July',<?php echo $july ?>],['Aug',<?php echo $aug ?>],
                    ['Sept',<?php echo $still ?>],['Oct',<?php echo $oct ?>],['Nov',<?php echo $nov ?>],['Dec',<?php echo $dec ?>]
                ]);
// Set Options
                var options = {
                    title: 'This years customer registration',
                    hAxis: {title: "Current Year's months"},
                    vAxis: {title: 'Number of customers'},
                    legend: 'none'
                };
// Draw
                var chart = new google.visualization.LineChart(document.getElementById('myChart'));
                chart.draw(data, options);
            }


        </script>


		<?php
	}else{
	?>
	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
			<div class="stats-tile">
				<div class="sale-icon">
					<i class="icon-globe"></i>
				</div>
				<div class="sale-details">
					<h2><?php echo $count_domains;?></h2>
					<p>Domains</p>
				</div>
				<div class="sale-graph">
					<div id="sparklineLine1"></div>
				</div>
			</div>
		</div>
		<div class="col-xl-4 col-lg-4 col-md-4 col-sm-6 col-12">
			<div class="stats-tile">
				<div class="sale-icon">
					<i class="icon-email"></i>
				</div>
				<div class="sale-details">
					<h2><?php echo $emails; ?></h2>
					<p>Emails</p>
				</div>
				<div class="sale-graph">
					<div id="sparklineLine2"></div>
				</div>
			</div>
		</div>
		<div class="col-xl-4 col-lg-4 col-md-4 col-sm-12 col-12">
			<div class="stats-tile">
				<div class="sale-icon">
					<i class="icon-compass"></i>
				</div>
				<div class="sale-details">
					<h2><?php echo $subdomains?></h2>
					<p>Subdomains</p>
				</div>
				<div class="sale-graph">
					<div id="sparklineLine3"></div>
				</div>
			</div>
		</div>
	</div>

	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
			<div class="card">
				<div class="card-header">
					<div class="card-title">Email</div>
					<div class="graph-day-selection" role="group">
						<button type="button" class="btn active">Your email panel</button>
					</div>
				</div>
				<div class="card-body">
					<div class="table-responsive">
						<table class="table products-table">
							<thead>
							<tr>

								<th> </th>
								<th> </th>
								<th> </th>
								<th> </th>

							</tr>
							</thead>
							<tbody>
							<tr>

								<td>
									<a href="<?php echo base_url('my_emails') ?>"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/icon.png" alt="Product Image">Email accounts</a>
								</td>
								<td>
                                    <a href="<?php echo base_url('my_email_forwarder')?>"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/icon2.png" alt="Product Image">Email Forward</a>
								</td>
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/icon2.png" alt="Product Image">Auto responders-->
<!--								</td>-->
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/icon4.png" alt="Mailing list">Mailing list-->
<!--								</td>-->

							</tr>

							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
			<div class="card">
				<div class="card-header">
					<div class="card-title">Files</div>
					<div class="graph-day-selection" role="group">
						<button type="button" class="btn active">Your file panel</button>
					</div>
				</div>
				<div class="card-body">
					<div class="table-responsive">
						<table class="table products-table">
							<thead>
							<tr>

								<th> </th>
								<th> </th>
								<th> </th>
								<th> </th>

							</tr>
							</thead>
							<tbody>
							<tr>

								<td>
                                    <a href="<?php  echo  base_url('Fmgr/index')?>" ><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/icon5.png" alt="Product Image">File manager</a>
								</td>
								<td>
                                    <a href="<?php  echo  base_url('files/ftp')?>"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/icon6.png" alt="Product Image">Ftp Accounts</a>
								</td>
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/icon7.png" alt="Product Image">Web disk-->
<!--								</td>-->
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/icon8.png" alt="Mailing list">Git version control-->
<!--								</td>-->

							</tr>

							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
			<div class="card">
				<div class="card-header">
					<div class="card-title">Databases</div>
					<div class="graph-day-selection" role="group">
						<button type="button" class="btn active">Your Databases</button>
					</div>
				</div>
				<div class="card-body">
					<div class="table-responsive">
						<table class="table products-table">
							<thead>
							<tr>

								<th> </th>
								<th> </th>
								<th> </th>
								<th> </th>

							</tr>
							</thead>
							<tbody>
							<tr>

								<td>
                                    <a href="<?php echo base_url('phpmyadmin')?>"  target="_blank"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/phpmy.png" alt="PhpMyadmin">PhpMyadmin</a>
								</td>
								<td>
                                    <a href="<?php echo base_url('database/list')?>"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/databases.png" alt="Database">MySQL Databases</a>
								</td>


							</tr>

							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
			<div class="card">
				<div class="card-header">
					<div class="card-title">Scripts</div>
					<div class="graph-day-selection" role="group">
						<button type="button" class="btn active">Scripts and tools</button>
					</div>
				</div>
				<div class="card-body">
					<div class="table-responsive">
						<table class="table products-table">
							<thead>
							<tr>

								<th> </th>
								<th> </th>
								<th> </th>
								<th> </th>

							</tr>
							</thead>
							<tbody>
							<tr>

								<td>
                                    <a href="<?php echo base_url('wordpress.php')?>" target="_blank"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/wordpress.jpg" alt="PhpMyadmin">Wordpress</a>
								</td>
								<td>
                                    <a href="<?php echo base_url('scripts/php')?>"><img class="user" src="<?php echo base_url('cpanel_assets/')?>img/products/php.png" alt="php version">PHP version</td>
								</td>
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/band.png" alt="Database wizard">Bandwidth usage								</td>-->
<!--								<td>-->
<!--									<img class="user" src="--><?php //echo base_url('cpanel_assets/')?><!--img/products/php.png" alt="Remote MySQL">PHP version-->
<!--								</td>-->

							</tr>

							</tbody>
						</table>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- Row end -->
	<?php

	}
	?>


</div>
<!-- Content wrapper end -->
