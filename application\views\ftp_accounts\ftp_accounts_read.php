<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card">
				<div class="card-body">
        <h2 style="margin-top:0px">Ftp accounts Details</h2>
					<div class="row">
						<div class="col-lg-6">
							<table class="table" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">

								<tr><td>Username</td><td><?php echo $username; ?></td></tr>
								<tr><td>Domain</td><td><?php echo $domain; ?></td></tr>
								<tr><td>Full name</td><td><?php echo $username; ?>@<?php echo $domain; ?></td></tr>
								<tr><td>Homedir</td><td><?php echo $homedir; ?></td></tr>
								<tr><td>Quota</td><td><?php echo $quota; ?></td></tr>
								<tr><td> Date added</td><td><?php echo $quota_date; ?></td></tr>
									</table>
						</div>
						<div class="col-lg-6">
							<?php
							if($this->session->flashdata('error')){
								?>
								<div class="alert alert-danger" role="alert">
									<ul>

										<?php
										foreach ($this->session->flashdata('error') as $err=>$value){
											echo "<li>-";
											echo $value;
											echo "</li>";
										}
										?>
									</ul>
								</div>
								<?php
							}
							?>
<div class="row">
	<div class="col-6">
		<form action="<?php echo base_url('Ftp_accounts/update_pass')?>" method="post" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;" >
			<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
				<input type="text" hidden name="username" value="<?php echo $username; ?>">
				<input type="text" hidden name="domain" value="<?php echo $domain; ?>">
				<input type="text" hidden name="id" value="<?php echo $ftp_id; ?>">
				<!-- Field wrapper start -->
				<div class="field-wrapper">
					<div class="input-group">
						<input class="form-control" type="password" name="newpass" required>
						<span class="input-group-text">
															<i class="icon-vpn_key"></i>
														</span>
					</div>
					<div class="field-placeholder">Change Password <span class="text-danger">*</span></div>
				</div>
				<!-- Field wrapper end -->

			</div>
			<input type="submit" class="btn btn-sm btn-warning" value="Confirm password change">
		</form>
	</div>
	<div class="col-6">
		<div class="card" style="border: thick solid blanchedalmond;border-radius: 15px;padding: 2em;">
		<div class="card-header bg-primary">
			<div class="card-title">Delete Ftp account</div>
		</div>
		<div class="card-body">

			<!-- Row start -->
			<div class="row gutters">
				<p>Are you sure? When you delete an ftp account, we permanently delete all of the account’s data.</p>
				<a class="btn btn-block btn-danger" href="<?php echo base_url('Ftp_accounts/delete/').$ftp_id?>" onclick="return confirm('Are you sure you want to do this?')"><i class="icon-trash-2"></i>Delete</a>
			</div>
			<!-- Row end -->

		</div>
	</div>
	</div>
</div>
						</div>
					</div>

				</div>
			</div>
		</div>
	</div>
</div>
