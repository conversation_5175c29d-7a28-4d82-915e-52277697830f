<?php $this->load->view('landing/header'); ?>

<!-- Contact Hero Section -->
<section class="hero-section">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h1>Get in Touch</h1>
                <p class="lead">Have questions about our email hosting services? Our team is here to help you find the perfect solution for your business.</p>
                
                <?php if (isset($_GET['plan'])): ?>
                <div class="price-highlight">
                    <h4><i class="fas fa-envelope me-2"></i>Interested in: <?php echo htmlspecialchars($_GET['plan']); ?></h4>
                    <p>Fill out the form below and we'll get back to you with more details</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Contact Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-lg-8 mb-4">
                <div class="feature-card">
                    <h3 class="mb-4"><i class="fas fa-envelope text-primary me-2"></i>Send us a Message</h3>
                    
                    <?php if (isset($success_message)): ?>
                    <div class="alert alert-success alert-dismissible fade show">
                        <i class="fas fa-check-circle me-2"></i><?php echo $success_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i><?php echo $error_message; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                    <?php endif; ?>
                    
                    <form id="contactForm" method="post" action="<?php echo current_url(); ?>">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">Full Name *</label>
                                <input type="text" class="form-control" id="name" name="name" required 
                                       value="<?php echo set_value('name'); ?>">
                                <?php echo form_error('name', '<small class="text-danger">', '</small>'); ?>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" required 
                                       value="<?php echo set_value('email'); ?>">
                                <?php echo form_error('email', '<small class="text-danger">', '</small>'); ?>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">Phone Number</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo set_value('phone'); ?>">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="company" class="form-label">Company Name</label>
                                <input type="text" class="form-control" id="company" name="company" 
                                       value="<?php echo set_value('company'); ?>">
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Subject *</label>
                            <select class="form-control" id="subject" name="subject" required>
                                <option value="">Select a subject</option>
                                <option value="Email Hosting Inquiry" <?php echo set_select('subject', 'Email Hosting Inquiry'); ?>>Email Hosting Inquiry</option>
                                <option value="Pricing Information" <?php echo set_select('subject', 'Pricing Information'); ?>>Pricing Information</option>
                                <option value="Technical Support" <?php echo set_select('subject', 'Technical Support'); ?>>Technical Support</option>
                                <option value="Domain Registration" <?php echo set_select('subject', 'Domain Registration'); ?>>Domain Registration</option>
                                <option value="Migration Assistance" <?php echo set_select('subject', 'Migration Assistance'); ?>>Migration Assistance</option>
                                <option value="Custom Solution" <?php echo set_select('subject', 'Custom Solution'); ?>>Custom Solution</option>
                                <option value="Other" <?php echo set_select('subject', 'Other'); ?>>Other</option>
                            </select>
                            <?php echo form_error('subject', '<small class="text-danger">', '</small>'); ?>
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message *</label>
                            <textarea class="form-control" id="message" name="message" rows="5" required 
                                      placeholder="Tell us about your email hosting needs..."><?php echo set_value('message'); ?></textarea>
                            <?php echo form_error('message', '<small class="text-danger">', '</small>'); ?>
                        </div>
                        
                        <div class="mb-3">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="newsletter" name="newsletter" value="1">
                                <label class="form-check-label" for="newsletter">
                                    I'd like to receive updates about new services and special offers
                                </label>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>Send Message
                        </button>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-lg-4">
                <div class="feature-card mb-4">
                    <h4 class="mb-4"><i class="fas fa-info-circle text-primary me-2"></i>Contact Information</h4>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-start">
                            <div class="feature-icon bg-primary me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div>
                                <h6>Email</h6>
                                <p class="mb-0">
                                    <a href="mailto:<?php echo $settings->company_email; ?>" class="text-decoration-none">
                                        <?php echo $settings->company_email; ?>
                                    </a>
                                    <button class="btn btn-sm btn-outline-secondary ms-2 copy-btn" 
                                            data-copy="<?php echo $settings->company_email; ?>">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-start">
                            <div class="feature-icon bg-primary me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                <i class="fas fa-phone"></i>
                            </div>
                            <div>
                                <h6>Phone</h6>
                                <p class="mb-0">
                                    <a href="tel:+265999123456" class="text-decoration-none">+*********** 456</a>
                                    <button class="btn btn-sm btn-outline-secondary ms-2 copy-btn" 
                                            data-copy="+265999123456">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item mb-3">
                        <div class="d-flex align-items-start">
                            <div class="feature-icon bg-primary me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <h6>Address</h6>
                                <p class="mb-0">
                                    Area 47, Sector 3<br>
                                    Lilongwe, Malawi
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="d-flex align-items-start">
                            <div class="feature-icon bg-primary me-3" style="width: 40px; height: 40px; font-size: 1rem;">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div>
                                <h6>Business Hours</h6>
                                <p class="mb-0">
                                    Monday - Friday: 8:00 AM - 6:00 PM<br>
                                    Saturday: 9:00 AM - 2:00 PM<br>
                                    Sunday: Closed
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Links -->
                <div class="feature-card">
                    <h5 class="mb-3"><i class="fas fa-link text-primary me-2"></i>Quick Links</h5>
                    <div class="d-grid gap-2">
                        <a href="<?php echo base_url('pricing'); ?>" class="btn btn-outline-primary">
                            <i class="fas fa-tag me-2"></i>View Pricing Plans
                        </a>
                        <a href="<?php echo base_url('login'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-sign-in-alt me-2"></i>Customer Login
                        </a>
                        <a href="<?php echo base_url('about'); ?>" class="btn btn-outline-info">
                            <i class="fas fa-info-circle me-2"></i>About Our Company
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-5 bg-white">
    <div class="container">
        <div class="section-title">
            <h2>Common Questions</h2>
            <p>Quick answers to questions you might have before contacting us.</p>
        </div>
        
        <div class="row">
            <div class="col-lg-6 mb-4">
                <div class="feature-card h-100">
                    <h5><i class="fas fa-question-circle text-primary me-2"></i>How quickly can I get started?</h5>
                    <p>Most email hosting accounts are set up within 24 hours of payment confirmation. Domain registration may take 24-48 hours to propagate globally.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="feature-card h-100">
                    <h5><i class="fas fa-question-circle text-primary me-2"></i>Do you provide migration assistance?</h5>
                    <p>Yes! We offer free email migration services to help you transfer your existing emails from your current provider without any data loss.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="feature-card h-100">
                    <h5><i class="fas fa-question-circle text-primary me-2"></i>What payment methods do you accept?</h5>
                    <p>We accept bank transfers, mobile money (Airtel Money, TNM Mpamba), and major credit cards. Payment plans are available for annual subscriptions.</p>
                </div>
            </div>
            
            <div class="col-lg-6 mb-4">
                <div class="feature-card h-100">
                    <h5><i class="fas fa-question-circle text-primary me-2"></i>Is technical support included?</h5>
                    <p>Yes, all our plans include technical support. We provide email support for all plans, with phone support available for Business and Enterprise plans.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="cta-section">
    <div class="container">
        <div class="row justify-content-center text-center">
            <div class="col-lg-8">
                <h2>Ready to Get Professional Email?</h2>
                <p>Don't wait! Get your business email hosting set up today and start building your professional online presence.</p>
                <div class="d-flex gap-3 justify-content-center flex-wrap">
                    <a href="<?php echo base_url('pricing'); ?>" class="btn-cta">
                        <i class="fas fa-rocket me-2"></i>Start Now - 9,800 Kwacha
                    </a>
                    <a href="tel:+265999123456" class="btn btn-outline-light btn-lg">
                        <i class="fas fa-phone me-2"></i>Call Us Now
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<?php $this->load->view('landing/footer'); ?>
