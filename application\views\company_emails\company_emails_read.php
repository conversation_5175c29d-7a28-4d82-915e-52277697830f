<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card">
				<div class="card-body">
					<h5>MANAGE AN EMAIL ACCOUNT</h5>
					<hr>
        <table class="table">
	    <tr><td>Email</td><td><?php echo $email; ?>@<?php echo $domain; ?></td><td><a href="#" class="btn btn-sm btn-success"><i class="icon-arrow_forward"></i>Check the email</a> </td></tr>

	</table>
					<div class="row">
					<div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12 border-right">
						<?php
						if($this->session->flashdata('error')){
							?>
							<div class="alert alert-danger" role="alert">
								<ul>

									<?php
									foreach ($this->session->flashdata('error') as $err=>$value){
										echo "<li>-";
										echo $value;
										echo "</li>";
									}
									?>
								</ul>
							</div>
							<?php
						}
						?>
					<div class="card">
						<div class="card-header bg-primary">
							<div class="card-title">Security</div>
						</div>
						<div class="card-body">

							<!-- Row start -->
							<div class="row gutters">
								<form action="<?php echo base_url('Company_emails/update_settings')?>" method="post">
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
									<input type="text" hidden name="email" value="<?php echo $email; ?>">
									<input type="text" hidden name="domain" value="<?php echo $domain; ?>">
									<input type="text" hidden name="id" value="<?php echo $email_id; ?>">
									<!-- Field wrapper start -->
									<div class="field-wrapper">
										<div class="input-group">
											<input class="form-control" type="password" name="newpass">
											<span class="input-group-text">
															<i class="icon-vpn_key"></i>
														</span>
										</div>
										<div class="field-placeholder">Change Password <span class="text-danger">*</span></div>
									</div>
									<!-- Field wrapper end -->

								</div>
								Restrictions
								<hr>
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 ">

									<!-- Field wrapper start -->
									<div class="field-wrapper">
										<div class="checkbox-container">
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" name="incoming_email" id="inlineRadio1"  <?php if($suspension=="allow_incoming_email"){echo "checked";} ?> value="allow_incoming_email">
												<label class="form-check-label" for="inlineRadio1">Allow</label>
											</div>
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" name="incoming_email" <?php if($suspension=="disallow_incoming_email"){echo "checked";} ?>  id="inlineRadio2" value="disallow_incoming_email">
												<label class="form-check-label" for="inlineRadio2">Suspend</label>
											</div>

											<div class="field-placeholder">Receiving incoming email</div>
										</div>
									</div>
									<!-- Field wrapper end -->

								</div>
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 ">

									<!-- Field wrapper start -->
									<div class="field-wrapper">
										<div class="checkbox-container">
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" name="outgoing" <?php if($outgoing=="allow"){echo "checked";} ?> id="inlineRadio1" value="allow">
												<label class="form-check-label" for="inlineRadio1">Allow</label>
											</div>
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" <?php if($outgoing=="suspend"){echo "checked";} ?> name="outgoing" id="inlineRadio2" value="suspend">
												<label class="form-check-label" for="inlineRadio2">Suspend</label>
											</div>
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" name="outgoing" <?php if($outgoing=="hold"){echo "checked";} ?> id="inlineRadio2" value="hold">
												<label class="form-check-label" for="inlineRadio2">Hold</label>
											</div>

											<div class="field-placeholder">Sending Outgoing email</div>
										</div>
									</div>
									<!-- Field wrapper end -->

								</div>
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12 ">

									<!-- Field wrapper start -->
									<div class="field-wrapper">
										<div class="checkbox-container">
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" <?php if($login=="allow"){echo "checked";} ?> name="login" id="inlineRadio1" value="allow">
												<label class="form-check-label" for="inlineRadio1">Allow</label>
											</div>
											<div class="form-check form-check-inline">
												<input class="form-check-input" type="radio" <?php if($login=="suspend"){echo "checked";} ?> name="login" id="inlineRadio2" value="suspend">
												<label class="form-check-label" for="inlineRadio2">Suspend</label>
											</div>


											<div class="field-placeholder">Logging in to email</div>
										</div>
									</div>
									<!-- Field wrapper end -->

								</div>
								<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
									<button class="btn btn-primary" type="submit">Update email settings</button> <a href="<?php  echo base_url('my_emails')?>" style="float: right;" class="btn btn-success" ><i class="icon-arrow_back"></i>Go back</a>
								</div>
								</form>
							</div>
							<!-- Row end -->

						</div>
					</div>
					</div>
					<div class="col-xl-6 col-lg-6 col-md-6 col-sm-6 col-12">
						<div class="card">
							<div class="card-header bg-primary">
								<div class="card-title">Delete email account</div>
							</div>
							<div class="card-body">

								<!-- Row start -->
								<div class="row gutters">
									<p>Are you sure? When you delete an email account, we permanently delete all of the account’s data.</p>
									<a class="btn btn-block btn-danger" href="<?php echo base_url('Company_emails/delete/').$email_id?>" onclick="return confirm('Are you sure you want to do this?')"><i class="icon-trash-2"></i>Delete</a>
								</div>
								<!-- Row end -->

							</div>
						</div>
					</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
