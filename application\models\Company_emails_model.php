<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Company_emails_model extends CI_Model
{

    public $table = 'company_emails';
    public $id = 'email_id';
    public $order = 'DESC';

    function __construct()
    {
        parent::__construct();
    }
	function count_my_emails($company) {
		$this->db->from($this->table)
			->where('company',$company);
		return $this->db->count_all_results();
	}
	function get_my_emails($company) {
		$this->db->from($this->table)
			->where('company',$company);
		return $this->db->get()->result();
	}
	function get_by_domain($domain) {
		$this->db->from($this->table)
			->where('domain',$domain);
		return $this->db->get()->result();
	}
    // get all
    function get_all()
    {
        $this->db->order_by($this->id, $this->order);
        return $this->db->get($this->table)->result();
    }

    // get data by id
    function get_by_id($id)
    {
        $this->db->where($this->id, $id);
        return $this->db->get($this->table)->row();
    }
    function get_by_id2($id)
    {
        $this->db->where($this->id, $id);
        $this->db->where('company', $this->session->userdata('company_id'));
        return $this->db->get($this->table)->row();
    }
    
    // get total rows
    function total_rows($q = NULL) {
        $this->db->like('email_id', $q);
	$this->db->or_like('email', $q);
	$this->db->or_like('domain', $q);
	$this->db->or_like('company', $q);
	$this->db->or_like('email_stamp', $q);
	$this->db->from($this->table);
        return $this->db->count_all_results();
    }

    // get data with limit and search
    function get_limit_data($limit, $start = 0, $q = NULL) {
        $this->db->order_by($this->id, $this->order);
        $this->db->like('email_id', $q);
	$this->db->or_like('email', $q);
	$this->db->or_like('domain', $q);
	$this->db->or_like('company', $q);
	$this->db->or_like('email_stamp', $q);
	$this->db->limit($limit, $start);
        return $this->db->get($this->table)->result();
    }

    // insert data
    function insert($data)
    {
        $this->db->insert($this->table, $data);
    }

    // update data
    function update($id, $data)
    {
        $this->db->where($this->id, $id);
        $this->db->update($this->table, $data);
    }

    // delete data
    function delete($id)
    {
        $this->db->where($this->id, $id);
        $this->db->delete($this->table);
    }

}

/* End of file Company_emails_model.php */
/* Location: ./application/models/Company_emails_model.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-15 07:44:54 */
/* http://harviacode.com */
