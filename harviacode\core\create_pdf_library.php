<?php 

$string = "<?php if (!defined('BASEPATH')) exit('No direct script access allowed');
 
class pdf {
    
    function pdf()
    {
        \$CI = & get_instance();
        log_message('Debug', 'mPDF class is loaded.');
    }
 
    function load(\$param=NULL)
    {
        include_once APPPATH.'/third_party/mpdf/mpdf.php';
         
        if (\$params == NULL)
        {
            \$param = '\"en-GB-x\",\"A4\",\"\",\"\",10,10,10,10,6,3';         
        }
         
        return new mPDF(\$param);
    }
}


/* End of file pdf.php */
/* Location: ./application/libraries/pdf.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator ".date('Y-m-d H:i:s')." */
/* http://harviacode.com */";



$hasil_pdf = createFile($string, $target."libraries/pdf.php");

?>