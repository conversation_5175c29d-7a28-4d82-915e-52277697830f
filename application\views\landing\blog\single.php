<?php $this->load->view('landing/header'); ?>

<!-- Blog Post Hero -->
<section class="blog-post-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <!-- Breadcrumb -->
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo base_url('blog'); ?>">Blog</a></li>
                        <?php if ($post->category_name): ?>
                            <li class="breadcrumb-item">
                                <a href="<?php echo base_url('blog/category/' . $post->category_slug); ?>">
                                    <?php echo htmlspecialchars($post->category_name); ?>
                                </a>
                            </li>
                        <?php endif; ?>
                        <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($post->title); ?></li>
                    </ol>
                </nav>

                <!-- Post Meta -->
                <div class="post-meta">
                    <?php if ($post->category_name): ?>
                        <a href="<?php echo base_url('blog/category/' . $post->category_slug); ?>" class="category-badge">
                            <?php echo htmlspecialchars($post->category_name); ?>
                        </a>
                    <?php endif; ?>
                    <span class="post-date">
                        <i class="fas fa-calendar-alt me-1"></i>
                        <?php echo date('F j, Y', strtotime($post->published_at)); ?>
                    </span>
                    <span class="post-author">
                        <i class="fas fa-user me-1"></i>
                        <?php echo htmlspecialchars($post->author_name); ?>
                    </span>
                    <span class="post-views">
                        <i class="fas fa-eye me-1"></i>
                        <?php echo number_format($post->views); ?> views
                    </span>
                </div>

                <!-- Post Title -->
                <h1 class="post-title"><?php echo htmlspecialchars($post->title); ?></h1>

                <!-- Post Excerpt -->
                <?php if ($post->excerpt): ?>
                    <p class="post-excerpt"><?php echo htmlspecialchars($post->excerpt); ?></p>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Blog Post Content -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <article class="blog-post-content">
                    <!-- Featured Image -->
                    <?php if ($post->featured_image): ?>
                        <div class="post-featured-image">
                            <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>"
                                 alt="<?php echo htmlspecialchars($post->title); ?>"
                                 class="img-fluid rounded">
                        </div>
                    <?php endif; ?>

                    <!-- Post Content -->
                    <div class="post-content">
                        <?php echo $post->content; ?>
                    </div>

                    <!-- Post Tags -->
                    <?php if (!empty($post_tags)): ?>
                        <div class="post-tags">
                            <h6>Tags:</h6>
                            <div class="tag-list">
                                <?php foreach ($post_tags as $tag): ?>
                                    <a href="<?php echo base_url('blog/tag/' . $tag->slug); ?>" class="tag-item">
                                        <?php echo htmlspecialchars($tag->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Social Share -->
                    <div class="post-share">
                        <h6>Share this post:</h6>
                        <div class="share-buttons">
                            <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo urlencode(current_url()); ?>"
                               target="_blank" class="share-btn facebook">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <a href="https://twitter.com/intent/tweet?url=<?php echo urlencode(current_url()); ?>&text=<?php echo urlencode($post->title); ?>"
                               target="_blank" class="share-btn twitter">
                                <i class="fab fa-twitter"></i>
                            </a>
                            <a href="https://www.linkedin.com/sharing/share-offsite/?url=<?php echo urlencode(current_url()); ?>"
                               target="_blank" class="share-btn linkedin">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <a href="mailto:?subject=<?php echo urlencode($post->title); ?>&body=<?php echo urlencode(current_url()); ?>"
                               class="share-btn email">
                                <i class="fas fa-envelope"></i>
                            </a>
                        </div>
                    </div>
                </article>

                <!-- Related Posts -->
                <?php if (!empty($related_posts)): ?>
                    <section class="related-posts">
                        <h4>Related Posts</h4>
                        <div class="row g-4">
                            <?php foreach ($related_posts as $related_post): ?>
                                <div class="col-md-4">
                                    <div class="related-post-card">
                                        <?php if ($related_post->featured_image): ?>
                                            <div class="related-post-image">
                                                <img src="<?php echo base_url('uploads/blog/thumbs/' . $related_post->featured_image); ?>"
                                                     alt="<?php echo htmlspecialchars($related_post->title); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="related-post-content">
                                            <h6>
                                                <a href="<?php echo base_url('blog/post/' . $related_post->slug); ?>">
                                                    <?php echo htmlspecialchars($related_post->title); ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($related_post->published_at)); ?>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>

                <!-- Comments Section -->
                <section class="comments-section">
                    <h4>Comments (<?php echo $comment_count; ?>)</h4>

                    <!-- Comment Form -->
                    <div class="comment-form-wrapper">
                        <h5>Leave a Comment</h5>
                        <form id="commentForm" class="comment-form">
                            <input type="hidden" name="post_id" value="<?php echo $post->id; ?>">
                            <input type="hidden" name="parent_id" value="">

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="author_name" class="form-label">Name *</label>
                                        <input type="text" class="form-control" id="author_name" name="author_name" required>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="author_email" class="form-label">Email *</label>
                                        <input type="email" class="form-control" id="author_email" name="author_email" required>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="author_website" class="form-label">Website (optional)</label>
                                <input type="url" class="form-control" id="author_website" name="author_website">
                            </div>

                            <div class="mb-3">
                                <label for="content" class="form-label">Comment *</label>
                                <textarea class="form-control" id="content" name="content" rows="4" required></textarea>
                            </div>

                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Post Comment
                            </button>
                        </form>
                    </div>

                    <!-- Comments List -->
                    <?php if (!empty($comments)): ?>
                        <div class="comments-list">
                            <?php foreach ($comments as $comment): ?>
                                <div class="comment-item" id="comment-<?php echo $comment->id; ?>">
                                    <div class="comment-avatar">
                                        <i class="fas fa-user-circle fa-2x text-muted"></i>
                                    </div>
                                    <div class="comment-content">
                                        <div class="comment-header">
                                            <h6 class="comment-author">
                                                <?php if ($comment->author_website): ?>
                                                    <a href="<?php echo htmlspecialchars($comment->author_website); ?>" target="_blank">
                                                        <?php echo htmlspecialchars($comment->author_name); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <?php echo htmlspecialchars($comment->author_name); ?>
                                                <?php endif; ?>
                                            </h6>
                                            <span class="comment-date">
                                                <?php echo date('M j, Y \a\t g:i A', strtotime($comment->created_at)); ?>
                                            </span>
                                        </div>
                                        <div class="comment-text">
                                            <?php echo nl2br(htmlspecialchars($comment->content)); ?>
                                        </div>
                                        <div class="comment-actions">
                                            <button class="btn btn-sm btn-outline-primary reply-btn" data-comment-id="<?php echo $comment->id; ?>">
                                                <i class="fas fa-reply me-1"></i>Reply
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Comment Replies -->
                                <?php if (!empty($comment->replies)): ?>
                                    <div class="comment-replies">
                                        <?php foreach ($comment->replies as $reply): ?>
                                            <div class="comment-item reply" id="comment-<?php echo $reply->id; ?>">
                                                <div class="comment-avatar">
                                                    <i class="fas fa-user-circle fa-2x text-muted"></i>
                                                </div>
                                                <div class="comment-content">
                                                    <div class="comment-header">
                                                        <h6 class="comment-author">
                                                            <?php if ($reply->author_website): ?>
                                                                <a href="<?php echo htmlspecialchars($reply->author_website); ?>" target="_blank">
                                                                    <?php echo htmlspecialchars($reply->author_name); ?>
                                                                </a>
                                                            <?php else: ?>
                                                                <?php echo htmlspecialchars($reply->author_name); ?>
                                                            <?php endif; ?>
                                                        </h6>
                                                        <span class="comment-date">
                                                            <?php echo date('M j, Y \a\t g:i A', strtotime($reply->created_at)); ?>
                                                        </span>
                                                    </div>
                                                    <div class="comment-text">
                                                        <?php echo nl2br(htmlspecialchars($reply->content)); ?>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="no-comments">
                            <p class="text-muted">No comments yet. Be the first to comment!</p>
                        </div>
                    <?php endif; ?>
                </section>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <?php $this->load->view('landing/blog/sidebar'); ?>
            </div>
        </div>
    </div>
</section>

<style>
/* Blog Post Styles */
.blog-post-hero {
    background: var(--gradient-primary);
    color: white;
    padding: 6rem 0 4rem;
}

.breadcrumb {
    background: rgba(255,255,255,0.1);
    border-radius: 0.5rem;
    padding: 0.75rem 1rem;
    margin-bottom: 2rem;
}

.breadcrumb-item a {
    color: rgba(255,255,255,0.8);
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: white;
}

.breadcrumb-item.active {
    color: rgba(255,255,255,0.9);
}

.post-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.category-badge {
    background: rgba(255,255,255,0.2);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    text-decoration: none;
    font-weight: 500;
}

.category-badge:hover {
    background: rgba(255,255,255,0.3);
    color: white;
}

.post-date, .post-author, .post-views {
    color: rgba(255,255,255,0.9);
}

.post-title {
    font-size: clamp(2rem, 5vw, 3.5rem);
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: 1.5rem;
}

.post-excerpt {
    font-size: 1.25rem;
    color: rgba(255,255,255,0.9);
    line-height: 1.6;
}

.blog-post-content {
    background: white;
    border-radius: 1rem;
    padding: 2.5rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.post-featured-image {
    margin-bottom: 2rem;
}

.post-content {
    font-size: 1.125rem;
    line-height: 1.8;
    color: var(--text-dark);
    margin-bottom: 2rem;
}

.post-content h2, .post-content h3, .post-content h4 {
    margin-top: 2rem;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.post-content p {
    margin-bottom: 1.5rem;
}

.post-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.post-tags, .post-share {
    padding: 1.5rem 0;
    border-top: 1px solid var(--border-light);
    margin-top: 1.5rem;
}

.post-tags h6, .post-share h6 {
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-item {
    background: var(--bg-tertiary);
    color: var(--text-light);
    padding: 0.5rem 1rem;
    border-radius: 1.5rem;
    text-decoration: none;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.tag-item:hover {
    background: var(--primary-color);
    color: white;
}

.share-buttons {
    display: flex;
    gap: 0.5rem;
}

.share-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease;
}

.share-btn:hover {
    transform: translateY(-2px);
    color: white;
}

.share-btn.facebook { background: #3b5998; }
.share-btn.twitter { background: #1da1f2; }
.share-btn.linkedin { background: #0077b5; }
.share-btn.email { background: #34495e; }

/* Related Posts */
.related-posts {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    margin-bottom: 3rem;
}

.related-post-card {
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
    overflow: hidden;
    transition: transform 0.3s ease;
}

.related-post-card:hover {
    transform: translateY(-3px);
}

.related-post-image {
    height: 120px;
    overflow: hidden;
}

.related-post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.related-post-content {
    padding: 1rem;
}

.related-post-content h6 a {
    color: var(--text-dark);
    text-decoration: none;
    line-height: 1.4;
}

.related-post-content h6 a:hover {
    color: var(--primary-color);
}

/* Comments */
.comments-section {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.comment-form-wrapper {
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.comments-list {
    margin-top: 2rem;
}

.comment-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-light);
}

.comment-item.reply {
    margin-left: 3rem;
    margin-top: 1rem;
}

.comment-avatar {
    flex-shrink: 0;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.comment-author {
    margin: 0;
    font-size: 1rem;
}

.comment-author a {
    color: var(--primary-color);
    text-decoration: none;
}

.comment-date {
    font-size: 0.875rem;
    color: var(--text-light);
}

.comment-text {
    margin-bottom: 1rem;
    line-height: 1.6;
}

.comment-replies {
    margin-top: 1rem;
}

.no-comments {
    text-align: center;
    padding: 2rem;
    background: var(--bg-tertiary);
    border-radius: 0.5rem;
}

@media (max-width: 768px) {
    .post-meta {
        flex-direction: column;
        gap: 0.5rem;
    }

    .blog-post-content {
        padding: 1.5rem;
    }

    .comment-item.reply {
        margin-left: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Comment form submission
    const commentForm = document.getElementById('commentForm');
    if (commentForm) {
        commentForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Posting...';
            submitBtn.disabled = true;

            fetch('<?php echo base_url("blog/add_comment"); ?>', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert(data.message);
                    commentForm.reset();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                alert('An error occurred. Please try again.');
            })
            .finally(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    }

    // Reply buttons
    const replyBtns = document.querySelectorAll('.reply-btn');
    replyBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const commentId = this.dataset.commentId;
            const parentIdInput = document.querySelector('input[name="parent_id"]');
            if (parentIdInput) {
                parentIdInput.value = commentId;
                document.getElementById('commentForm').scrollIntoView({ behavior: 'smooth' });
                document.getElementById('author_name').focus();
            }
        });
    });
});
</script>

<?php $this->load->view('landing/footer'); ?>