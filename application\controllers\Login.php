<?php


class Login extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model("Users_model");
	}
	public function index(){
		$this->load->view('login');
	}
	public function forget_password(){
		$this->load->view('forgot_password');
	}
	public function auth(){
		$email = $this->input->post('email');
		$pass = md5($this->input->post('password'));
		$result = $this->Users_model->auth($email,$pass);
		if(!empty($result)){
			$this->session->set_userdata('company_id',$result->company);
			$this->session->set_userdata('user_id',$result->user_id);
			$this->session->set_userdata('name',$result->full_name);
			$this->session->set_userdata('email',$result->email);
			$this->session->set_userdata('role',$result->user_role);
			redirect('Dashboard');
		}else{
			$this->session->set_flashdata('error','Email or password is not valid');
			redirect('Login');
		}
	}
    function generateRandomString($length = 6) {
        return substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil($length/strlen($x)) )),1,$length);
    }


    public function send(){
        $settings = get_by_id('cpanel_settings','id','1');
		$email = $this->input->post('email');

		$result = get_by_id('users','email', $email);
		$new_pass =  $this->generateRandomString();

		if(!empty($result)){
		    $this->Users_model->update($result->id,array('password'=>md5($new_pass)));

            $output = "";
         $output.='
         <html>
         <head>
         <title>'.$settings->company_name.'</title>
         <style>
         body {
  background: green;
  text-align: center;
}

.card {
 text-align: center;
   margin: auto;
  width: 50%;
  
  padding: 10px;
  
  background: #E7E9EB;
  border-radius: 15px;
 
  height: 500px;

}

.card-1 {
  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
}

.card-1:hover {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-2 {
  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
}

.card-3 {
  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}

.card-4 {
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}

.card-5 {
  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
}


</style>
         
         </head>
         <body >
         <center><img src="'.base_url('uploads/').$settings->logo.'" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
         <br>
          <center><a href="'.base_url().'">'.$settings->company_name.'</a></center>
            <div class="card card-5" style="justify-items: center; align-items: center;">
            <p style="font-weight: bolder; font-size: 30px;">Hello: '.$result->full_name.'</p>
            <p style="font-weight: bold; font-size: 20px;">Here is your new password : </p>
            <p style="font-weight: bolder; font-size: 30px;">'.$new_pass.'</p>
            <p style="font-weight: bold; font-size: 20px;">it is recommended to change after you login</p>
            
         </div>
         
         <center>Powered by:<a href="'.base_url().'">'.$settings->company_name.'</a></center>
         </body>
         </html>
         ';

            $headers = "MIME-Version: 1.0" . "\r\n";
            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";

// More headers
            $headers .= 'From: '.$settings->company_email.'' . "\r\n";



            mail($email,'Password Recovery',$output,$headers);
            $this->session->set_flashdata('success','Email  is  valid');
            redirect('Login');
		}else{
			$this->session->set_flashdata('error','Email d is not valid');
			redirect('Login/forget_password');
		}
	}

	public function broadcast_msg(){

        $config = array('protocol' => 'smtp',
            'smtp_host' => 'infocustech-mw.com',
            'smtp_port' => 465,
            'smtp_user' => '<EMAIL>',
            'smtp_pass' => 'Mwaii@1992',
        );

        $this->load->library('email',$config);
        $this->email->set_newline("\r\n");
        $this->email->from('<EMAIL>', 'My Blabla');
        $this->email->to('<EMAIL>');
        $this->email->subject('Just test');
        $this->email->message('qwe adlw is alqopl slaod');

        if ($this->email->send()) {
            echo "email send";
        } else {
            show_error($this->email->debugger());
        }



//        $settings = get_by_id('cpanel_settings','id','1');
//		$email = $this->input->post('email');
//
//		$result = get_by_id('users','email', '<EMAIL>');
//		$new_pass =  $this->generateRandomString();
//
//		if(!empty($result)){
//		    $this->Users_model->update($result->id,array('password'=>md5($new_pass)));
//
//            $output = "";
//         $output.='
//         <html>
//         <head>
//         <title>'.$settings->company_name.'</title>
//         <style>
//         body {
//  background: green;
//  text-align: center;
//}
//
//.card {
// text-align: center;
//   margin: auto;
//  width: 50%;
//
//  padding: 10px;
//
//  background: #E7E9EB;
//  border-radius: 15px;
//
//  height: 500px;
//
//}
//
//.card-1 {
//  box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
//  transition: all 0.3s cubic-bezier(.25,.8,.25,1);
//}
//
//.card-1:hover {
//  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
//}
//
//.card-2 {
//  box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
//}
//
//.card-3 {
//  box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
//}
//
//.card-4 {
//  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
//}
//
//.card-5 {
//  box-shadow: 0 19px 38px rgba(0,0,0,0.30), 0 15px 12px rgba(0,0,0,0.22);
//}
//
//
//</style>
//
//         </head>
//         <body >
//         <center><img src="'.base_url('uploads/').$settings->logo.'" alt="logo" height="100" width="100" style="border-radius: 50px; border: thick solid lightslategray;"></center>
//         <br>
//          <center><a href="'.base_url().'">'.$settings->company_name.'</a></center>
//            <div class="card card-5" style="justify-items: center; align-items: center;">
//            <p style="font-weight: bolder; font-size: 30px;">Hello: '.$result->full_name.'</p>
//            <p style="font-weight: bold; font-size: 20px;">Here is your new password : </p>
//            <p style="font-weight: bolder; font-size: 30px;">'.$new_pass.'</p>
//            <p style="font-weight: bold; font-size: 20px;">it is recommended to change after you login</p>
//
//         </div>
//
//         <center>Powered by:<a href="'.base_url().'">'.$settings->company_name.'</a></center>
//         </body>
//         </html>
//         ';
//
//            $headers = "MIME-Version: 1.0" . "\r\n";
//            $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
//
//// More headers
//            $headers .= 'From: '.$settings->company_email.'' . "\r\n";
//
//
//
//            mail($email,'Password Recovery',$output,$headers);
//            $this->session->set_flashdata('success','Email  is  valid');
//            redirect($_SERVER['HTTP_REFERER']);
//		}else{
//			$this->session->set_flashdata('error','Email d is not valid');
//			redirect('Login/forget_password');
//		}
	}
	public function logout(){
		$this->session->sess_destroy();
		redirect(base_url().'login/index');
	}

}
