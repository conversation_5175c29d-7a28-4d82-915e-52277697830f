<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Product_payments extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Product_payments_model');
        $this->load->model('Product_life_model');
        $this->load->library('form_validation');
    }

    public function index()
    {


        $data = array(
            'product_payments_data' => $this->Product_payments_model->get_all(),

        );
        $config['active_menu'] = "server_config";
        $config['current_link'] = "payments";
        $this->load->view('header',$config);
        $this->load->view('product_payments/product_payments_list', $data);
        $this->load->view('footer');
       
    }
 public function company()
    {


        $data = array(
            'product_payments_data' => $this->Product_payments_model->get_co($this->session->userdata('company_id')),

        );
        $config['active_menu'] = "billing";
        $config['current_link'] = "co_pay";
        $this->load->view('header',$config);
        $this->load->view('payment_methods/payments', $data);
        $this->load->view('footer');

    }

    public function read($id) 
    {
        $row = $this->Product_payments_model->get_by_id($id);
        if ($row) {
            $data = array(
		'product_payment_id' => $row->product_payment_id,
		'product_life_id' => $row->product_life_id,
		'payment_method' => $row->payment_method,
		'reference' => $row->reference,
		'amount' => $row->amount,
		'payment_date' => $row->payment_date,
		'pop' => $row->pop,
		'stamp' => $row->stamp,
	    );
            $config['active_menu'] = "server_config";
            $config['current_link'] = "payments";
            $this->load->view('header',$config);
            $this->load->view('product_payments/product_payments_read', $data);
            $this->load->view('footer');

        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_payments'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('product_payments/create_action'),
	    'product_payment_id' => set_value('product_payment_id'),
	    'product_life_id' => set_value('product_life_id'),
	    'payment_method' => set_value('payment_method'),
	    'reference' => set_value('reference'),
	    'amount' => set_value('amount'),
	    'payment_date' => set_value('payment_date'),
	    'pop' => set_value('pop'),
	    'stamp' => set_value('stamp'),
	);
        $config['active_menu'] = "server_config";
        $config['current_link'] = "payments";
        $this->load->view('header',$config);
        $this->load->view('product_payments/product_payments_form', $data);
        $this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'product_life_id' => $this->input->post('product_life_id',TRUE),
		'payment_method' => $this->input->post('payment_method',TRUE),
		'reference' => $this->input->post('reference',TRUE),
		'amount' => $this->input->post('amount',TRUE),
		'payment_date' => $this->input->post('payment_date',TRUE),
		'pop' => $this->input->post('pop',TRUE),

	    );
            $exp = $this->input->post('exp');
            if($exp=='yes'){
                $this->Product_life_model->update($this->input->post('product_life_id',TRUE),array('expire_date'=>$this->input->post('expire_date',TRUE)));
            }

            $this->Product_payments_model->insert($data);
            $this->toaster->success('Payment was added successfully');
            redirect(site_url('product_payments'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Product_payments_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('product_payments/update_action'),
		'product_payment_id' => set_value('product_payment_id', $row->product_payment_id),
		'product_life_id' => set_value('product_life_id', $row->product_life_id),
		'payment_method' => set_value('payment_method', $row->payment_method),
		'reference' => set_value('reference', $row->reference),
		'amount' => set_value('amount', $row->amount),
		'payment_date' => set_value('payment_date', $row->payment_date),
		'pop' => set_value('pop', $row->pop),
		'stamp' => set_value('stamp', $row->stamp),
	    );
            $this->load->view('product_payments/product_payments_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_payments'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('product_payment_id', TRUE));
        } else {
            $data = array(
		'product_life_id' => $this->input->post('product_life_id',TRUE),
		'payment_method' => $this->input->post('payment_method',TRUE),
		'reference' => $this->input->post('reference',TRUE),
		'amount' => $this->input->post('amount',TRUE),
		'payment_date' => $this->input->post('payment_date',TRUE),
		'pop' => $this->input->post('pop',TRUE),
		'stamp' => $this->input->post('stamp',TRUE),
	    );

            $this->Product_payments_model->update($this->input->post('product_payment_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('product_payments'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Product_payments_model->get_by_id($id);

        if ($row) {
            $this->Product_payments_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('product_payments'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_payments'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('product_life_id', 'product life id', 'trim|required');
	$this->form_validation->set_rules('payment_method', 'payment method', 'trim|required');

	$this->form_validation->set_rules('amount', 'amount', 'trim|required|numeric');
	$this->form_validation->set_rules('payment_date', 'payment date', 'trim|required');


	$this->form_validation->set_rules('product_payment_id', 'product_payment_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

