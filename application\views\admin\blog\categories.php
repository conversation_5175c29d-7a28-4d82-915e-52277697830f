<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - EmailHost-Plus Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --cpanel-orange: #ff6c2c;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-blog me-2"></i>Blog Admin
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin'); ?>">
                            <i class="fas fa-list me-2"></i>All Posts
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/create'); ?>">
                            <i class="fas fa-plus me-2"></i>Add New Post
                        </a>
                        <a class="nav-link active" href="<?php echo base_url('admin/blog_admin/categories'); ?>">
                            <i class="fas fa-folder me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/comments'); ?>">
                            <i class="fas fa-comments me-2"></i>Comments
                        </a>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                        <a class="nav-link" href="<?php echo base_url('blog'); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Blog
                        </a>
                        <a class="nav-link" href="<?php echo base_url(); ?>">
                            <i class="fas fa-home me-2"></i>Back to Site
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><?php echo $page_title; ?></h2>
                        <a href="<?php echo base_url('admin/blog_admin'); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Posts
                        </a>
                    </div>

                    <!-- Flash Messages -->
                    <?php if ($this->session->flashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('success'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (validation_errors()): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo validation_errors(); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <!-- Add New Category -->
                        <div class="col-lg-4">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-plus me-2"></i>Add New Category</h5>
                                </div>
                                <div class="card-body">
                                    <form method="post">
                                        <div class="mb-3">
                                            <label for="name" class="form-label">Category Name *</label>
                                            <input type="text" class="form-control" id="name" name="name" 
                                                   value="<?php echo set_value('name'); ?>" required>
                                        </div>

                                        <div class="mb-3">
                                            <label for="description" class="form-label">Description</label>
                                            <textarea class="form-control" id="description" name="description" 
                                                      rows="3" placeholder="Optional category description..."><?php echo set_value('description'); ?></textarea>
                                        </div>

                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-save me-2"></i>Add Category
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Categories List -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>Existing Categories</h5>
                                </div>
                                <div class="card-body">
                                    <?php if (!empty($categories)): ?>
                                        <div class="table-responsive">
                                            <table class="table table-hover">
                                                <thead>
                                                    <tr>
                                                        <th>Name</th>
                                                        <th>Slug</th>
                                                        <th>Description</th>
                                                        <th>Posts</th>
                                                        <th>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($categories as $category): ?>
                                                        <tr>
                                                            <td>
                                                                <strong><?php echo htmlspecialchars($category->name); ?></strong>
                                                            </td>
                                                            <td>
                                                                <code><?php echo htmlspecialchars($category->slug); ?></code>
                                                            </td>
                                                            <td>
                                                                <?php if ($category->description): ?>
                                                                    <?php echo htmlspecialchars(word_limiter($category->description, 10)); ?>
                                                                <?php else: ?>
                                                                    <span class="text-muted">No description</span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td>
                                                                <?php
                                                                // Count posts in this category
                                                                $this->db->where('category_id', $category->id);
                                                                $post_count = $this->db->count_all_results('blog_posts');
                                                                ?>
                                                                <span class="badge bg-secondary"><?php echo $post_count; ?></span>
                                                            </td>
                                                            <td>
                                                                <div class="btn-group btn-group-sm">
                                                                    <a href="<?php echo base_url('blog/category/' . $category->slug); ?>" 
                                                                       class="btn btn-outline-primary" target="_blank" title="View">
                                                                        <i class="fas fa-eye"></i>
                                                                    </a>
                                                                    <button type="button" class="btn btn-outline-secondary" 
                                                                            onclick="editCategory(<?php echo $category->id; ?>, '<?php echo addslashes($category->name); ?>', '<?php echo addslashes($category->description); ?>')" 
                                                                            title="Edit">
                                                                        <i class="fas fa-edit"></i>
                                                                    </button>
                                                                    <?php if ($post_count == 0): ?>
                                                                        <button type="button" class="btn btn-outline-danger" 
                                                                                onclick="deleteCategory(<?php echo $category->id; ?>)" title="Delete">
                                                                            <i class="fas fa-trash"></i>
                                                                        </button>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="text-center py-4">
                                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                                            <h5>No categories yet</h5>
                                            <p class="text-muted">Create your first category to organize your blog posts.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Category</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editCategoryForm" method="post">
                    <div class="modal-body">
                        <input type="hidden" id="edit_category_id" name="category_id">
                        
                        <div class="mb-3">
                            <label for="edit_name" class="form-label">Category Name *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required>
                        </div>

                        <div class="mb-3">
                            <label for="edit_description" class="form-label">Description</label>
                            <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Update Category
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function editCategory(id, name, description) {
            document.getElementById('edit_category_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_description').value = description;
            
            const modal = new bootstrap.Modal(document.getElementById('editCategoryModal'));
            modal.show();
        }

        function deleteCategory(categoryId) {
            if (confirm('Are you sure you want to delete this category? This action cannot be undone.')) {
                // Create a form to submit the delete request
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '<?php echo base_url("admin/blog_admin/delete_category/"); ?>' + categoryId;
                
                // Add CSRF token if needed
                const csrfInput = document.createElement('input');
                csrfInput.type = 'hidden';
                csrfInput.name = 'delete_category';
                csrfInput.value = '1';
                form.appendChild(csrfInput);
                
                document.body.appendChild(form);
                form.submit();
            }
        }

        // Handle edit form submission
        document.getElementById('editCategoryForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const categoryId = formData.get('category_id');
            
            // Create a form to submit the update
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?php echo base_url("admin/blog_admin/edit_category/"); ?>' + categoryId;
            
            // Add form data
            for (let [key, value] of formData.entries()) {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = key;
                input.value = value;
                form.appendChild(input);
            }
            
            document.body.appendChild(form);
            form.submit();
        });
    </script>
</body>
</html>
