<?php
function send_sms($sender,$message)
{
	$data = array(
		'msisdn' => $sender,
		'message' => $message
	);

	try {
		$ch = curl_init();
//            $endpoint = "https://537e97fa4f17.ngrok.io/sendsms";
		$endpoint = "https://api.sandbox.namecheap.com/xml.response";
		//Set your auth headers
//        curl_setopt($ch, CURLOPT_HEADER, true);
		curl_setopt($ch, CURLOPT_HTTPHEADER, array(
			"Content-Type: application/json",
//                "Content-Length: " . strlen(json_encode($data))
		));

		curl_setopt($ch, CURLOPT_URL, $endpoint);
		curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "GET");
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
		curl_setopt($ch, CURLOPT_POST, 1);
		curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
//        echo json_encode($data);

		$result = curl_exec($ch);
//        $info = curl_getinfo($ch);
		//var_dump($result);

		curl_close($ch);

		//'$final = json_decode($result,TRUE);

		$final = json_decode($result);
	} catch(Exception $e){
		return $e;
	}
}
