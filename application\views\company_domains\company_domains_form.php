<div class="content-wrapper">

	<!-- Row start -->
	<div class="row gutters">
		<div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

			<div class="card">
				<div class="card-body">
        <h2 style="margin-top:0px">Company domains <?php echo $button ?></h2>
        <form action="<?php echo $action; ?>" method="post">
	   <div class="row">
           <div class="col-lg-4">
               <label for="varchar">Primary Domain Name (do not start with http or https or www use ie example,com) <?php echo form_error('domain_name') ?></label>
               <input type="text" class="form-control" name="domain_name" id="domain_name"  placeholder="ie example.com" value="<?php echo $domain_name; ?>" />
           </div>
           <div class="col-lg-4">
               <label for="int">Domain owner customer (If company is not registered go to customer on menu)  <?php echo form_error('company_id') ?></label>
               <select class="form-control" name="company_id" id="company">
                   <option value="">--select select--</option>
                   <?php

                   $c = $this->Company_model->get_all();
                   foreach ($c as $item){
                       ?>
                       <option value="<?php  echo $item->company_id?>" <?php  if($company_id==$item->company_id){echo "selected";} ?>><?php  echo $item->company_name?></option>
                       <?php
                   }
                   ?>
               </select>
           </div>
           <div class="col-lg-4">
               <label for="varchar">Domain File Path (<font color="red">Unique folder for this domain ( system will point to that folder, Please got to your cpanel and create it first and should be inside the chosen company folder)</font> <?php echo form_error('domain_file_path') ?></label>
               <input type="text" class="form-control" name="domain_file_path" id="domain_file_path" placeholder="Domain File Path" value="<?php echo $domain_file_path; ?>" />
           </div>
       </div>
            <div class="row">
                <div class="col-lg-6">
                    <label for="varchar"> Domain Expiry date </label>
                    <input type="date" class="form-control" name="domain_expiry_date"  placeholder="Domain expiry date" required  />
                </div>
                <div class="col-lg-6">
                    <label for="varchar">Domain Amount Paid (if client purchased only hosting put 0) </label>
                    <input type="text" class="form-control" name="domain_amount" id="expiry_date" placeholder="Amount for domain" required />
                </div>
            </div>

            <div class="row">
                <div class="col-lg-6">
                    <?php
                    $payment_m = get_all('payment_methods');
                    ?>
                    <label for="varchar">Payment method (if non on dropdown you can add one click add Payment method on menu)</label>
                    <select name="payment_method" class="form-control" id="" required>
                        <option value="">--select payment method used--</option>
                        <?php
                        foreach ($payment_m as $pm){
                            ?>
                            <option value="<?php echo $pm->payment_method_id; ?>"><?php echo $pm->name; ?></option>
                            <?php
                        }
                        ?>
                    </select>
                </div>
                <div class="col-lg-6">
                    <label for="varchar">Payment reference </label>
                    <input type="text" class="form-control" name="reference"  placeholder="Payment reference"  required />
                </div>
            </div>
	    <input type="hidden" name="domain_id" value="<?php echo $domain_id; ?>" /> 
	    <button type="submit" class="btn btn-primary"><?php echo $button ?></button> 
	    <a href="<?php echo site_url('company_domains') ?>" class="btn btn-default">Cancel</a>
	</form>
				</div>
			</div>
		</div>
	</div>
</div>
