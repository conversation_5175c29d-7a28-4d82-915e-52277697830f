<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Blog_management extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Blog_model');
        $this->load->library('form_validation');
        $this->load->library('upload');
        $this->load->helper('url');
        $this->load->helper('text');
        
        // Check if user is logged in (same as Company controller)
        if (!$this->session->userdata('user_id')) {
            redirect('Login');
        }
    }

    /**
     * Blog posts listing
     */
    public function index()
    {
        $data = array();
        
        // Get all posts (including drafts for admin)
        $this->db->select('bp.*, bc.name as category_name, u.full_name as author_name');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_categories bc', 'bp.category_id = bc.id', 'left');
        $this->db->join('users u', 'bp.author_id = u.user_id', 'left');
        $this->db->order_by('bp.created_at', 'DESC');
        
        $data['posts'] = $this->db->get()->result();
        
        $config['active_menu'] = "server_config";
        $config['current_link'] = "blog_posts";
        $this->load->view('header', $config);
        $this->load->view('blog_management/index', $data);
        $this->load->view('footer');
    }

    /**
     * Create new blog post
     */
    public function create() 
    {
        $data = array();
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('title', 'Title', 'required|max_length[255]');
            $this->form_validation->set_rules('content', 'Content', 'required');
            $this->form_validation->set_rules('category_id', 'Category', 'integer');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[draft,published,archived]');
            
            if ($this->form_validation->run() === TRUE) {
                // Handle file upload
                $featured_image = null;
                if (!empty($_FILES['featured_image']['name'])) {
                    $featured_image = $this->upload_featured_image();
                }
                
                // Generate slug
                $slug = $this->generate_slug($this->input->post('title'));
                
                $post_data = array(
                    'title' => $this->input->post('title'),
                    'slug' => $slug,
                    'content' => $this->input->post('content'),
                    'excerpt' => $this->input->post('excerpt'),
                    'featured_image' => $featured_image,
                    'category_id' => $this->input->post('category_id') ?: null,
                    'author_id' => $this->session->userdata('user_id'),
                    'status' => $this->input->post('status'),
                    'meta_title' => $this->input->post('meta_title'),
                    'meta_description' => $this->input->post('meta_description'),
                    'meta_keywords' => $this->input->post('meta_keywords')
                );
                
                if ($this->input->post('status') === 'published') {
                    $post_data['published_at'] = date('Y-m-d H:i:s');
                }
                
                $this->db->insert('blog_posts', $post_data);
                $post_id = $this->db->insert_id();
                
                // Handle tags
                $this->save_post_tags($post_id, $this->input->post('tags'));
                
                $this->session->set_flashdata('message', 'Blog post created successfully!');
                redirect('blog_management');
            }
        }
        
        // Get categories for dropdown
        $data['categories'] = $this->Blog_model->get_categories();
        
        $config['active_menu'] = "server_config";
        $config['current_link'] = "blog_create";
        $this->load->view('header', $config);
        $this->load->view('blog_management/create', $data);
        $this->load->view('footer');
    }

    /**
     * Edit blog post
     */
    public function edit($id) 
    {
        $data = array();
        
        // Get post
        $this->db->select('bp.*, GROUP_CONCAT(bt.name) as tags');
        $this->db->from('blog_posts bp');
        $this->db->join('blog_post_tags bpt', 'bp.id = bpt.post_id', 'left');
        $this->db->join('blog_tags bt', 'bpt.tag_id = bt.id', 'left');
        $this->db->where('bp.id', $id);
        $this->db->group_by('bp.id');
        
        $post = $this->db->get()->row();
        
        if (!$post) {
            $this->session->set_flashdata('message', 'Post not found');
            redirect('blog_management');
        }
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('title', 'Title', 'required|max_length[255]');
            $this->form_validation->set_rules('content', 'Content', 'required');
            $this->form_validation->set_rules('category_id', 'Category', 'integer');
            $this->form_validation->set_rules('status', 'Status', 'required|in_list[draft,published,archived]');
            
            if ($this->form_validation->run() === TRUE) {
                // Handle file upload
                $featured_image = $post->featured_image;
                if (!empty($_FILES['featured_image']['name'])) {
                    $featured_image = $this->upload_featured_image();
                    // Delete old image if exists
                    if ($post->featured_image && file_exists('./uploads/blog/' . $post->featured_image)) {
                        unlink('./uploads/blog/' . $post->featured_image);
                    }
                }
                
                // Generate new slug if title changed
                $slug = $post->slug;
                if ($this->input->post('title') !== $post->title) {
                    $slug = $this->generate_slug($this->input->post('title'));
                }
                
                $post_data = array(
                    'title' => $this->input->post('title'),
                    'slug' => $slug,
                    'content' => $this->input->post('content'),
                    'excerpt' => $this->input->post('excerpt'),
                    'featured_image' => $featured_image,
                    'category_id' => $this->input->post('category_id') ?: null,
                    'status' => $this->input->post('status'),
                    'meta_title' => $this->input->post('meta_title'),
                    'meta_description' => $this->input->post('meta_description'),
                    'meta_keywords' => $this->input->post('meta_keywords')
                );
                
                // Set published_at if status changed to published
                if ($this->input->post('status') === 'published' && $post->status !== 'published') {
                    $post_data['published_at'] = date('Y-m-d H:i:s');
                }
                
                $this->db->where('id', $id);
                $this->db->update('blog_posts', $post_data);
                
                // Handle tags
                $this->save_post_tags($id, $this->input->post('tags'));
                
                $this->session->set_flashdata('message', 'Blog post updated successfully!');
                redirect('blog_management');
            }
        }
        
        $data['post'] = $post;
        $data['categories'] = $this->Blog_model->get_categories();
        
        $config['active_menu'] = "server_config";
        $config['current_link'] = "blog_edit";
        $this->load->view('header', $config);
        $this->load->view('blog_management/edit', $data);
        $this->load->view('footer');
    }

    /**
     * Delete blog post
     */
    public function delete($id) 
    {
        $post = $this->db->get_where('blog_posts', array('id' => $id))->row();
        
        if (!$post) {
            $this->session->set_flashdata('message', 'Post not found');
            redirect('blog_management');
        }
        
        // Delete featured image if exists
        if ($post->featured_image && file_exists('./uploads/blog/' . $post->featured_image)) {
            unlink('./uploads/blog/' . $post->featured_image);
        }
        
        // Delete post (cascading will handle tags and comments)
        $this->db->where('id', $id);
        $this->db->delete('blog_posts');
        
        $this->session->set_flashdata('message', 'Blog post deleted successfully!');
        redirect('blog_management');
    }

    /**
     * Manage categories
     */
    public function categories() 
    {
        $data = array();
        
        if ($this->input->method() === 'post') {
            $this->form_validation->set_rules('name', 'Category Name', 'required|max_length[255]');
            
            if ($this->form_validation->run() === TRUE) {
                $category_data = array(
                    'name' => $this->input->post('name'),
                    'slug' => $this->generate_slug($this->input->post('name')),
                    'description' => $this->input->post('description')
                );
                
                $this->db->insert('blog_categories', $category_data);
                $this->session->set_flashdata('message', 'Category created successfully!');
                redirect('blog_management/categories');
            }
        }
        
        $data['categories'] = $this->Blog_model->get_categories();
        
        $config['active_menu'] = "server_config";
        $config['current_link'] = "blog_categories";
        $this->load->view('header', $config);
        $this->load->view('blog_management/categories', $data);
        $this->load->view('footer');
    }

    /**
     * Manage comments
     */
    public function comments() 
    {
        $data = array();
        
        // Get all comments
        $this->db->select('bc.*, bp.title as post_title, bp.slug as post_slug');
        $this->db->from('blog_comments bc');
        $this->db->join('blog_posts bp', 'bc.post_id = bp.id');
        $this->db->order_by('bc.created_at', 'DESC');
        
        $data['comments'] = $this->db->get()->result();
        
        $config['active_menu'] = "server_config";
        $config['current_link'] = "blog_comments";
        $this->load->view('header', $config);
        $this->load->view('blog_management/comments', $data);
        $this->load->view('footer');
    }

    /**
     * Approve comment
     */
    public function approve_comment($id) 
    {
        $this->db->where('id', $id);
        $this->db->update('blog_comments', array('status' => 'approved'));
        
        $this->session->set_flashdata('message', 'Comment approved!');
        redirect('blog_management/comments');
    }

    /**
     * Delete comment
     */
    public function delete_comment($id) 
    {
        $this->db->where('id', $id);
        $this->db->delete('blog_comments');
        
        $this->session->set_flashdata('message', 'Comment deleted!');
        redirect('blog_management/comments');
    }

    // ========== HELPER METHODS ==========

    /**
     * Upload featured image
     */
    private function upload_featured_image() {
        $config['upload_path'] = './uploads/blog/';
        $config['allowed_types'] = 'gif|jpg|png|jpeg|webp';
        $config['max_size'] = 2048; // 2MB
        $config['encrypt_name'] = TRUE;
        
        // Create directory if it doesn't exist
        if (!is_dir($config['upload_path'])) {
            mkdir($config['upload_path'], 0755, true);
        }
        
        $this->upload->initialize($config);
        
        if ($this->upload->do_upload('featured_image')) {
            $upload_data = $this->upload->data();
            return $upload_data['file_name'];
        }
        
        return null;
    }

    /**
     * Generate URL slug
     */
    private function generate_slug($title) {
        $slug = url_title(convert_accented_characters($title), 'dash', TRUE);
        
        // Check if slug exists
        $original_slug = $slug;
        $counter = 1;
        
        while ($this->slug_exists($slug)) {
            $slug = $original_slug . '-' . $counter;
            $counter++;
        }
        
        return $slug;
    }

    /**
     * Check if slug exists
     */
    private function slug_exists($slug) {
        $this->db->where('slug', $slug);
        return $this->db->count_all_results('blog_posts') > 0;
    }

    /**
     * Save post tags
     */
    private function save_post_tags($post_id, $tags_string) {
        // Delete existing tags
        $this->db->where('post_id', $post_id);
        $this->db->delete('blog_post_tags');
        
        if (empty($tags_string)) {
            return;
        }
        
        $tags = array_map('trim', explode(',', $tags_string));
        
        foreach ($tags as $tag_name) {
            if (empty($tag_name)) continue;
            
            $tag_slug = url_title(convert_accented_characters($tag_name), 'dash', TRUE);
            
            // Check if tag exists
            $this->db->where('slug', $tag_slug);
            $existing_tag = $this->db->get('blog_tags')->row();
            
            if ($existing_tag) {
                $tag_id = $existing_tag->id;
            } else {
                // Create new tag
                $tag_data = array(
                    'name' => $tag_name,
                    'slug' => $tag_slug
                );
                $this->db->insert('blog_tags', $tag_data);
                $tag_id = $this->db->insert_id();
            }
            
            // Link tag to post
            $this->db->insert('blog_post_tags', array(
                'post_id' => $post_id,
                'tag_id' => $tag_id
            ));
        }
    }
}
