-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.0.2
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1:3306
-- Generation Time: May 20, 2022 at 11:11 PM
-- Server version: 5.7.31-log
-- PHP Version: 7.4.9

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `extendcpanel`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin`
--

DROP TABLE IF EXISTS `admin`;
CREATE TABLE IF NOT EXISTS `admin` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(200) NOT NULL,
  `password` varchar(200) NOT NULL,
  `fullname` varchar(200) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `company`
--

DROP TABLE IF EXISTS `company`;
CREATE TABLE IF NOT EXISTS `company` (
  `company_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(200) NOT NULL,
  `company_email` varchar(200) NOT NULL,
  `company_phone` varchar(200) NOT NULL,
  `company_dir` varchar(200) NOT NULL,
  `primary_domain` varchar(200) NOT NULL,
  `company_stamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `company_database`
--

DROP TABLE IF EXISTS `company_database`;
CREATE TABLE IF NOT EXISTS `company_database` (
  `database_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `name` varchar(200) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`database_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `company_domains`
--

DROP TABLE IF EXISTS `company_domains`;
CREATE TABLE IF NOT EXISTS `company_domains` (
  `domain_id` int(11) NOT NULL AUTO_INCREMENT,
  `domain_name` varchar(200) NOT NULL,
  `company_id` int(11) NOT NULL,
  `domain_file_path` varchar(200) NOT NULL,
  `domain_stamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`domain_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `company_emails`
--

DROP TABLE IF EXISTS `company_emails`;
CREATE TABLE IF NOT EXISTS `company_emails` (
  `email_id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(200) NOT NULL,
  `domain` varchar(200) NOT NULL,
  `company` int(11) NOT NULL,
  `suspension` varchar(200) NOT NULL DEFAULT 'allow_incoming_email',
  `outgoing` varchar(200) NOT NULL DEFAULT 'allow',
  `login` enum('allow','suspend') NOT NULL DEFAULT 'allow',
  `email_stamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`email_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `cpanel_settings`
--

DROP TABLE IF EXISTS `cpanel_settings`;
CREATE TABLE IF NOT EXISTS `cpanel_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `hosting_ip` varchar(50) DEFAULT NULL,
  `username` varchar(200) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `cpanel_user` varchar(200) NOT NULL,
  `cpanel_root_path` varchar(200) NOT NULL,
  `logo` varchar(200) DEFAULT NULL,
  `company_name` varchar(200) DEFAULT NULL,
  `company_phone` varchar(200) NOT NULL,
  `company_email` varchar(200) NOT NULL,
  `currency` varchar(200) DEFAULT NULL,
  `login_page_heading` varchar(200) DEFAULT NULL,
  `login_page_subtextt` text,
  `protocal` varchar(200) DEFAULT 'smtp',
  `email_host` varchar(200) DEFAULT NULL,
  `email_port` varchar(200) DEFAULT '465',
  `email_user` varchar(200) DEFAULT NULL,
  `email_pass` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;

--
-- Dumping data for table `cpanel_settings`
--

INSERT INTO `cpanel_settings` (`id`, `hosting_ip`, `username`, `password`, `cpanel_user`, `cpanel_root_path`, `logo`, `company_name`, `company_phone`, `company_email`, `currency`, `login_page_heading`, `login_page_subtextt`, `protocal`, `email_host`, `email_port`, `email_user`, `email_pass`) VALUES
(1, '', '', '', 'malawim2', '/home/<USER>/public_html/', '62867f842988c1466.png', 'Extend Cpanel', '+265994099461', '<EMAIL>', '$', 'WEBSITE HOSTING MADE SIMPLE', 'With Our premium hosting you may get unlimited space, email and even bandwidth. Free wordpress installation and customized cpanel. ', 'smtp', 'extendcpanel.com', '465', '<EMAIL>', 'Qwerty@fhfh');

-- --------------------------------------------------------

--
-- Table structure for table `database_user`
--

DROP TABLE IF EXISTS `database_user`;
CREATE TABLE IF NOT EXISTS `database_user` (
  `database_user` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `username` varchar(200) NOT NULL,
  `date_added` datetime NOT NULL,
  PRIMARY KEY (`database_user`)
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=latin1;

--
-- Dumping data for table `database_user`
--

INSERT INTO `database_user` (`database_user`, `company_id`, `username`, `date_added`) VALUES
(1, 1, 'zoba', '0000-00-00 00:00:00'),
(2, 1, 'boy', '0000-00-00 00:00:00'),
(3, 1, 'seconda', '0000-00-00 00:00:00'),
(4, 1, 'sda', '0000-00-00 00:00:00'),
(5, 1, 'okboy', '0000-00-00 00:00:00'),
(6, 1, 'moveon', '0000-00-00 00:00:00'),
(7, 1, 'mnbgj', '0000-00-00 00:00:00'),
(8, 1, 'memory', '0000-00-00 00:00:00'),
(9, 1, 'kjh', '0000-00-00 00:00:00'),
(10, 1, 'bjgfdhr', '0000-00-00 00:00:00'),
(11, 1, 'hjgh', '0000-00-00 00:00:00'),
(12, 1, 'user1hsg', '0000-00-00 00:00:00'),
(13, 1, 'sdfa', '0000-00-00 00:00:00'),
(14, 1, 'daf', '0000-00-00 00:00:00'),
(15, 1, 'mim', '0000-00-00 00:00:00'),
(16, 1, 'llj', '0000-00-00 00:00:00'),
(17, 1, 'zoona', '0000-00-00 00:00:00'),
(18, 1, 'fia', '0000-00-00 00:00:00'),
(19, 1, 'malawim2_infocus_bok', '0000-00-00 00:00:00'),
(20, 1, 'malawim2_infocus_bro', '0000-00-00 00:00:00'),
(21, 1, 'infocus_finalt', '0000-00-00 00:00:00'),
(22, 1, 'infocus_kuyesa', '0000-00-00 00:00:00'),
(23, 1, 'infocus_boys', '0000-00-00 00:00:00'),
(24, 1, 'infocus_usem', '0000-00-00 00:00:00'),
(25, 1, 'infocus_chisomo', '0000-00-00 00:00:00'),
(26, 3, 'aphodo_manu', '0000-00-00 00:00:00'),
(27, 7, 'malawifreedomnetwork_my', '0000-00-00 00:00:00');

-- --------------------------------------------------------

--
-- Table structure for table `db_user_privileges`
--

DROP TABLE IF EXISTS `db_user_privileges`;
CREATE TABLE IF NOT EXISTS `db_user_privileges` (
  `p_id` int(11) NOT NULL AUTO_INCREMENT,
  `user` varchar(200) NOT NULL,
  `company_id` int(11) NOT NULL,
  `privilege` varchar(100) NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`p_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `directory`
--

DROP TABLE IF EXISTS `directory`;
CREATE TABLE IF NOT EXISTS `directory` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user` int(11) NOT NULL,
  `user_dir` varchar(200) NOT NULL,
  `date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;

--
-- Dumping data for table `directory`
--

INSERT INTO `directory` (`id`, `user`, `user_dir`, `date`) VALUES
(1, 1, 'tools', '2021-09-14 00:28:38'),
(2, 2, 'infocus', '2021-09-14 00:36:10');

-- --------------------------------------------------------

--
-- Table structure for table `email_forwarder`
--

DROP TABLE IF EXISTS `email_forwarder`;
CREATE TABLE IF NOT EXISTS `email_forwarder` (
  `email_forwarder_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `forwarder` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `forwarded_to` text COLLATE utf8_unicode_ci NOT NULL,
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`email_forwarder_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `ftp_accounts`
--

DROP TABLE IF EXISTS `ftp_accounts`;
CREATE TABLE IF NOT EXISTS `ftp_accounts` (
  `ftp_id` int(11) NOT NULL AUTO_INCREMENT,
  `company_id` int(11) NOT NULL,
  `username` varchar(200) NOT NULL,
  `domain` varchar(200) NOT NULL,
  `homedir` varchar(200) NOT NULL,
  `quota` varchar(200) NOT NULL,
  `quota_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`ftp_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE IF NOT EXISTS `payment_methods` (
  `payment_method_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `stamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`payment_method_id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`payment_method_id`, `name`, `stamp`) VALUES
(1, 'Cash', '2022-05-17 13:09:57'),
(2, 'Paypal direct pay', '2022-05-17 13:10:33'),
(3, 'Bank', '2022-05-17 13:11:03');

-- --------------------------------------------------------

--
-- Table structure for table `product_life`
--

DROP TABLE IF EXISTS `product_life`;
CREATE TABLE IF NOT EXISTS `product_life` (
  `product_life_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_type` enum('Hosting','Domain') COLLATE utf8_unicode_ci NOT NULL,
  `product_id` int(11) NOT NULL,
  `product_name` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `product_price` decimal(18,2) NOT NULL,
  `expire_date` date NOT NULL,
  `product_owner` int(11) NOT NULL,
  `updated_date` date NOT NULL,
  PRIMARY KEY (`product_life_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `product_payments`
--

DROP TABLE IF EXISTS `product_payments`;
CREATE TABLE IF NOT EXISTS `product_payments` (
  `product_payment_id` int(11) NOT NULL AUTO_INCREMENT,
  `product_life_id` int(11) NOT NULL,
  `payment_method` int(11) NOT NULL,
  `reference` varchar(200) COLLATE utf8_unicode_ci NOT NULL,
  `amount` decimal(18,2) NOT NULL,
  `payment_date` date NOT NULL,
  `pop` varchar(200) COLLATE utf8_unicode_ci DEFAULT NULL,
  `stamp` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`product_payment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `sub_domains`
--

DROP TABLE IF EXISTS `sub_domains`;
CREATE TABLE IF NOT EXISTS `sub_domains` (
  `sub_domain_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(200) NOT NULL,
  `domain` varchar(200) NOT NULL,
  `dir` varchar(200) NOT NULL,
  `company` int(11) NOT NULL,
  `sub_domain_stamp` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`sub_domain_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
CREATE TABLE IF NOT EXISTS `users` (
  `user_id` int(11) NOT NULL AUTO_INCREMENT,
  `full_name` varchar(200) NOT NULL,
  `email` varchar(200) NOT NULL,
  `password` varchar(100) NOT NULL,
  `company` int(11) NOT NULL,
  `profile_photo` varchar(200) DEFAULT 'avatar.png',
  `user_role` int(11) NOT NULL DEFAULT '2',
  `date_added` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
