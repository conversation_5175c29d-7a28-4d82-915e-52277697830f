<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

include_once dirname(__FILE__).DIRECTORY_SEPARATOR.'php/elFinderConnector.class.php';
include_once dirname(__FILE__).DIRECTORY_SEPARATOR.'php/elFinder.class.php';
include_once dirname(__FILE__).DIRECTORY_SEPARATOR.'php/elFinderVolumeDriver.class.php';
include_once dirname(__FILE__).DIRECTORY_SEPARATOR.'php/elFinderVolumeLocalFileSystem.class.php';

class Elfinder_lib 
{
  public function __construct($opts) 
  {
    $connector = new elFinderConnector(new elFinder($opts));
    $connector->run();
  }
}
