<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <!-- Card start -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title d-flex justify-content-between align-items-center">
                        <h4>Blog Posts Management</h4>
                        <a href="<?php echo base_url('Blog_management/create'); ?>" class="btn btn-primary">
                            <i class="icon-plus"></i> Add New Post
                        </a>
                    </div>
                </div>
                <div class="card-body">

                    <?php if ($this->session->flashdata('message')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('message'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($posts)): ?>
                        <!-- Table start -->
                        <div class="table-responsive">
                            <table id="copy-print-csv" class="table v-middle">
                                <thead>
                                    <tr>
                                        <th>Title</th>
                                        <th>Author</th>
                                        <th>Category</th>
                                        <th>Status</th>
                                        <th>Views</th>
                                        <th>Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($posts as $post): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <?php if ($post->featured_image): ?>
                                                        <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" 
                                                             alt="" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                    <?php else: ?>
                                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                             style="width: 50px; height: 50px;">
                                                            <i class="icon-image text-muted"></i>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div>
                                                        <h6 class="mb-0"><?php echo htmlspecialchars($post->title); ?></h6>
                                                        <small class="text-muted"><?php echo $post->slug; ?></small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($post->author_name ?: 'Unknown'); ?></td>
                                            <td>
                                                <?php if ($post->category_name): ?>
                                                    <span class="badge bg-secondary"><?php echo htmlspecialchars($post->category_name); ?></span>
                                                <?php else: ?>
                                                    <span class="text-muted">Uncategorized</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php
                                                $status_class = '';
                                                switch($post->status) {
                                                    case 'published': $status_class = 'bg-success'; break;
                                                    case 'draft': $status_class = 'bg-warning'; break;
                                                    case 'archived': $status_class = 'bg-secondary'; break;
                                                }
                                                ?>
                                                <span class="badge <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($post->status); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <i class="icon-eye text-muted me-1"></i>
                                                <?php echo number_format($post->views); ?>
                                            </td>
                                            <td>
                                                <small>
                                                    <?php echo date('M j, Y', strtotime($post->created_at)); ?><br>
                                                    <span class="text-muted"><?php echo date('g:i A', strtotime($post->created_at)); ?></span>
                                                </small>
                                            </td>
                                            <td>
                                                <div class="actions">
                                                    <?php if ($post->status === 'published'): ?>
                                                        <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" 
                                                           class="btn btn-outline-primary btn-sm" target="_blank" title="View">
                                                            <i class="icon-eye"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <a href="<?php echo base_url('Blog_management/edit/' . $post->id); ?>" 
                                                       class="btn btn-outline-secondary btn-sm" title="Edit">
                                                        <i class="icon-edit"></i>
                                                    </a>
                                                    <button type="button" class="btn btn-outline-danger btn-sm" 
                                                            onclick="deletePost(<?php echo $post->id; ?>)" title="Delete">
                                                        <i class="icon-trash"></i>
                                                    </button>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <!-- Table end -->
                    <?php else: ?>
                        <div class="text-center py-5">
                            <i class="icon-file-text display-1 text-muted mb-3"></i>
                            <h5>No blog posts yet</h5>
                            <p class="text-muted">Create your first blog post to get started.</p>
                            <a href="<?php echo base_url('Blog_management/create'); ?>" class="btn btn-primary">
                                <i class="icon-plus me-2"></i>Create First Post
                            </a>
                        </div>
                    <?php endif; ?>

                </div>
            </div>
            <!-- Card end -->

        </div>
    </div>
    <!-- Row end -->

</div>
<!-- Content wrapper end -->

<script>
function deletePost(postId) {
    if (confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
        window.location.href = '<?php echo base_url("Blog_management/delete/"); ?>' + postId;
    }
}
</script>

<style>
.actions {
    display: flex;
    gap: 0.25rem;
}

.badge {
    font-size: 0.75rem;
}

.table td {
    vertical-align: middle;
}

.display-1 {
    font-size: 4rem;
}
</style>
