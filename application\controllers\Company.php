<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Company extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Company_model');
        $this->load->model('Product_life_model');
        $this->load->model('Product_payments_model');
        $this->load->model('Payment_methods_model');
        $this->load->model('Company_domains_model');
        $this->load->library('form_validation');
    }

    public function index()
    {


        $data = array(
            'datab' => $this->Company_model->get_all(),

        );
		$config['active_menu'] = "server_config";
		$config['current_link'] = "company";
		$this->load->view('header',$config);
        $this->load->view('companies', $data);
		$this->load->view('footer');
    }

    public function read($id) 
    {
        $row = $this->Company_model->get_by_id($id);
        if ($row) {
            $data = array(
		'company_id' => $row->company_id,
		'company_name' => $row->company_name,
		'company_email' => $row->company_email,
		'company_phone' => $row->company_phone,
		'company_dir' => $row->company_dir,
		'primary_domain' => $row->primary_domain,
		'company_stamp' => $row->company_stamp,
	    );
            $config['active_menu'] = "server_config";
            $config['current_link'] = "company";
            $this->load->view('header',$config);
            $this->load->view('company/company_read', $data);
            $this->load->view('footer');

        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company'));
        }
    }

    public  function details(){

             $row = $this->Company_model->get_by_id($this->session->userdata('company_id'));
        if ($row) {
            $data = array(
                'company_id' => $row->company_id,
                'company_name' => $row->company_name,
                'company_email' => $row->company_email,
                'company_phone' => $row->company_phone,
                'company_dir' => $row->company_dir,
                'primary_domain' => $row->primary_domain,
                'company_stamp' => $row->company_stamp,
            );
            $config['active_menu'] = "company";
            $config['current_link'] = "company_details";
            $this->load->view('header',$config);
            $this->load->view('company/company_read', $data);
            $this->load->view('footer');

        } else {
            echo "no access please ask admin";
        }
    }
    public function billing(){
        $data['product_life_data'] = $this->Product_life_model->get_all_by_id($this->session->userdata('company_id'));
        $config['active_menu'] = "company";
        $config['current_link'] = "billing";
        $this->load->view('header',$config);
        $this->load->view('company/details',$data);
        $this->load->view('footer');
    }
    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('company/create_action'),
	    'company_id' => set_value('company_id'),
	    'company_name' => set_value('company_name'),
	    'company_email' => set_value('company_email'),
	    'company_phone' => set_value('company_phone'),
	    'company_dir' => set_value('company_dir'),
	    'primary_domain' => set_value('primary_domain'),
	    'company_stamp' => set_value('company_stamp'),
	);
		$config['active_menu'] = "server_config";
		$config['current_link'] = "company";
		$this->load->view('header',$config);
		$this->load->view('company/company_form', $data);
		$this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'company_name' => $this->input->post('company_name',TRUE),
		'company_email' => $this->input->post('company_email',TRUE),
		'company_phone' => $this->input->post('company_phone',TRUE),
		'company_dir' => $this->input->post('company_dir',TRUE),
		'primary_domain' => $this->input->post('primary_domain',TRUE),

	    );

           $id= $this->Company_model->insert($data);


            $data2 = array(
                'domain_name' => $this->input->post('primary_domain',TRUE),
                'company_id' => $id,
                'domain_file_path' => $this->input->post('company_dir',TRUE),

            );


           $domains = $this->Company_domains_model->insert($data2);
            $hotsing_data = array(
                'product_type' => 'Hosting',
                'product_id' => $id,
                'product_name' => $this->input->post('company_name',TRUE),
                'product_price' => $this->input->post('hosting_amount',TRUE),
                'expire_date' => $this->input->post('hosting_expiry_date',TRUE),
                'product_owner' => $id,
                'updated_date' => date('Y-m-d'),
            );
            $domain_data = array(
                'product_type' => 'Domain',
                'product_id' => $domains,
                'product_name' => $this->input->post('primary_domain',TRUE),
                'product_price' => $this->input->post('domain_amount',TRUE),
                'expire_date' => $this->input->post('domain_expiry_date',TRUE),
                'product_owner' => $id,
                'updated_date' => date('Y-m-d'),
            );

           $life1 = $this->Product_life_model->insert($domain_data);
           $life2 = $this->Product_life_model->insert($hotsing_data);

            $hosting_product_payments = array(
                'product_life_id' => $life2,
                'payment_method' => $this->input->post('payment_method',TRUE),
                'reference' => $this->input->post('reference',TRUE),
                'amount' => $this->input->post('hosting_amount',TRUE),
                'payment_date' => date('Y-m-d'),
                
                
            );
            $domain_product_payments = array(
                'product_life_id' => $life1,
                'payment_method' => $this->input->post('payment_method',TRUE),
                'reference' => $this->input->post('reference',TRUE),
                'amount' => $this->input->post('domain_amount',TRUE),
                'payment_date' => date('Y-m-d'),
                
                
            );

            $this->Product_payments_model->insert($hosting_product_payments);
            $this->Product_payments_model->insert($domain_product_payments);
            $this->toaster->success('Create Record Success');
            redirect(site_url('company'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Company_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('company/update_action'),
		'company_id' => set_value('company_id', $row->company_id),
		'company_name' => set_value('company_name', $row->company_name),
		'company_email' => set_value('company_email', $row->company_email),
		'company_phone' => set_value('company_phone', $row->company_phone),
		'company_dir' => set_value('company_dir', $row->company_dir),
		'primary_domain' => set_value('primary_domain', $row->primary_domain),
		'company_stamp' => set_value('company_stamp', $row->company_stamp),
	    );
			$config['active_menu'] = "server_config";
			$config['current_link'] = "company";
			$this->load->view('header',$config);
			$this->load->view('company/edit', $data);
			$this->load->view('footer');
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('company_id', TRUE));
        } else {
            $data = array(
		'company_name' => $this->input->post('company_name',TRUE),
		'company_email' => $this->input->post('company_email',TRUE),
		'company_phone' => $this->input->post('company_phone',TRUE),
		'company_dir' => $this->input->post('company_dir',TRUE),
		'primary_domain' => $this->input->post('primary_domain',TRUE),

	    );

            $this->Company_model->update($this->input->post('company_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('company'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Company_model->get_by_id($id);

        if ($row) {
            $this->Company_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('company'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('company'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('company_name', 'company name', 'trim|required');
	$this->form_validation->set_rules('company_email', 'company email', 'trim|required');
	$this->form_validation->set_rules('company_phone', 'company phone', 'trim|required');
	$this->form_validation->set_rules('company_dir', 'company dir', 'trim|required');
	$this->form_validation->set_rules('primary_domain', 'primary domain', 'trim|required');

	$this->form_validation->set_rules('company_id', 'company_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

