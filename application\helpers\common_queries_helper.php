<?php
function get_all($id){


	$ci =& get_instance();
	$ci->load->database();
//	$ci->load->model('Dbc_users_model');

	$sql="SELECT * FROM $id ";
	return $query = $ci->db->query($sql)->result();


}
function check_exist_in_table($table,$field,$key){
	$ci =& get_instance();
	$ci->load->database();
//	$ci->load->model('Dbc_users_model');

	$sql="SELECT * FROM $table WHERE $field='$key'";
	return $query = $ci->db->query($sql)->row();

}
function get_by_id($table,$key,$value){


    $ci =& get_instance();
    $ci->load->database();
//	$ci->load->model('Dbc_users_model');

    $sql="SELECT * FROM $table  WHERE $key = '$value'";
    return $query = $ci->db->query($sql)->row();


}
function count_by_year($year,$month){


    $ci =& get_instance();
    $ci->load->database();
//	$ci->load->model('Dbc_users_model');

    $sql="SELECT * FROM company  WHERE YEAR (company_stamp)= $year AND MONTH (company_stamp) = $month";
    return $query = $ci->db->query($sql)->num_rows();


}function sum_year_rev($year){


    $ci =& get_instance();
    $ci->load->database();
//	$ci->load->model('Dbc_users_model');

    $sql="SELECT SUM(amount)  as total_sum FROM product_payments  WHERE YEAR (payment_date)= $year ";
    return $query = $ci->db->query($sql)->row();


}
