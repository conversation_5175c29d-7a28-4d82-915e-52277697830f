

<!-- Content wrapper start -->
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <div class="card">
           <div class="row">
               <div class="col-lg-6">
                   <form action="<?php echo base_url('Email_alert/notify_all')?>" method="post" style="padding: 1em;">
                       <label for="">Your will broadcast to the customer emails below.</label>
                       <br>
                   <?php
                   $count = 0;
                   foreach ($company as $c){
                       $count ++;
                   }
                   echo "<font color='red' size='25'>".$count." - Customers</font>";
                   ?>
                       <label for="">Subject </label>
                       <input type="text" name="subject" class="form-control"  placeholder="Email subject" required>
                       <br>
                       <label for="">Message</label>
                   <textarea name="message" id="" cols="30" rows="10" class="form-control" placeholder="Type your Message to broadcast"></textarea>
                       <br>
                       <button type="submit" class="btn btn-block btn-danger">Notify All</button>
                   </form>
               </div>
               <div class="col-lg-6">
                   <h4>Send to selected customers</h4>
                   <form action="<?php echo base_url('Email_alert/notify_selected')?>" method="post" style="padding: 1em;">
                       <label for="">Subject </label>
                       <input type="text" name="subject" class="form-control"  placeholder="Email subject" required>
                       <br>
                       <label for="">Message</label>
                   <textarea name="message" id="" cols="30" rows="10" class="form-control" required placeholder="Type your message to broadcast"></textarea>
                   <br>

                       <label for="">You can select multiple emails to be notified</label>
                   <select name="emails[]" id="sele" class="form-control" multiple required>
                       <option value="">--select customers--</option>
                        <?php

                   foreach ($company as $c){
                     ?>
                       <option value="<?php echo $c->company_email ?>"><?php echo $c->company_name."(".$c->company_email.")"?></option>
                       <?php
                   }

                   ?>
                   </select>
                       <br>
                   <button type="submit" class="btn btn-block btn-danger">Notify selected</button>
                   </form>
               </div>
           </div>
            </div>
        </div>
    </div>
</div>
