<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');
require APPPATH . '/libraries/CpanelApi.php';
class Ftp_accounts extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->confi = config_cpanel();
        $this->cpanel = new cpanelAPI($this->confi['servername'],$this->confi['password'],$this->confi['ip']);
        $this->load->model('Ftp_accounts_model');
		$this->load->model('Company_domains_model');
		$this->load->model('Company_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'ftp_accounts/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'ftp_accounts/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'ftp_accounts/index.html';
            $config['first_url'] = base_url() . 'ftp_accounts/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Ftp_accounts_model->total_rows($q);
        $ftp_accounts = $this->Ftp_accounts_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'ftp_accounts_data' => $ftp_accounts,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('ftp_accounts/ftp_accounts_list', $data);
    }

    public function manage($id)
    {
        $row = $this->Ftp_accounts_model->get_by_id2($id);
        if ($row) {
            $data = array(
		'ftp_id' => $row->ftp_id,
		'company_id' => $row->company_id,
		'username' => $row->username,
		'domain' => $row->domain,
		'homedir' => $row->homedir,
		'quota' => $row->quota,
		'quota_date' => $row->quota_date,
	    );
			$config['active_menu'] = "files";
			$config['current_link'] = "ftp";
			$this->load->view('header',$config);
            $this->load->view('ftp_accounts/ftp_accounts_read', $data);
            $this->load->view('footer');
        } else {

			$this->toaster->error('Error, No access to this data');

            redirect(site_url('files/ftp'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('ftp_accounts/create_action'),
	    'ftp_id' => set_value('ftp_id'),
	    'company_id' => set_value('company_id'),
	    'username' => set_value('username'),
	    'domain' => set_value('domain'),
	    'homedir' => set_value('homedir'),
	    'quota' => set_value('quota'),
	    'quota_date' => set_value('quota_date'),
	);
		$config['active_menu'] = "files";
		$config['current_link'] = "ftp";
		$this->load->view('header',$config);
        $this->load->view('ftp_accounts/ftp_accounts_form', $data);
		$this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'company_id' => $this->session->userdata('company_id'),
		'username' => $this->input->post('username',TRUE),
		'domain' => $this->input->post('domain',TRUE),
		'homedir' => $this->input->post('homedir',TRUE),
		'quota' => $this->input->post('quota',TRUE),

	    );

			$response = $this->cpanel->uapi->Ftp->add_ftp( array (
				'user' => $data['username'],
				'domain' => $data['domain'],
				'homedir' => '/home3/malawim2/public_html/'.$this->input->post('fullpath').'/'.$data['homedir'],
				'quota' => 'unlimited',

				'pass' => $this->input->post('password')

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Ftp_accounts_model->insert($data);
				$this->toaster->success('Success !, email  was added successfully');
				redirect(site_url('ftp_accounts/my_accounts'));
			}


        }
    }
    public function update_pass(){
		$response = $this->cpanel->uapi->Ftp->passwd( array (
			'user' => $this->input->post('username'),
			'domain' => $this->input->post('domain'),
			'pass' => $this->input->post('newpass')

		));
		if ($response->status == 0){
			$this->session->set_flashdata('error',$response->errors);
			redirect($_SERVER["HTTP_REFERER"]);
		}else{
			$this->toaster->success('Success !, ftp pass was changed successfully');
			redirect(site_url('ftp_accounts/my_accounts'));
		}


	}
    public function my_accounts(){
    	$data['data'] = $this->Ftp_accounts_model->get_mine($this->session->userdata("company_id"));
		$config['active_menu'] = "files";
		$config['current_link'] = "ftp";
		$this->load->view('header',$config);
		$this->load->view('my_ftp',$data);
		$this->load->view('footer');
	}

    
    public function delete($id) 
    {
        $row = $this->Ftp_accounts_model->get_by_id2($id);

        if ($row) {
			$response = $this->cpanel->uapi->Ftp->delete_ftp( array (
				'user' => $row->username,
				'domain' => $row->domain

			));
			if ($response->status == 0){
				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Ftp_accounts_model->delete($id);
				$this->toaster->success('Success !, delete successfully');
				redirect(site_url('ftp_accounts/my_accounts'));
			}



        } else{
			$this->toaster->error('Error !, delete failed');
			redirect(site_url('ftp_accounts/my_accounts'));
        }
    }

    public function _rules() 
    {

	$this->form_validation->set_rules('username', 'username', 'trim|required');
	$this->form_validation->set_rules('domain', 'domain', 'trim|required');
	$this->form_validation->set_rules('homedir', 'homedir', 'trim|required');
	$this->form_validation->set_rules('quota', 'quota', 'trim|required');


	$this->form_validation->set_rules('ftp_id', 'ftp_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}


