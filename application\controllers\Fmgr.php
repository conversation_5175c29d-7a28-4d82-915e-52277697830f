 <?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class Fmgr extends CI_Controller {

	public function __construct()	{
		parent::__construct();
		$this->load->model('Company_model');
	}

	public function index()	{

		$config['active_menu'] = "files";
		$config['current_link'] = "files";
		$this->load->view('header',$config);
		$this->load->view('fmgr_view');
		$this->load->view('footer');

	}

	function elfinder_init(){
	    $r = $this->Company_model->get_by_id($this->session->userdata('company_id'));
		$pp = '../../'.$r->company_dir;
		$pp2 = $r->company_dir;
	  $this->load->helper('path');
	  $opts = array(
	     'debug' => true,
	    'roots' => array(
	      array( 
	        'driver' => 'LocalFileSystem', 
	        'path'   => set_realpath(realpath(APPPATH.$pp)),
	        'URL'    => base_url($pp2) . '/'
		  		)
	            )
	               );
	  $this->load->library('Elfinder_lib', $opts);
	}
	function wordpress(){
		$this->load->view('wp');
	}

}

/* End of file Fmgr.php */
/* Location: ./application/controllers/Fmgr.php */
 ?>
