<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');
require APPPATH . '/libraries/CpanelApi.php';
class Company_emails extends CI_Controller
{

    function __construct()
    {
        parent::__construct();
        $this->confi = config_cpanel();
		$this->cpanel = new cpanelAPI($this->confi['servername'],$this->confi['password'],$this->confi['ip']);
        $this->load->model('Company_emails_model');
        $this->load->model('Company_domains_model');
        $this->load->model('Company_model');
        $this->load->library('form_validation');
    }
public function my_emails(){
	$data['data'] = $this->Company_emails_model->get_my_emails($this->session->userdata('company_id'));
	$config['active_menu'] = "emails";
	$config['current_link'] = "emails";
	$this->load->view('header',$config);
	$this->load->view('my_emails',$data);
	$this->load->view('footer');
}
function tryit(){
    echo $this->confi['ip'];

}
    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'company_emails/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'company_emails/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'company_emails/index.html';
            $config['first_url'] = base_url() . 'company_emails/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Company_emails_model->total_rows($q);
        $company_emails = $this->Company_emails_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'company_emails_data' => $company_emails,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('company_emails/company_emails_list', $data);
    }

    public function manage($id)
    {
//    	$id = $this->encryption->decrypt($idd);
        $row = $this->Company_emails_model->get_by_id($id);
        if ($row) {
            $data = array(
		'email_id' => $row->email_id,
		'email' => $row->email,
		'domain' => $row->domain,
		'company' => $row->company,
		'suspension' => $row->suspension,
		'outgoing' => $row->outgoing,
		'login' => $row->login,
	    );
			$config['active_menu'] = "emails";
			$config['current_link'] = "emails";
			$this->load->view('header',$config);
            $this->load->view('company_emails/company_emails_read', $data);
			$this->load->view('footer');
        } else {
			$this->toaster->error('Error , record not found');
            redirect(site_url('my_emails'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('company_emails/create_action'),
	    'email_id' => set_value('email_id'),
	    'email' => set_value('email'),
	    'domain' => set_value('domain'),
	    'company' => set_value('company'),
	    'email_stamp' => set_value('email_stamp'),
	);
		$config['active_menu'] = "emails";
		$config['current_link'] = "email_create";
		$this->load->view('header',$config);
		$this->load->view('email_form',$data);
		$this->load->view('footer');
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'email' => $this->input->post('email',TRUE),
		'domain' => $this->input->post('domain',TRUE),
		'company' => $this->session->userdata('company_id'),

	    );
			$response = $this->cpanel->uapi->Email->add_pop( array (
				'email' => $data['email'],
				'domain' => $data['domain'],
				'password' => $this->input->post('pass2'),
				'quota' => 'unlimited',
				'send_welcome_email' => '1',

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->insert($data);
				$this->toaster->success('Success !, email  was added successfully');
				redirect(site_url('my_emails'));
			}

        }
    }

    public function update_settings(){
    	$email = $this->input->post('email');
    	$id = $this->input->post('id');
    	$domain = $this->input->post('domain');
    	$pass = $this->input->post('newpass');
    	$iem = $this->input->post('incoming_email');
    	$outgoing = $this->input->post('outgoing');
    	$login = $this->input->post('login');
    	if($email !="" && $pass !=""){
			$response = $this->cpanel->uapi->Email->passwd_pop( array (
				'email' => $email,
				'domain' => $domain,
				'password' => $pass
			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}

		}
    	if($email !="" && $iem =="disallow_incoming_email"){
			$response = $this->cpanel->uapi->Email->suspend_incoming( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('suspension'=>$iem));
			}

		}elseif($email !="" && $iem =="allow_incoming_email"){
			$response = $this->cpanel->uapi->Email->unsuspend_incoming ( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('suspension'=>$iem));
			}
		}

    	//outgoin messages
		if($email !="" && $outgoing =="suspend"){
			$response = $this->cpanel->uapi->Email->suspend_outgoing( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('outgoing'=>$outgoing));
			}

		}elseif($email !="" && $outgoing =="allow"){
			$response = $this->cpanel->uapi->Email->unsuspend_outgoing( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('outgoing'=>$outgoing));
			}
		}elseif($email !="" && $outgoing =="hold"){
			$response = $this->cpanel->uapi->Email->hold_outgoing( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('outgoing'=>$outgoing));
			}
		}
		//login

		if($email !="" && $login =="suspend"){
			$response = $this->cpanel->uapi->Email->suspend_login( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('login'=>$login));
			}

		}elseif($email !="" && $login =="allow"){
			$response = $this->cpanel->uapi->Email->unsuspend_login( array (
				'email' => $email."@".$domain

			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->update($id,array('login'=>$login));
			}
		}
			$this->toaster->success('Success !, all changes to'.$email.' are saved');
			redirect(site_url('my_emails'));

	}
    public function delete($id)
    {
//		$id = $this->encryption->decrypt($idd);
        $row = $this->Company_emails_model->get_by_id2($id);

        if ($row) {
            $this->Company_emails_model->delete($id);
			$response = $this->cpanel->uapi->Email->delete_pop( array (
				'email' => "".$row->email."@".$row->domain.""
			));
			if ($response->status == 0){

				$this->session->set_flashdata('error',$response->errors);
				redirect($_SERVER["HTTP_REFERER"]);
			}else{
				$this->Company_emails_model->delete($id);
				$this->toaster->success('Success !, email  was deleted successfully');
				redirect(site_url('my_emails'));
			}

        } else {
			$this->toaster->error('Error !, no record found email  was not deleted ');
			redirect(site_url('my_emails'));
        }
    }
function webmail($domain){


$surl = "https://".$domain.":2096";

redirect($surl);




}
function get_my_emails($domain){
        $res = '';
        $data = $this->Company_emails_model->get_by_domain($domain);
        $res .='<option value="">--select-</option>';
        foreach ($data as $d){
            $res .='<option value="'.$d->email.'@'.$d->domain.'">'.$d->email.'@'.$d->domain.'</option>';
        }
        echo $res;
}
    public function _rules()
    {
	$this->form_validation->set_rules('email', 'email', 'trim|required');
	$this->form_validation->set_rules('domain', 'domain', 'trim|required');



	$this->form_validation->set_rules('email_id', 'email_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}


