<?php
$domains = $this->Company_domains_model->get_my_domains($this->session->userdata('company_id'));
$company = $this->Company_model->company_details($this->session->userdata('company_id'));

?>
<div class="content-wrapper">

    <!-- Row start -->
    <div class="row gutters">
        <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">

            <div class="card">
                <div class="card-body">
        <h2 style="margin-top:0px">Email forwarder <?php echo $button ?></h2>
        <form action="<?php echo $action; ?>" method="post">
            <?php
            if($this->session->flashdata('error')){
                ?>
                <div class="alert alert-danger" role="alert">
                    <ul>

                        <?php
                        foreach ($this->session->flashdata('error') as $err=>$value){
                            echo "<li>-";
                            echo $value;
                            echo "</li>";
                        }
                        ?>
                    </ul>
                </div>
                <?php
            }
            ?>
            <div class="row">
                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

                    <div class="field-wrapper">
                        <label for="varchar">Email forwarder domain <?php echo form_error('domain') ?></label>
                        <select class="select-single js-states" name="domain"  id="domain" title="Select Domain" required>
                            <option value="">--select domain--</option>
                            <?php
                            foreach ($domains as $d){
                                ?>
                                <option value="<?php echo $d->domain_name ?>"><?php echo $d->domain_name ?></option>
                                <?php
                            }
                            ?>
                        </select>

                    </div>
                </div>


                <div class="col-xl-6 col-lg-6 col-md-6 col-sm-12 col-12">

                    <div class="field-wrapper">
                        <label for="varchar">Select Forwarder email (<EMAIL>)<?php echo form_error('forwarder') ?></label>
                        <select class="select-single js-states" name="forwarder"  id="forwarder" title="Select email" required>
                            <option value="">--select email--</option>

                        </select>

                    </div>
                </div>


            </div>


	    <div class="form-group">
            <label for="forwarded_to">Forwarded To email (you can add more emails separate by comma) :(<EMAIL> )<?php echo form_error('forwarded_to') ?></label>
            <textarea class="form-control" rows="3" name="forwarded_to" id="forwarded_to" placeholder="Forwarded To"><?php echo $forwarded_to; ?></textarea>
        </div>

	    <input type="hidden" name="email_forwarder_id" value="<?php echo $email_forwarder_id; ?>" /> 
	    <button type="submit" class="btn btn-primary"><?php echo $button ?></button> 

	</form>
                </div>
            </div>
        </div>
    </div>
</div>
