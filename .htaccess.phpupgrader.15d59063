Options +FollowSymLinks
Options -Indexes

<FilesMatch "(?i)((\.tpl|\.ini|\.log|(?<!robots)\.txt))">
    Order deny,allow
    Deny from all
</FilesMatch>
     RewriteEngine On
     RewriteCond %{HTTPS} off
     RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
DirectoryIndex index.php
RewriteEngine on
RewriteCond $1 !^(index\.php|images|robots\.txt)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/$1 [L,QSA]