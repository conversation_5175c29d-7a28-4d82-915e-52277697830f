<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - EmailHost-Plus Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #6366f1;
            --primary-dark: #4f46e5;
            --cpanel-orange: #ff6c2c;
        }
        
        .sidebar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            min-height: 100vh;
            color: white;
        }
        
        .sidebar .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1.5rem;
            border-radius: 0.5rem;
            margin: 0.25rem 0;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        .main-content {
            background: #f8fafc;
            min-height: 100vh;
        }
        
        .card {
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            border-radius: 1rem;
        }
        
        .btn-primary {
            background: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background: var(--primary-dark);
            border-color: var(--primary-dark);
        }
        
        .badge-published { background: #10b981; }
        .badge-draft { background: #f59e0b; }
        .badge-archived { background: #6b7280; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 sidebar p-0">
                <div class="p-4">
                    <h4 class="text-white mb-4">
                        <i class="fas fa-blog me-2"></i>Blog Admin
                    </h4>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="<?php echo base_url('admin/blog_admin'); ?>">
                            <i class="fas fa-list me-2"></i>All Posts
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/create'); ?>">
                            <i class="fas fa-plus me-2"></i>Add New Post
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/categories'); ?>">
                            <i class="fas fa-folder me-2"></i>Categories
                        </a>
                        <a class="nav-link" href="<?php echo base_url('admin/blog_admin/comments'); ?>">
                            <i class="fas fa-comments me-2"></i>Comments
                        </a>
                        <hr class="my-3" style="border-color: rgba(255,255,255,0.2);">
                        <a class="nav-link" href="<?php echo base_url('blog'); ?>" target="_blank">
                            <i class="fas fa-external-link-alt me-2"></i>View Blog
                        </a>
                        <a class="nav-link" href="<?php echo base_url(); ?>">
                            <i class="fas fa-home me-2"></i>Back to Site
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 main-content">
                <div class="p-4">
                    <!-- Header -->
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2><?php echo $page_title; ?></h2>
                        <a href="<?php echo base_url('admin/blog_admin/create'); ?>" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Add New Post
                        </a>
                    </div>

                    <!-- Flash Messages -->
                    <?php if ($this->session->flashdata('success')): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('success'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($this->session->flashdata('error')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $this->session->flashdata('error'); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <!-- Posts Table -->
                    <div class="card">
                        <div class="card-body">
                            <?php if (!empty($posts)): ?>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Title</th>
                                                <th>Author</th>
                                                <th>Category</th>
                                                <th>Status</th>
                                                <th>Views</th>
                                                <th>Date</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($posts as $post): ?>
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <?php if ($post->featured_image): ?>
                                                                <img src="<?php echo base_url('uploads/blog/thumbs/' . $post->featured_image); ?>" 
                                                                     alt="" class="rounded me-3" style="width: 50px; height: 50px; object-fit: cover;">
                                                            <?php else: ?>
                                                                <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                                     style="width: 50px; height: 50px;">
                                                                    <i class="fas fa-image text-muted"></i>
                                                                </div>
                                                            <?php endif; ?>
                                                            <div>
                                                                <h6 class="mb-0"><?php echo htmlspecialchars($post->title); ?></h6>
                                                                <small class="text-muted"><?php echo $post->slug; ?></small>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td><?php echo htmlspecialchars($post->author_name ?: 'Unknown'); ?></td>
                                                    <td>
                                                        <?php if ($post->category_name): ?>
                                                            <span class="badge bg-secondary"><?php echo htmlspecialchars($post->category_name); ?></span>
                                                        <?php else: ?>
                                                            <span class="text-muted">Uncategorized</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <span class="badge badge-<?php echo $post->status; ?>">
                                                            <?php echo ucfirst($post->status); ?>
                                                        </span>
                                                    </td>
                                                    <td>
                                                        <i class="fas fa-eye text-muted me-1"></i>
                                                        <?php echo number_format($post->views); ?>
                                                    </td>
                                                    <td>
                                                        <small>
                                                            <?php echo date('M j, Y', strtotime($post->created_at)); ?><br>
                                                            <span class="text-muted"><?php echo date('g:i A', strtotime($post->created_at)); ?></span>
                                                        </small>
                                                    </td>
                                                    <td>
                                                        <div class="btn-group btn-group-sm">
                                                            <?php if ($post->status === 'published'): ?>
                                                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" 
                                                                   class="btn btn-outline-primary" target="_blank" title="View">
                                                                    <i class="fas fa-eye"></i>
                                                                </a>
                                                            <?php endif; ?>
                                                            <a href="<?php echo base_url('admin/blog_admin/edit/' . $post->id); ?>" 
                                                               class="btn btn-outline-secondary" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                            <button type="button" class="btn btn-outline-danger" 
                                                                    onclick="deletePost(<?php echo $post->id; ?>)" title="Delete">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-blog fa-3x text-muted mb-3"></i>
                                    <h5>No blog posts yet</h5>
                                    <p class="text-muted">Create your first blog post to get started.</p>
                                    <a href="<?php echo base_url('admin/blog_admin/create'); ?>" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Create First Post
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function deletePost(postId) {
            if (confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
                window.location.href = '<?php echo base_url("admin/blog_admin/delete/"); ?>' + postId;
            }
        }
    </script>
</body>
</html>
