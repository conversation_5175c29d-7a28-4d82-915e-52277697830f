    </main>

    <?php
    // Ensure settings are available
    if (!isset($settings)) {
        $settings = get_by_id('cpanel_settings','id','1');
    }
    ?>

    <!-- Footer -->
    <footer class="landing-footer">
        <div class="container">
            <div class="row footer-content">
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5><?php echo isset($settings->company_name) ? $settings->company_name : 'Professional Hosting'; ?></h5>
                        <p class="mb-3">Professional email hosting and web services provider. We help businesses establish their online presence with reliable, secure, and affordable solutions.</p>
                        <div class="social-links">
                            <a href="#" class="text-white me-3"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="text-white me-3"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="text-white me-3"><i class="fab fa-linkedin-in"></i></a>
                            <a href="#" class="text-white"><i class="fab fa-instagram"></i></a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5>Services</h5>
                        <ul>
                            <li><a href="<?php echo base_url('pricing'); ?>">Email Hosting</a></li>
                            <li><a href="<?php echo base_url('pricing'); ?>">Domain Registration</a></li>
                            <li><a href="<?php echo base_url('pricing'); ?>">Web Hosting</a></li>
                            <li><a href="<?php echo base_url('pricing'); ?>">Business Email</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5>Company</h5>
                        <ul>
                            <li><a href="<?php echo base_url('about'); ?>">About Us</a></li>
                            <li><a href="<?php echo base_url('contact'); ?>">Contact</a></li>
                            <li><a href="<?php echo base_url('pricing'); ?>">Pricing</a></li>
                            <li><a href="#">Support</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5>Legal</h5>
                        <ul>
                            <li><a href="#">Privacy Policy</a></li>
                            <li><a href="#">Terms of Service</a></li>
                            <li><a href="#">Refund Policy</a></li>
                            <li><a href="#">SLA</a></li>
                        </ul>
                    </div>
                </div>

                <div class="col-lg-2 col-md-6 mb-4">
                    <div class="footer-section">
                        <h5>Contact Info</h5>
                        <ul>
                            <li><i class="fas fa-envelope me-2"></i><?php echo isset($settings->company_email) ? $settings->company_email : '<EMAIL>'; ?></li>
                            <li><i class="fas fa-phone me-2"></i>+265 XXX XXX XXX</li>
                            <li><i class="fas fa-map-marker-alt me-2"></i>Lilongwe, Malawi</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <p>&copy; <?php echo date('Y'); ?> <?php echo isset($settings->company_name) ? $settings->company_name : 'Professional Hosting'; ?>. All rights reserved.</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p>Powered by professional hosting solutions</p>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script>
        // Set base URL for JavaScript
        const base_url = '<?php echo base_url(); ?>';
    </script>
    <script src="<?php echo base_url('assets/js/landing.js'); ?>"></script>

    <!-- Additional JS for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Page-specific JavaScript -->
    <?php if (isset($page_js)): ?>
        <script>
            <?php echo $page_js; ?>
        </script>
    <?php endif; ?>
</body>
</html>
