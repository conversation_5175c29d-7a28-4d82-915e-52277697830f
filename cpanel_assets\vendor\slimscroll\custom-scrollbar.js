// Content Wrapper
// $(function() {
// 	$('.content-wrapper').slimScroll({
// 		height: "calc(100vh - 150px)",
// 		color: '#adb4c5',
// 		alwaysVisible: false,
// 		size: "7px",
// 		distance: '2px',
// 		railVisible: false,
// 		railColor: "#adb4c5",
// 	});
// });


// Custom Scroll Fixed Height 200px
$(function() {
	$('.sidebarMenuScroll').slimScroll({
		height: "calc(100vh - 250px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});



// Default Sidebar Scroll
$(function() {
	$('.defaultSidebarMenuScroll').slimScroll({
		height: "calc(100vh - 140px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});



// Compact Sidebar Scroll
$(function() {
	$('.compactSidebarMenuScroll').slimScroll({
		height: "calc(100vh - 140px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll for Chat Page
$(function() {
	$('.usersContainerScroll').slimScroll({
		height: "calc(100vh - 240px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '0px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll Fixed Height 200px
$(function() {
	$('.megamenu-scroll').slimScroll({
		height: "calc(100vh - 100px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


$(function() {
	$('.chatContainerScroll').slimScroll({
		height: "calc(100vh - 370px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '0px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Tasks App Page
$(function() {
	$('.lablesContainerScroll').slimScroll({
		height: "calc(100vh - 200px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});
$(function() {
	$('.tasksContainerScroll').slimScroll({
		height: "calc(100vh - 250px)",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll Fixed Height 210px
$(function() {
	$('.customScroll').slimScroll({
		height: "210px",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll Fixed Height 250px
$(function() {
	$('.customScroll250').slimScroll({
		height: "250px",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll Fixed Height 150px
$(function() {
	$('.customScroll150').slimScroll({
		height: "150px",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll Fixed Height 300px
$(function() {
	$('.customScroll300').slimScroll({
		height: "300px",
		color: '#d6dbe6',
		alwaysVisible: false,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});


// Custom Scroll for Modal Dailog
$(function() {
	$('.modal-body').slimScroll({
		height: "95%",
		color: '#d6dbe6',
		alwaysVisible: true,
		size: "5px",
		distance: '1px',
		railVisible: false,
		railColor: "#d6dbe6",
	});
});