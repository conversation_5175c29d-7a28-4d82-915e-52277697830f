<?php


class General_c extends CI_Controller
{
public function __construct()
{
	parent::__construct();
}
public function copy(){
	$this->copydir("../wordpresscore","../newsite");
	$this->zipopen();


}

public function zipopen(){
	$zip = new ZipArchive;
	$res = $zip->open("../newsite/wordpress.zip");
	if ($res === TRUE) {

		// Unzip path
		$extractpath = "../newsite";

		// Extract file
		$zip->extractTo($extractpath);
		$zip->close();

		$this->session->set_flashdata('msg','Upload & Extract successfully.');
	} else {
		$this->session->set_flashdata('msg','Failed to extract.');
	}
}

	public  function copydir($source,$destination)
	{
		if(!is_dir($destination)){
			$oldumask = umask(0);
			mkdir($destination, 01777); // so you get the sticky bit set
			umask($oldumask);
		}
		$dir_handle = @opendir($source) or die("Unable to open");
		while ($file = readdir($dir_handle))
		{
			if($file!="." && $file!=".." && !is_dir("$source/$file"))
				copy("$source/$file","$destination/$file");
		}
		closedir($dir_handle);
	}
}
