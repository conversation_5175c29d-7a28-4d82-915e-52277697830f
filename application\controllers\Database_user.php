<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Database_user extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Database_user_model');
        $this->load->library('form_validation');
    }

    public function index()
    {
        $q = urldecode($this->input->get('q', TRUE));
        $start = intval($this->input->get('start'));
        
        if ($q <> '') {
            $config['base_url'] = base_url() . 'database_user/index.html?q=' . urlencode($q);
            $config['first_url'] = base_url() . 'database_user/index.html?q=' . urlencode($q);
        } else {
            $config['base_url'] = base_url() . 'database_user/index.html';
            $config['first_url'] = base_url() . 'database_user/index.html';
        }

        $config['per_page'] = 10;
        $config['page_query_string'] = TRUE;
        $config['total_rows'] = $this->Database_user_model->total_rows($q);
        $database_user = $this->Database_user_model->get_limit_data($config['per_page'], $start, $q);

        $this->load->library('pagination');
        $this->pagination->initialize($config);

        $data = array(
            'database_user_data' => $database_user,
            'q' => $q,
            'pagination' => $this->pagination->create_links(),
            'total_rows' => $config['total_rows'],
            'start' => $start,
        );
        $this->load->view('database_user/database_user_list', $data);
    }

    public function read($id) 
    {
        $row = $this->Database_user_model->get_by_id($id);
        if ($row) {
            $data = array(
		'database_user' => $row->database_user,
		'company_id' => $row->company_id,
		'username' => $row->username,
		'date_added' => $row->date_added,
	    );
            $this->load->view('database_user/database_user_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('database_user'));
        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('database_user/create_action'),
	    'database_user' => set_value('database_user'),
	    'company_id' => set_value('company_id'),
	    'username' => set_value('username'),
	    'date_added' => set_value('date_added'),
	);
        $this->load->view('database_user/database_user_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'company_id' => $this->input->post('company_id',TRUE),
		'username' => $this->input->post('username',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Database_user_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('database_user'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Database_user_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('database_user/update_action'),
		'database_user' => set_value('database_user', $row->database_user),
		'company_id' => set_value('company_id', $row->company_id),
		'username' => set_value('username', $row->username),
		'date_added' => set_value('date_added', $row->date_added),
	    );
            $this->load->view('database_user/database_user_form', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('database_user'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('database_user', TRUE));
        } else {
            $data = array(
		'company_id' => $this->input->post('company_id',TRUE),
		'username' => $this->input->post('username',TRUE),
		'date_added' => $this->input->post('date_added',TRUE),
	    );

            $this->Database_user_model->update($this->input->post('database_user', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('database_user'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Database_user_model->get_by_id($id);

        if ($row) {
            $this->Database_user_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('database_user'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('database_user'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('company_id', 'company id', 'trim|required');
	$this->form_validation->set_rules('username', 'username', 'trim|required');
	$this->form_validation->set_rules('date_added', 'date added', 'trim|required');

	$this->form_validation->set_rules('database_user', 'database_user', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

/* End of file Database_user.php */
/* Location: ./application/controllers/Database_user.php */
/* Please DO NOT modify this information : */
/* Generated by Harviacode Codeigniter CRUD Generator 2021-09-21 06:30:37 */
/* http://harviacode.com */