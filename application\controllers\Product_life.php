<?php

if (!defined('BASEPATH'))
    exit('No direct script access allowed');

class Product_life extends CI_Controller
{
    function __construct()
    {
        parent::__construct();
        $this->load->model('Product_life_model');
        $this->load->library('form_validation');
    }

    public function index()
    {


        $data = array(
            'product_life_data' => $this->Product_life_model->get_all(),
        );

        $config['active_menu'] = "server_config";
        $config['current_link'] = "product_life";
        $this->load->view('header',$config);
        $this->load->view('product_life/product_life_list', $data);
        $this->load->view('footer');

    }

    public function read($id) 
    {
        $row = $this->Product_life_model->get_by_id($id);
        if ($row) {
            $data = array(
		'product_life_id' => $row->product_life_id,
		'product_type' => $row->product_type,
		'product_id' => $row->product_id,
		'product_name' => $row->product_name,
		'product_price' => $row->product_price,
		'expire_date' => $row->expire_date,
		'product_owner' => $row->product_owner,
		'updated_date' => $row->updated_date,
	    );
            $this->load->view('product_life/product_life_read', $data);
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_life'));
        }
    }
    public function get_d($id)
    {
        $row = $this->Product_life_model->get_by_id($id);
        if ($row) {
            $data = array(
		'product_life_id' => $row->product_life_id,
		'product_type' => $row->product_type,
		'product_id' => $row->product_id,
		'product_name' => $row->product_name,
		'product_price' => $row->product_price,
		'expire_date' => $row->expire_date,
		'product_owner' => $row->product_owner,
		'updated_date' => $row->updated_date,
	    );

            echo $row->expire_date;
        } else {

        }
    }

    public function create() 
    {
        $data = array(
            'button' => 'Create',
            'action' => site_url('product_life/create_action'),
	    'product_life_id' => set_value('product_life_id'),
	    'product_type' => set_value('product_type'),
	    'product_id' => set_value('product_id'),
	    'product_name' => set_value('product_name'),
	    'product_price' => set_value('product_price'),
	    'expire_date' => set_value('expire_date'),
	    'product_owner' => set_value('product_owner'),
	    'updated_date' => set_value('updated_date'),
	);
        $this->load->view('product_life/product_life_form', $data);
    }
    
    public function create_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->create();
        } else {
            $data = array(
		'product_type' => $this->input->post('product_type',TRUE),
		'product_id' => $this->input->post('product_id',TRUE),
		'product_name' => $this->input->post('product_name',TRUE),
		'product_price' => $this->input->post('product_price',TRUE),
		'expire_date' => $this->input->post('expire_date',TRUE),
		'product_owner' => $this->input->post('product_owner',TRUE),
		'updated_date' => $this->input->post('updated_date',TRUE),
	    );

            $this->Product_life_model->insert($data);
            $this->session->set_flashdata('message', 'Create Record Success');
            redirect(site_url('product_life'));
        }
    }
    
    public function update($id) 
    {
        $row = $this->Product_life_model->get_by_id($id);

        if ($row) {
            $data = array(
                'button' => 'Update',
                'action' => site_url('product_life/update_action'),
		'product_life_id' => set_value('product_life_id', $row->product_life_id),
		'product_type' => set_value('product_type', $row->product_type),
		'product_id' => set_value('product_id', $row->product_id),
		'product_name' => set_value('product_name', $row->product_name),
		'product_price' => set_value('product_price', $row->product_price),
		'expire_date' => set_value('expire_date', $row->expire_date),
		'product_owner' => set_value('product_owner', $row->product_owner),
		'updated_date' => set_value('updated_date', $row->updated_date),
	    );
            $config['active_menu'] = "server_config";
            $config['current_link'] = "product_life";
            $this->load->view('header',$config);
            $this->load->view('product_life/product_life_form', $data);
            $this->load->view('footer');
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_life'));
        }
    }
    
    public function update_action() 
    {
        $this->_rules();

        if ($this->form_validation->run() == FALSE) {
            $this->update($this->input->post('product_life_id', TRUE));
        } else {
            $data = array(
		'product_type' => $this->input->post('product_type',TRUE),
		'product_id' => $this->input->post('product_id',TRUE),
		'product_name' => $this->input->post('product_name',TRUE),
		'product_price' => $this->input->post('product_price',TRUE),
		'expire_date' => $this->input->post('expire_date',TRUE),
		'product_owner' => $this->input->post('product_owner',TRUE),
		'updated_date' => $this->input->post('updated_date',TRUE),
	    );

            $this->Product_life_model->update($this->input->post('product_life_id', TRUE), $data);
            $this->session->set_flashdata('message', 'Update Record Success');
            redirect(site_url('product_life'));
        }
    }
    
    public function delete($id) 
    {
        $row = $this->Product_life_model->get_by_id($id);

        if ($row) {
            $this->Product_life_model->delete($id);
            $this->session->set_flashdata('message', 'Delete Record Success');
            redirect(site_url('product_life'));
        } else {
            $this->session->set_flashdata('message', 'Record Not Found');
            redirect(site_url('product_life'));
        }
    }

    public function _rules() 
    {
	$this->form_validation->set_rules('product_type', 'product type', 'trim|required');
	$this->form_validation->set_rules('product_id', 'product id', 'trim|required');


	$this->form_validation->set_rules('product_life_id', 'product_life_id', 'trim');
	$this->form_validation->set_error_delimiters('<span class="text-danger">', '</span>');
    }

}

