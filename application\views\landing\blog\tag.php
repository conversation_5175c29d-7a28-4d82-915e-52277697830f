<?php $this->load->view('landing/header'); ?>

<!-- Blog Tag -->
<section class="blog-hero">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?php echo base_url(); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo base_url('blog'); ?>">Blog</a></li>
                        <li class="breadcrumb-item active">Tag: <?php echo htmlspecialchars($tag_name); ?></li>
                    </ol>
                </nav>
                <h1 class="hero-title">
                    <i class="fas fa-tag me-2"></i>
                    <?php echo htmlspecialchars($tag_name); ?>
                </h1>
                <p class="hero-subtitle">
                    <?php if (!empty($posts)): ?>
                        <?php echo count($posts); ?> post(s) tagged with "<?php echo htmlspecialchars($tag_name); ?>"
                    <?php else: ?>
                        No posts found with this tag
                    <?php endif; ?>
                </p>
            </div>
        </div>
    </div>
</section>

<section class="blog-content">
    <div class="container">
        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <?php if (!empty($posts)): ?>
                    <div class="row">
                        <?php foreach ($posts as $post): ?>
                            <div class="col-md-6 mb-4">
                                <article class="blog-card">
                                    <?php if ($post->featured_image): ?>
                                        <div class="blog-card-image">
                                            <img src="<?php echo base_url('uploads/blog/' . $post->featured_image); ?>" 
                                                 alt="<?php echo htmlspecialchars($post->title); ?>" 
                                                 class="img-fluid">
                                            <div class="blog-card-overlay">
                                                <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more-btn">
                                                    <i class="fas fa-arrow-right"></i>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="blog-card-content">
                                        <div class="blog-meta">
                                            <span class="blog-date">
                                                <i class="fas fa-calendar"></i>
                                                <?php echo date('M j, Y', strtotime($post->published_at)); ?>
                                            </span>
                                            <?php if ($post->category_name): ?>
                                                <a href="<?php echo base_url('blog/category/' . $post->category_slug); ?>" class="blog-category">
                                                    <i class="fas fa-folder"></i>
                                                    <?php echo htmlspecialchars($post->category_name); ?>
                                                </a>
                                            <?php endif; ?>
                                            <span class="blog-author">
                                                <i class="fas fa-user"></i>
                                                <?php echo htmlspecialchars($post->author_name); ?>
                                            </span>
                                        </div>
                                        
                                        <h3 class="blog-card-title">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>">
                                                <?php echo htmlspecialchars($post->title); ?>
                                            </a>
                                        </h3>
                                        
                                        <p class="blog-card-excerpt">
                                            <?php 
                                            $excerpt = $post->excerpt ?: strip_tags($post->content);
                                            echo htmlspecialchars(word_limiter($excerpt, 20));
                                            ?>
                                        </p>
                                        
                                        <div class="blog-card-footer">
                                            <a href="<?php echo base_url('blog/post/' . $post->slug); ?>" class="read-more">
                                                Read More <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                            <div class="blog-stats">
                                                <span><i class="fas fa-eye"></i> <?php echo number_format($post->views); ?></span>
                                            </div>
                                        </div>
                                    </div>
                                </article>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="no-posts">
                        <div class="text-center py-5">
                            <i class="fas fa-tag fa-3x text-muted mb-3"></i>
                            <h4>No Posts with This Tag</h4>
                            <p class="text-muted mb-4">
                                There are no published posts tagged with "<?php echo htmlspecialchars($tag_name); ?>" yet.
                            </p>
                            <a href="<?php echo base_url('blog'); ?>" class="btn btn-primary">
                                <i class="fas fa-arrow-left me-2"></i>Back to All Posts
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <div class="blog-sidebar">
                    <!-- Search Widget -->
                    <div class="sidebar-widget">
                        <h5 class="widget-title">Search Posts</h5>
                        <form action="<?php echo base_url('blog/search'); ?>" method="get" class="search-form">
                            <div class="input-group">
                                <input type="text" name="q" class="form-control" 
                                       placeholder="Search posts..." required>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Categories Widget -->
                    <?php if (!empty($categories)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Categories</h5>
                            <ul class="category-list">
                                <?php foreach ($categories as $category): ?>
                                    <li>
                                        <a href="<?php echo base_url('blog/category/' . $category->slug); ?>">
                                            <?php echo htmlspecialchars($category->name); ?>
                                            <span class="post-count">(<?php echo $category->post_count; ?>)</span>
                                        </a>
                                    </li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                    <?php endif; ?>

                    <!-- Recent Posts Widget -->
                    <?php if (!empty($recent_posts)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Recent Posts</h5>
                            <div class="recent-posts">
                                <?php foreach ($recent_posts as $recent_post): ?>
                                    <div class="recent-post-item">
                                        <?php if ($recent_post->featured_image): ?>
                                            <div class="recent-post-image">
                                                <img src="<?php echo base_url('uploads/blog/thumbs/' . $recent_post->featured_image); ?>" 
                                                     alt="<?php echo htmlspecialchars($recent_post->title); ?>">
                                            </div>
                                        <?php endif; ?>
                                        <div class="recent-post-content">
                                            <h6>
                                                <a href="<?php echo base_url('blog/post/' . $recent_post->slug); ?>">
                                                    <?php echo htmlspecialchars($recent_post->title); ?>
                                                </a>
                                            </h6>
                                            <small class="text-muted">
                                                <?php echo date('M j, Y', strtotime($recent_post->published_at)); ?>
                                            </small>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Popular Tags Widget -->
                    <?php if (!empty($popular_tags)): ?>
                        <div class="sidebar-widget">
                            <h5 class="widget-title">Popular Tags</h5>
                            <div class="tag-cloud">
                                <?php foreach ($popular_tags as $tag): ?>
                                    <a href="<?php echo base_url('blog/tag/' . $tag->slug); ?>" 
                                       class="tag-item <?php echo ($tag->slug == $tag_slug) ? 'active' : ''; ?>">
                                        <?php echo htmlspecialchars($tag->name); ?>
                                    </a>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Related Tags Widget -->
                    <div class="sidebar-widget">
                        <h5 class="widget-title">Explore More Tags</h5>
                        <p class="text-muted mb-3">Discover content by browsing related topics</p>
                        <div class="tag-suggestions">
                            <a href="<?php echo base_url('blog/tag/email-hosting'); ?>" class="tag-item">Email Hosting</a>
                            <a href="<?php echo base_url('blog/tag/web-development'); ?>" class="tag-item">Web Development</a>
                            <a href="<?php echo base_url('blog/tag/business-tips'); ?>" class="tag-item">Business Tips</a>
                            <a href="<?php echo base_url('blog/tag/tutorials'); ?>" class="tag-item">Tutorials</a>
                            <a href="<?php echo base_url('blog/tag/security'); ?>" class="tag-item">Security</a>
                            <a href="<?php echo base_url('blog/tag/performance'); ?>" class="tag-item">Performance</a>
                        </div>
                    </div>

                    <!-- Back to Blog Widget -->
                    <div class="sidebar-widget back-to-blog">
                        <div class="text-center">
                            <h5 class="widget-title">Explore All Posts</h5>
                            <p class="text-muted mb-3">Browse our complete collection of articles</p>
                            <a href="<?php echo base_url('blog'); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-th-large me-2"></i>View All Posts
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
.no-posts {
    background: white;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.tag-item.active {
    background: var(--primary-color);
    color: white;
}

.tag-suggestions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.back-to-blog {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
}

.back-to-blog .widget-title {
    color: var(--text-dark);
}

.hero-title i {
    color: var(--primary-color);
}
</style>

<?php $this->load->view('landing/footer'); ?>
